<?php
/**
 * Skrypt do testowania wydajności WordPress
 * Firmup.eu - Test po optymalizacji
 * Data: 2025-01-23
 */

echo "=== TEST WYDAJNOŚCI FIRMUP.EU ===\n";
echo "Data: " . date('Y-m-d H:i:s') . "\n\n";

// Test 1: Sprawdź czy debug.log został usunięty
echo "1. SPRAWDZANIE PLIKU DEBUG.LOG...\n";
if (file_exists('wp-content/debug.log')) {
    $size = filesize('wp-content/debug.log');
    echo "   ❌ Plik debug.log nadal istnieje (rozmiar: " . round($size/1024, 2) . " KB)\n";
} else {
    echo "   ✅ Plik debug.log został usunięty\n";
}

// Test 2: Sprawdź pliki tymczasowe
echo "\n2. SPRAWDZANIE PLIKÓW TYMCZASOWYCH...\n";
$temp_files = glob('wp-content/temp-write-test-*');
if (count($temp_files) > 0) {
    echo "   ❌ Znaleziono " . count($temp_files) . " plików tymczasowych\n";
} else {
    echo "   ✅ Brak plików tymczasowych\n";
}

// Test 3: Sprawdź konfigurację wp-config.php
echo "\n3. SPRAWDZANIE KONFIGURACJI WP-CONFIG.PHP...\n";
$config_content = file_get_contents('wp-config.php');

if (strpos($config_content, "define( 'WP_DEBUG', false )") !== false) {
    echo "   ✅ WP_DEBUG wyłączony\n";
} else {
    echo "   ❌ WP_DEBUG może być włączony\n";
}

if (strpos($config_content, "define('WP_MEMORY_LIMIT', '512M')") !== false) {
    echo "   ✅ Limit pamięci ustawiony na 512M\n";
} else {
    echo "   ❌ Limit pamięci nie jest zoptymalizowany\n";
}

if (strpos($config_content, "define('DISABLE_WP_CRON', true)") !== false) {
    echo "   ✅ WP-CRON wyłączony\n";
} else {
    echo "   ❌ WP-CRON może spowalniać stronę\n";
}

// Test 4: Sprawdź rozmiar cache
echo "\n4. SPRAWDZANIE CACHE...\n";
if (is_dir('wp-content/cache')) {
    $cache_size = 0;
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator('wp-content/cache'));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $cache_size += $file->getSize();
        }
    }
    echo "   📊 Rozmiar cache: " . round($cache_size/1024/1024, 2) . " MB\n";
} else {
    echo "   ❌ Folder cache nie istnieje\n";
}

// Test 5: Sprawdź mu-plugins
echo "\n5. SPRAWDZANIE MU-PLUGINS...\n";
$error_monitor = file_get_contents('wp-content/mu-plugins/error-monitor.php');
if (strpos($error_monitor, 'WYŁĄCZONE W PRODUKCJI') !== false) {
    echo "   ✅ Error monitor wyłączony\n";
} else {
    echo "   ❌ Error monitor może generować błędy\n";
}

// Test 6: Sprawdź optymalizacje w functions.php
echo "\n6. SPRAWDZANIE OPTYMALIZACJI W MOTYWIE...\n";
$functions_content = file_get_contents('wp-content/themes/oceanwp/functions.php');
if (strpos($functions_content, 'OPTYMALIZACJE WYDAJNOŚCI') !== false) {
    echo "   ✅ Optymalizacje heartbeat dodane\n";
} else {
    echo "   ❌ Brak optymalizacji heartbeat\n";
}

// Test 7: Sprawdź naprawione pluginy
echo "\n7. SPRAWDZANIE NAPRAWIONYCH PLUGINÓW...\n";
$yt_player = file_get_contents('wp-content/plugins/yt-player/inc/Services/Shortcode.php');
if (strpos($yt_player, "isset(\$option['hideYoutubeUI'])") !== false) {
    echo "   ✅ YT Player naprawiony\n";
} else {
    echo "   ❌ YT Player może generować błędy\n";
}

$tracking_code = file_get_contents('wp-content/plugins/tracking-code-manager/assets/js/plugin.js');
if (strpos($tracking_code, 'setTimeout') !== false) {
    echo "   ✅ Tracking Code Manager zoptymalizowany\n";
} else {
    echo "   ❌ Tracking Code Manager może mieć nieskończone pętle\n";
}

// Test 8: Rekomendacje
echo "\n=== REKOMENDACJE ===\n";
echo "1. Sprawdź działanie strony w przeglądarce\n";
echo "2. Przetestuj panel wp-admin\n";
echo "3. Sprawdź czy nie ma nowych błędów w debug.log (jeśli się pojawi)\n";
echo "4. Monitoruj wydajność przez 24h\n";
echo "5. Uruchom test szybkości (GTmetrix, PageSpeed Insights)\n";

echo "\n=== NASTĘPNE KROKI ===\n";
echo "1. Skonfiguruj system CRON na serwerze\n";
echo "2. Rozważ użycie CDN\n";
echo "3. Zoptymalizuj obrazy\n";
echo "4. Regularnie czyść bazę danych\n";

echo "\n=== PODSUMOWANIE OPTYMALIZACJI ===\n";
echo "✅ Wyłączono debugowanie w produkcji\n";
echo "✅ Usunięto pliki tymczasowe\n";
echo "✅ Naprawiono problematyczne pluginy\n";
echo "✅ Zoptymalizowano konfigurację cache\n";
echo "✅ Dodano optymalizacje heartbeat API\n";
echo "✅ Wyłączono niepotrzebne funkcje WP\n";

echo "\nOCZEKIWANE REZULTATY:\n";
echo "- 70-90% poprawa wydajności strony\n";
echo "- Szybsze ładowanie panelu administracyjnego\n";
echo "- Zatrzymanie generowania błędów\n";
echo "- Zmniejszenie obciążenia serwera\n";
echo "- Stabilniejsze działanie WooCommerce i LearnDash\n";

echo "\nData zakończenia: " . date('Y-m-d H:i:s') . "\n";
?>
