-- =====================================================
-- ULTRA OPTYMALIZACJA BAZY DANYCH - FIRMUP.EU
-- Data: 2025-01-23
-- 
-- INSTRUKCJA:
-- 1. Zrób BACKUP bazy danych przed wykonaniem!
-- 2. Wykonaj zapytania po kolei w phpMyAdmin
-- 3. Sprawdź wyniki po każdym kroku
-- 4. Monitoruj rozmiar bazy przed i po optymalizacji
-- =====================================================

-- SPRAWDŹ ROZMIAR BAZY PRZED OPTYMALIZACJĄ
SELECT 
    table_name AS 'Tabela',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Rozmiar (MB)',
    table_rows AS 'Liczba wierszy'
FROM information_schema.TABLES 
WHERE table_schema = DATABASE()
ORDER BY (data_length + index_length) DESC;

-- =====================================================
-- KROK 1: CZYSZCZENIE REWIZJI POSTÓW
-- =====================================================

-- Sprawdź liczbę rewizji przed czyszczeniem
SELECT COUNT(*) AS 'Liczba rewizji' FROM wp_posts WHERE post_type = 'revision';

-- Usuń stare rewizje (zostaw tylko 3 najnowsze dla każdego posta)
DELETE p1 FROM wp_posts p1
INNER JOIN wp_posts p2 
WHERE p1.post_type = 'revision' 
AND p1.post_parent = p2.ID
AND p1.post_date < (
    SELECT post_date FROM (
        SELECT post_date FROM wp_posts 
        WHERE post_type = 'revision' 
        AND post_parent = p2.ID 
        ORDER BY post_date DESC 
        LIMIT 1 OFFSET 3
    ) AS temp
);

-- Usuń meta dane orphaned po rewizjach
DELETE pm FROM wp_postmeta pm
LEFT JOIN wp_posts p ON pm.post_id = p.ID
WHERE p.ID IS NULL;

-- =====================================================
-- KROK 2: CZYSZCZENIE KOMENTARZY SPAM
-- =====================================================

-- Sprawdź liczbę spam komentarzy
SELECT 
    comment_approved,
    COUNT(*) as liczba
FROM wp_comments 
GROUP BY comment_approved;

-- Usuń spam komentarze
DELETE FROM wp_comments WHERE comment_approved = 'spam';

-- Usuń komentarze w koszu
DELETE FROM wp_comments WHERE comment_approved = 'trash';

-- Usuń orphaned comment meta
DELETE cm FROM wp_commentmeta cm
LEFT JOIN wp_comments c ON cm.comment_id = c.comment_ID
WHERE c.comment_ID IS NULL;

-- =====================================================
-- KROK 3: CZYSZCZENIE TRANSIENTS
-- =====================================================

-- Sprawdź liczbę transients
SELECT COUNT(*) AS 'Liczba transients' 
FROM wp_options 
WHERE option_name LIKE '_transient_%' OR option_name LIKE '_site_transient_%';

-- Usuń wygasłe transients
DELETE FROM wp_options 
WHERE option_name LIKE '_transient_timeout_%' 
AND option_value < UNIX_TIMESTAMP();

-- Usuń odpowiadające im transients
DELETE FROM wp_options 
WHERE option_name LIKE '_transient_%' 
AND option_name NOT LIKE '_transient_timeout_%'
AND option_name NOT IN (
    SELECT REPLACE(option_name, '_transient_timeout_', '_transient_') 
    FROM (SELECT option_name FROM wp_options WHERE option_name LIKE '_transient_timeout_%') AS temp
);

-- Usuń stare site transients
DELETE FROM wp_options 
WHERE option_name LIKE '_site_transient_timeout_%' 
AND option_value < UNIX_TIMESTAMP();

DELETE FROM wp_options 
WHERE option_name LIKE '_site_transient_%' 
AND option_name NOT LIKE '_site_transient_timeout_%'
AND option_name NOT IN (
    SELECT REPLACE(option_name, '_site_transient_timeout_', '_site_transient_') 
    FROM (SELECT option_name FROM wp_options WHERE option_name LIKE '_site_transient_timeout_%') AS temp
);

-- =====================================================
-- KROK 4: CZYSZCZENIE ORPHANED META
-- =====================================================

-- Usuń orphaned user meta
DELETE um FROM wp_usermeta um
LEFT JOIN wp_users u ON um.user_id = u.ID
WHERE u.ID IS NULL;

-- Usuń orphaned term relationships
DELETE tr FROM wp_term_relationships tr
LEFT JOIN wp_posts p ON tr.object_id = p.ID
LEFT JOIN wp_terms t ON tr.term_taxonomy_id = t.term_id
WHERE p.ID IS NULL OR t.term_id IS NULL;

-- Usuń orphaned term meta
DELETE tm FROM wp_termmeta tm
LEFT JOIN wp_terms t ON tm.term_id = t.term_id
WHERE t.term_id IS NULL;

-- =====================================================
-- KROK 5: CZYSZCZENIE WOOCOMMERCE
-- =====================================================

-- Usuń stare sesje WooCommerce (starsze niż 7 dni)
DELETE FROM wp_options 
WHERE option_name LIKE '_wc_session_%' 
AND option_value < (UNIX_TIMESTAMP() - 604800);

-- Usuń stare logi WooCommerce
DELETE FROM wp_options 
WHERE option_name LIKE 'woocommerce_log_%' 
AND option_value < (UNIX_TIMESTAMP() - 2592000); -- 30 dni

-- Usuń orphaned order meta
DELETE om FROM wp_postmeta om
LEFT JOIN wp_posts p ON om.post_id = p.ID
WHERE p.ID IS NULL AND om.meta_key LIKE '_order_%';

-- =====================================================
-- KROK 6: CZYSZCZENIE LEARNDASH
-- =====================================================

-- Usuń orphaned LearnDash user meta
DELETE FROM wp_usermeta 
WHERE meta_key LIKE 'learndash_%' 
AND user_id NOT IN (SELECT ID FROM wp_users);

-- Usuń stare activity LearnDash (starsze niż 6 miesięcy)
DELETE FROM wp_learndash_user_activity 
WHERE activity_updated < (UNIX_TIMESTAMP() - 15552000); -- 6 miesięcy

-- =====================================================
-- KROK 7: OPTYMALIZACJA TABEL
-- =====================================================

-- Optymalizuj wszystkie tabele WordPress
OPTIMIZE TABLE wp_posts;
OPTIMIZE TABLE wp_postmeta;
OPTIMIZE TABLE wp_options;
OPTIMIZE TABLE wp_comments;
OPTIMIZE TABLE wp_commentmeta;
OPTIMIZE TABLE wp_users;
OPTIMIZE TABLE wp_usermeta;
OPTIMIZE TABLE wp_terms;
OPTIMIZE TABLE wp_term_taxonomy;
OPTIMIZE TABLE wp_term_relationships;
OPTIMIZE TABLE wp_termmeta;

-- Optymalizuj tabele WooCommerce
OPTIMIZE TABLE wp_woocommerce_sessions;
OPTIMIZE TABLE wp_woocommerce_api_keys;
OPTIMIZE TABLE wp_woocommerce_attribute_taxonomies;
OPTIMIZE TABLE wp_woocommerce_downloadable_product_permissions;
OPTIMIZE TABLE wp_woocommerce_order_items;
OPTIMIZE TABLE wp_woocommerce_order_itemmeta;
OPTIMIZE TABLE wp_woocommerce_tax_rates;
OPTIMIZE TABLE wp_woocommerce_tax_rate_locations;
OPTIMIZE TABLE wp_woocommerce_shipping_zones;
OPTIMIZE TABLE wp_woocommerce_shipping_zone_locations;
OPTIMIZE TABLE wp_woocommerce_shipping_zone_methods;
OPTIMIZE TABLE wp_woocommerce_payment_tokens;
OPTIMIZE TABLE wp_woocommerce_payment_tokenmeta;

-- Optymalizuj tabele LearnDash
OPTIMIZE TABLE wp_learndash_user_activity;
OPTIMIZE TABLE wp_learndash_user_activity_meta;

-- =====================================================
-- KROK 8: SPRAWDŹ WYNIKI
-- =====================================================

-- Sprawdź rozmiar bazy PO optymalizacji
SELECT 
    table_name AS 'Tabela',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Rozmiar (MB)',
    table_rows AS 'Liczba wierszy'
FROM information_schema.TABLES 
WHERE table_schema = DATABASE()
ORDER BY (data_length + index_length) DESC;

-- Sprawdź całkowity rozmiar bazy
SELECT 
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Całkowity rozmiar bazy (MB)'
FROM information_schema.TABLES 
WHERE table_schema = DATABASE();

-- Sprawdź liczbę wierszy w głównych tabelach
SELECT 
    'wp_posts' as tabela, COUNT(*) as wiersze FROM wp_posts
UNION ALL
SELECT 
    'wp_postmeta' as tabela, COUNT(*) as wiersze FROM wp_postmeta
UNION ALL
SELECT 
    'wp_options' as tabela, COUNT(*) as wiersze FROM wp_options
UNION ALL
SELECT 
    'wp_comments' as tabela, COUNT(*) as wiersze FROM wp_comments
UNION ALL
SELECT 
    'wp_users' as tabela, COUNT(*) as wiersze FROM wp_users;

-- =====================================================
-- PODSUMOWANIE OPTYMALIZACJI
-- =====================================================

/*
WYKONANE OPTYMALIZACJE:
✅ Usunięto stare rewizje postów (zostaw max 3)
✅ Usunięto spam komentarze
✅ Usunięto wygasłe transients
✅ Usunięto orphaned meta data
✅ Usunięto stare sesje WooCommerce
✅ Usunięto stare logi i activity LearnDash
✅ Zoptymalizowano wszystkie tabele

OCZEKIWANE REZULTATY:
- 30-50% redukcja rozmiaru bazy danych
- Szybsze zapytania do bazy danych
- Lepsza wydajność wp-admin
- Mniej obciążenia serwera

UWAGA:
- Zawsze rób backup przed optymalizacją!
- Sprawdź działanie strony po optymalizacji
- Powtarzaj optymalizację co 3-6 miesięcy
*/
