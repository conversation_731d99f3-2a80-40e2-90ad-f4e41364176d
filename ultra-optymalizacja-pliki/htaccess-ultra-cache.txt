# =====================================================
# ULTRA OPTYMALIZACJA CACHE - FIRMUP.EU
# Data: 2025-01-23
# 
# INSTRUKCJA:
# 1. Zrób backup obecnego .htaccess
# 2. Dodaj poniższy kod na KOŃCU pliku .htaccess
# 3. Nie zastępuj całego pliku - tylko dodaj na końcu!
# =====================================================

# ULTRA CACHE HEADERS
<IfModule mod_expires.c>
ExpiresActive On

# CSS i JavaScript - cache na 1 rok
ExpiresByType text/css "access plus 1 year"
ExpiresByType application/javascript "access plus 1 year"
ExpiresByType application/x-javascript "access plus 1 year"

# Obrazy - cache na 1 rok
ExpiresByType image/png "access plus 1 year"
ExpiresByType image/jpg "access plus 1 year"
ExpiresByType image/jpeg "access plus 1 year"
ExpiresByType image/webp "access plus 1 year"
ExpiresByType image/gif "access plus 1 year"
ExpiresByType image/svg+xml "access plus 1 year"
ExpiresByType image/x-icon "access plus 1 year"
ExpiresByType image/vnd.microsoft.icon "access plus 1 year"

# Czcionki - cache na 1 rok
ExpiresByType font/woff "access plus 1 year"
ExpiresByType font/woff2 "access plus 1 year"
ExpiresByType font/ttf "access plus 1 year"
ExpiresByType font/otf "access plus 1 year"
ExpiresByType application/font-woff "access plus 1 year"
ExpiresByType application/font-woff2 "access plus 1 year"

# HTML - cache na 1 godzinę
ExpiresByType text/html "access plus 1 hour"

# XML i JSON - cache na 1 dzień
ExpiresByType application/xml "access plus 1 day"
ExpiresByType text/xml "access plus 1 day"
ExpiresByType application/json "access plus 1 day"

# PDF i dokumenty - cache na 1 miesiąc
ExpiresByType application/pdf "access plus 1 month"
ExpiresByType application/msword "access plus 1 month"
ExpiresByType application/vnd.ms-excel "access plus 1 month"
</IfModule>

# GZIP COMPRESSION - ULTRA KOMPRESJA
<IfModule mod_deflate.c>
# Kompresja tekstów
AddOutputFilterByType DEFLATE text/plain
AddOutputFilterByType DEFLATE text/html
AddOutputFilterByType DEFLATE text/xml
AddOutputFilterByType DEFLATE text/css
AddOutputFilterByType DEFLATE text/javascript

# Kompresja aplikacji
AddOutputFilterByType DEFLATE application/xml
AddOutputFilterByType DEFLATE application/xhtml+xml
AddOutputFilterByType DEFLATE application/rss+xml
AddOutputFilterByType DEFLATE application/javascript
AddOutputFilterByType DEFLATE application/x-javascript
AddOutputFilterByType DEFLATE application/json

# Kompresja czcionek
AddOutputFilterByType DEFLATE font/woff
AddOutputFilterByType DEFLATE font/woff2
AddOutputFilterByType DEFLATE application/font-woff
AddOutputFilterByType DEFLATE application/font-woff2

# Wyłącz kompresję dla starych przeglądarek
BrowserMatch ^Mozilla/4 gzip-only-text/html
BrowserMatch ^Mozilla/4\.0[678] no-gzip
BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
</IfModule>

# BROWSER CACHING - ULTRA CACHE CONTROL
<IfModule mod_headers.c>
# Cache dla statycznych zasobów
<FilesMatch "\.(css|js|png|jpg|jpeg|webp|gif|ico|svg|woff|woff2|ttf|otf|eot)$">
Header set Cache-Control "max-age=31536000, public, immutable"
Header set Pragma "public"
Header append Vary "Accept-Encoding"
</FilesMatch>

# Cache dla HTML
<FilesMatch "\.(html|htm)$">
Header set Cache-Control "max-age=3600, public"
Header set Pragma "public"
</FilesMatch>

# Cache dla XML i JSON
<FilesMatch "\.(xml|json)$">
Header set Cache-Control "max-age=86400, public"
Header set Pragma "public"
</FilesMatch>

# Usuń ETag (nie potrzebne z Cache-Control)
Header unset ETag
FileETag None
</IfModule>

# SECURITY HEADERS - DODATKOWE BEZPIECZEŃSTWO
<IfModule mod_headers.c>
# Bezpieczeństwo
Header always set X-Content-Type-Options "nosniff"
Header always set X-Frame-Options "SAMEORIGIN"
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Content Security Policy (podstawowy)
Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' *.firmup.eu *.googleapis.com *.gstatic.com *.google-analytics.com *.googletagmanager.com"
</IfModule>

# OPTYMALIZACJA PLIKÓW - KOMPRESJA I CACHE
<IfModule mod_rewrite.c>
RewriteEngine On

# Przekierowanie na WebP jeśli dostępne
RewriteCond %{HTTP_ACCEPT} image/webp
RewriteCond %{REQUEST_FILENAME} \.(jpe?g|png)$
RewriteCond %{REQUEST_FILENAME}.webp -f
RewriteRule ^(.+)\.(jpe?g|png)$ $1.$2.webp [T=image/webp,E=accept:1,L]

# Dodaj nagłówek Vary dla WebP
RewriteRule \.webp$ - [E=WEBP:1]
Header append Vary Accept env=WEBP
</IfModule>

# BLOKOWANIE NIEPOTRZEBNYCH ZAPYTAŃ
<IfModule mod_rewrite.c>
# Blokuj dostęp do plików konfiguracyjnych
RewriteRule ^wp-config\.php$ - [F,L]
RewriteRule ^wp-admin/includes/ - [F,L]
RewriteRule ^wp-includes/[^/]+\.php$ - [F,L]
RewriteRule ^wp-includes/js/tinymce/langs/.+\.php - [F,L]
RewriteRule ^wp-includes/theme-compat/ - [F,L]

# Blokuj XML-RPC (jeśli nie używane)
RewriteRule ^xmlrpc\.php$ - [F,L]

# Blokuj dostęp do logów
RewriteRule ^(.*/)?\..*$ - [F,L]
RewriteRule ^(.*/)?.*\.(log|txt)$ - [F,L]
</IfModule>

# OPTYMALIZACJA POŁĄCZEŃ
<IfModule mod_headers.c>
# Keep-Alive dla lepszej wydajności
Header set Connection "Keep-Alive"
Header set Keep-Alive "timeout=5, max=100"
</IfModule>

# PRELOAD HEADERS - KRYTYCZNE ZASOBY
<IfModule mod_headers.c>
# Preload krytycznego CSS
<FilesMatch "style\.css$">
Header set Link "</wp-content/themes/oceanwp/style.css>; rel=preload; as=style"
</FilesMatch>

# Preload głównej czcionki
<FilesMatch "\.woff2$">
Header set Link "<$0>; rel=preload; as=font; type=font/woff2; crossorigin"
</FilesMatch>
</IfModule>

# KOŃCOWE OPTYMALIZACJE
<IfModule mod_mime.c>
# Dodaj typy MIME dla WebP
AddType image/webp .webp

# Dodaj typy MIME dla czcionek
AddType font/woff .woff
AddType font/woff2 .woff2
</IfModule>

# =====================================================
# KONIEC ULTRA OPTYMALIZACJI CACHE
# =====================================================
