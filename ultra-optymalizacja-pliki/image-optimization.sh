#!/bin/bash

# =====================================================
# ULTRA OPTYMALIZACJA OBRAZÓW - FIRMUP.EU
# Data: 2025-01-23
# 
# Ten skrypt automatycznie optymalizuje wszystkie obrazy
# na stronie dla maksymalnej wydajności
# 
# WYMAGANIA:
# - jpegoptim (sudo apt-get install jpegoptim)
# - optipng (sudo apt-get install optipng)  
# - cwebp (sudo apt-get install webp)
# - fdupes (sudo apt-get install fdupes)
# =====================================================

# Kolory dla lepszej czytelności
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funkcja wyświetlania nagłówka
print_header() {
    echo -e "${BLUE}=================================================${NC}"
    echo -e "${BLUE}🚀 ULTRA OPTYMALIZACJA OBRAZÓW - FIRMUP.EU${NC}"
    echo -e "${BLUE}=================================================${NC}"
    echo ""
}

# Funkcja sprawdzania wymaganych narzędzi
check_requirements() {
    echo -e "${YELLOW}🔍 Sprawdzanie wymaganych narzędzi...${NC}"
    
    MISSING_TOOLS=()
    
    if ! command -v jpegoptim &> /dev/null; then
        MISSING_TOOLS+=("jpegoptim")
    fi
    
    if ! command -v optipng &> /dev/null; then
        MISSING_TOOLS+=("optipng")
    fi
    
    if ! command -v cwebp &> /dev/null; then
        MISSING_TOOLS+=("cwebp")
    fi
    
    if ! command -v fdupes &> /dev/null; then
        MISSING_TOOLS+=("fdupes")
    fi
    
    if [ ${#MISSING_TOOLS[@]} -ne 0 ]; then
        echo -e "${RED}❌ Brakujące narzędzia: ${MISSING_TOOLS[*]}${NC}"
        echo -e "${YELLOW}Zainstaluj je używając:${NC}"
        echo "sudo apt-get update"
        echo "sudo apt-get install jpegoptim optipng webp fdupes"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Wszystkie narzędzia są dostępne${NC}"
    echo ""
}

# Funkcja tworzenia backupu
create_backup() {
    echo -e "${YELLOW}💾 Tworzenie backupu obrazów...${NC}"
    
    BACKUP_DIR="wp-content/uploads-backup-$(date +%Y%m%d-%H%M%S)"
    
    if [ -d "wp-content/uploads" ]; then
        cp -r wp-content/uploads "$BACKUP_DIR"
        echo -e "${GREEN}✅ Backup utworzony: $BACKUP_DIR${NC}"
    else
        echo -e "${RED}❌ Folder wp-content/uploads nie istnieje${NC}"
        exit 1
    fi
    echo ""
}

# Funkcja analizy obecnego stanu
analyze_current_state() {
    echo -e "${YELLOW}📊 Analiza obecnego stanu obrazów...${NC}"
    
    if [ ! -d "wp-content/uploads" ]; then
        echo -e "${RED}❌ Folder wp-content/uploads nie istnieje${NC}"
        exit 1
    fi
    
    cd wp-content/uploads
    
    # Liczba plików
    JPG_COUNT=$(find . -name "*.jpg" -o -name "*.jpeg" | wc -l)
    PNG_COUNT=$(find . -name "*.png" | wc -l)
    WEBP_COUNT=$(find . -name "*.webp" | wc -l)
    
    # Rozmiar plików
    JPG_SIZE=$(find . -name "*.jpg" -o -name "*.jpeg" -exec du -cb {} + 2>/dev/null | tail -1 | cut -f1)
    PNG_SIZE=$(find . -name "*.png" -exec du -cb {} + 2>/dev/null | tail -1 | cut -f1)
    WEBP_SIZE=$(find . -name "*.webp" -exec du -cb {} + 2>/dev/null | tail -1 | cut -f1)
    
    # Konwersja na MB
    JPG_SIZE_MB=$(echo "scale=2; $JPG_SIZE / 1024 / 1024" | bc 2>/dev/null || echo "0")
    PNG_SIZE_MB=$(echo "scale=2; $PNG_SIZE / 1024 / 1024" | bc 2>/dev/null || echo "0")
    WEBP_SIZE_MB=$(echo "scale=2; $WEBP_SIZE / 1024 / 1024" | bc 2>/dev/null || echo "0")
    
    echo "📸 JPG/JPEG: $JPG_COUNT plików (${JPG_SIZE_MB}MB)"
    echo "🖼️  PNG: $PNG_COUNT plików (${PNG_SIZE_MB}MB)"
    echo "🌐 WebP: $WEBP_COUNT plików (${WEBP_SIZE_MB}MB)"
    echo ""
    
    cd ../..
}

# Funkcja usuwania duplikatów
remove_duplicates() {
    echo -e "${YELLOW}🗑️  Usuwanie duplikatów obrazów...${NC}"
    
    cd wp-content/uploads
    
    # Znajdź i usuń duplikaty
    DUPLICATES=$(fdupes -r . | grep -v "^$" | wc -l)
    
    if [ $DUPLICATES -gt 0 ]; then
        echo "Znaleziono $DUPLICATES duplikatów"
        fdupes -r -d -N .
        echo -e "${GREEN}✅ Duplikaty usunięte${NC}"
    else
        echo -e "${GREEN}✅ Brak duplikatów do usunięcia${NC}"
    fi
    
    cd ../..
    echo ""
}

# Funkcja usuwania niepotrzebnych rozmiarów
remove_unnecessary_sizes() {
    echo -e "${YELLOW}🗑️  Usuwanie niepotrzebnych rozmiarów obrazów...${NC}"
    
    cd wp-content/uploads
    
    # Usuń małe rozmiary (zostaw tylko potrzebne)
    REMOVED_COUNT=0
    
    # Usuń bardzo małe rozmiary
    for size in "150x150" "100x100" "75x75" "50x50"; do
        COUNT=$(find . -name "*-${size}.*" | wc -l)
        if [ $COUNT -gt 0 ]; then
            find . -name "*-${size}.*" -delete
            REMOVED_COUNT=$((REMOVED_COUNT + COUNT))
        fi
    done
    
    echo -e "${GREEN}✅ Usunięto $REMOVED_COUNT niepotrzebnych miniatur${NC}"
    
    cd ../..
    echo ""
}

# Funkcja optymalizacji JPG
optimize_jpg() {
    echo -e "${YELLOW}📸 Optymalizacja plików JPG/JPEG...${NC}"
    
    cd wp-content/uploads
    
    JPG_FILES=$(find . -name "*.jpg" -o -name "*.jpeg")
    JPG_COUNT=$(echo "$JPG_FILES" | wc -l)
    
    if [ $JPG_COUNT -gt 0 ]; then
        echo "Optymalizacja $JPG_COUNT plików JPG/JPEG..."
        
        # Optymalizuj z jakością 85% i usuń metadane
        find . -name "*.jpg" -exec jpegoptim --max=85 --strip-all {} \;
        find . -name "*.jpeg" -exec jpegoptim --max=85 --strip-all {} \;
        
        echo -e "${GREEN}✅ Pliki JPG/JPEG zoptymalizowane${NC}"
    else
        echo -e "${GREEN}✅ Brak plików JPG/JPEG do optymalizacji${NC}"
    fi
    
    cd ../..
    echo ""
}

# Funkcja optymalizacji PNG
optimize_png() {
    echo -e "${YELLOW}🖼️  Optymalizacja plików PNG...${NC}"
    
    cd wp-content/uploads
    
    PNG_FILES=$(find . -name "*.png")
    PNG_COUNT=$(echo "$PNG_FILES" | wc -l)
    
    if [ $PNG_COUNT -gt 0 ]; then
        echo "Optymalizacja $PNG_COUNT plików PNG..."
        
        # Optymalizuj z maksymalną kompresją
        find . -name "*.png" -exec optipng -o7 -strip all {} \;
        
        echo -e "${GREEN}✅ Pliki PNG zoptymalizowane${NC}"
    else
        echo -e "${GREEN}✅ Brak plików PNG do optymalizacji${NC}"
    fi
    
    cd ../..
    echo ""
}

# Funkcja konwersji do WebP
convert_to_webp() {
    echo -e "${YELLOW}🌐 Konwersja obrazów do formatu WebP...${NC}"
    
    cd wp-content/uploads
    
    # Konwertuj JPG/JPEG do WebP
    JPG_FILES=$(find . -name "*.jpg" -o -name "*.jpeg")
    if [ ! -z "$JPG_FILES" ]; then
        echo "Konwersja plików JPG/JPEG do WebP..."
        find . -name "*.jpg" -exec cwebp -q 85 {} -o {}.webp \;
        find . -name "*.jpeg" -exec cwebp -q 85 {} -o {}.webp \;
    fi
    
    # Konwertuj PNG do WebP
    PNG_FILES=$(find . -name "*.png")
    if [ ! -z "$PNG_FILES" ]; then
        echo "Konwersja plików PNG do WebP..."
        find . -name "*.png" -exec cwebp -q 85 {} -o {}.webp \;
    fi
    
    # Popraw rozszerzenia WebP (usuń podwójne rozszerzenia)
    find . -name "*.jpg.webp" -exec rename 's/\.jpg\.webp$/.webp/' {} \;
    find . -name "*.jpeg.webp" -exec rename 's/\.jpeg\.webp$/.webp/' {} \;
    find . -name "*.png.webp" -exec rename 's/\.png\.webp$/.webp/' {} \;
    
    WEBP_COUNT=$(find . -name "*.webp" | wc -l)
    echo -e "${GREEN}✅ Utworzono $WEBP_COUNT plików WebP${NC}"
    
    cd ../..
    echo ""
}

# Funkcja czyszczenia starych plików
cleanup_old_files() {
    echo -e "${YELLOW}🧹 Czyszczenie starych i niepotrzebnych plików...${NC}"
    
    cd wp-content/uploads
    
    # Usuń pliki tymczasowe
    TEMP_COUNT=$(find . -name "*.tmp" -o -name "*.temp" -o -name "*~" | wc -l)
    if [ $TEMP_COUNT -gt 0 ]; then
        find . -name "*.tmp" -delete
        find . -name "*.temp" -delete
        find . -name "*~" -delete
        echo "Usunięto $TEMP_COUNT plików tymczasowych"
    fi
    
    # Usuń stare backup tłumaczeń
    BACKUP_COUNT=$(find . -name "*backup-*.po~" | wc -l)
    if [ $BACKUP_COUNT -gt 0 ]; then
        find . -name "*backup-*.po~" -delete
        echo "Usunięto $BACKUP_COUNT plików backup tłumaczeń"
    fi
    
    echo -e "${GREEN}✅ Czyszczenie zakończone${NC}"
    
    cd ../..
    echo ""
}

# Funkcja podsumowania
show_summary() {
    echo -e "${BLUE}=================================================${NC}"
    echo -e "${BLUE}📊 PODSUMOWANIE OPTYMALIZACJI${NC}"
    echo -e "${BLUE}=================================================${NC}"
    
    cd wp-content/uploads
    
    # Nowe statystyki
    NEW_JPG_COUNT=$(find . -name "*.jpg" -o -name "*.jpeg" | wc -l)
    NEW_PNG_COUNT=$(find . -name "*.png" | wc -l)
    NEW_WEBP_COUNT=$(find . -name "*.webp" | wc -l)
    
    NEW_JPG_SIZE=$(find . -name "*.jpg" -o -name "*.jpeg" -exec du -cb {} + 2>/dev/null | tail -1 | cut -f1)
    NEW_PNG_SIZE=$(find . -name "*.png" -exec du -cb {} + 2>/dev/null | tail -1 | cut -f1)
    NEW_WEBP_SIZE=$(find . -name "*.webp" -exec du -cb {} + 2>/dev/null | tail -1 | cut -f1)
    
    NEW_JPG_SIZE_MB=$(echo "scale=2; $NEW_JPG_SIZE / 1024 / 1024" | bc 2>/dev/null || echo "0")
    NEW_PNG_SIZE_MB=$(echo "scale=2; $NEW_PNG_SIZE / 1024 / 1024" | bc 2>/dev/null || echo "0")
    NEW_WEBP_SIZE_MB=$(echo "scale=2; $NEW_WEBP_SIZE / 1024 / 1024" | bc 2>/dev/null || echo "0")
    
    TOTAL_SIZE_MB=$(echo "scale=2; $NEW_JPG_SIZE_MB + $NEW_PNG_SIZE_MB + $NEW_WEBP_SIZE_MB" | bc 2>/dev/null || echo "0")
    
    echo ""
    echo -e "${GREEN}✅ WYNIKI OPTYMALIZACJI:${NC}"
    echo "📸 JPG/JPEG: $NEW_JPG_COUNT plików (${NEW_JPG_SIZE_MB}MB)"
    echo "🖼️  PNG: $NEW_PNG_COUNT plików (${NEW_PNG_SIZE_MB}MB)"
    echo "🌐 WebP: $NEW_WEBP_COUNT plików (${NEW_WEBP_SIZE_MB}MB)"
    echo "📊 Całkowity rozmiar: ${TOTAL_SIZE_MB}MB"
    echo ""
    echo -e "${GREEN}🎯 KORZYŚCI:${NC}"
    echo "• Obrazy zoptymalizowane pod kątem wydajności"
    echo "• Utworzono nowoczesne pliki WebP"
    echo "• Usunięto duplikaty i niepotrzebne pliki"
    echo "• Zmniejszono rozmiar bez utraty jakości"
    echo ""
    echo -e "${YELLOW}📝 NASTĘPNE KROKI:${NC}"
    echo "1. Sprawdź działanie strony"
    echo "2. Włącz WebP w ustawieniach serwera"
    echo "3. Skonfiguruj plugin WebP Converter"
    echo "4. Przetestuj wydajność strony"
    echo ""
    
    cd ../..
}

# Główna funkcja
main() {
    print_header
    
    # Sprawdź czy jesteśmy w katalogu WordPress
    if [ ! -f "wp-config.php" ]; then
        echo -e "${RED}❌ Ten skrypt musi być uruchomiony z głównego katalogu WordPress${NC}"
        exit 1
    fi
    
    check_requirements
    
    echo -e "${YELLOW}⚠️  UWAGA: Ten skrypt zmodyfikuje Twoje obrazy!${NC}"
    echo -e "${YELLOW}Backup zostanie utworzony automatycznie.${NC}"
    echo ""
    read -p "Czy chcesz kontynuować? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Operacja anulowana${NC}"
        exit 0
    fi
    
    create_backup
    analyze_current_state
    remove_duplicates
    remove_unnecessary_sizes
    optimize_jpg
    optimize_png
    convert_to_webp
    cleanup_old_files
    show_summary
    
    echo -e "${GREEN}🎉 ULTRA OPTYMALIZACJA OBRAZÓW ZAKOŃCZONA!${NC}"
    echo ""
}

# Uruchom główną funkcję
main "$@"
