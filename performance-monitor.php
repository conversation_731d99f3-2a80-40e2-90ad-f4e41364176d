<?php
/**
 * 📊 MONITOR WYDAJNOŚCI FIRMUP.EU
 * 
 * Skrypt do monitorowania wydajności strony w czasie rzeczywistym
 * 
 * INSTRUKCJA UŻYCIA:
 * 1. Wgraj plik na serwer do głównego katalogu WordPress
 * 2. Otwórz w przeglądarce: https://firmup.eu/performance-monitor.php
 * 3. Sprawdź wyniki i porównaj przed/po optymalizacji
 * 4. USUŃ PLIK po testach (ze względów bezpieczeństwa)
 */

// Zabezpieczenie - tylko dla administratorów
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

// Ładuj WordPress
require_once(ABSPATH . 'wp-config.php');
require_once(ABSPATH . 'wp-includes/wp-db.php');
require_once(ABSPATH . 'wp-includes/functions.php');

// Sprawdź uprawnienia
if (!current_user_can('manage_options')) {
    die('Brak uprawnień do wyświetlenia tej strony.');
}

// Rozpocznij pomiary
$start_time = microtime(true);
$start_memory = memory_get_usage();

?>
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitor Wydajności - FIRMUP.EU</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; border-bottom: 2px solid #0073aa; padding-bottom: 20px; margin-bottom: 30px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: #f9f9f9; padding: 20px; border-radius: 8px; border-left: 4px solid #0073aa; }
        .metric-title { font-weight: bold; color: #333; margin-bottom: 10px; font-size: 16px; }
        .metric-value { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .metric-good { color: #46b450; }
        .metric-warning { color: #ffb900; }
        .metric-bad { color: #dc3232; }
        .metric-description { font-size: 12px; color: #666; }
        .test-section { margin-bottom: 30px; }
        .test-title { font-size: 18px; font-weight: bold; color: #333; margin-bottom: 15px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
        .test-result { background: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 10px; }
        .recommendations { background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #0073aa; }
        .recommendations h3 { color: #0073aa; margin-top: 0; }
        .recommendations ul { margin: 10px 0; padding-left: 20px; }
        .recommendations li { margin-bottom: 5px; }
        .refresh-btn { background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .refresh-btn:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Monitor Wydajności FIRMUP.EU</h1>
            <p>Analiza wydajności strony w czasie rzeczywistym</p>
            <button class="refresh-btn" onclick="location.reload()">🔄 Odśwież Testy</button>
        </div>

        <?php
        // =====================================================
        // TESTY WYDAJNOŚCI
        // =====================================================

        // Test 1: Czas odpowiedzi bazy danych
        $db_start = microtime(true);
        global $wpdb;
        $wpdb->get_results("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'publish'");
        $db_time = (microtime(true) - $db_start) * 1000;

        // Test 2: Test cache obiektów
        $cache_start = microtime(true);
        $cache_test = wp_cache_get('performance_test_key', 'performance_test_group');
        if ($cache_test === false) {
            wp_cache_set('performance_test_key', 'test_value', 'performance_test_group', 3600);
        }
        $cache_time = (microtime(true) - $cache_start) * 1000;

        // Test 3: Pomiar pamięci
        $memory_usage = memory_get_usage(true) / 1024 / 1024;
        $memory_peak = memory_get_peak_usage(true) / 1024 / 1024;
        $memory_limit = ini_get('memory_limit');

        // Test 4: Liczba zapytań do bazy
        $query_count = get_num_queries();

        // Test 5: Czas całkowity
        $total_time = (microtime(true) - $start_time) * 1000;

        // Test 6: Sprawdź rozmiar bazy danych
        $db_size_query = $wpdb->get_results("
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'db_size_mb'
            FROM information_schema.tables 
            WHERE table_schema = '" . DB_NAME . "'
        ");
        $db_size = $db_size_query[0]->db_size_mb ?? 'N/A';

        // Test 7: Sprawdź liczbę postów
        $posts_count = wp_count_posts();
        $total_posts = $posts_count->publish + $posts_count->draft + $posts_count->private;

        // Test 8: Sprawdź aktywne pluginy
        $active_plugins = get_option('active_plugins');
        $plugins_count = count($active_plugins);

        // Test 9: Sprawdź rozmiar uploads
        $uploads_dir = wp_upload_dir();
        $uploads_size = 0;
        if (is_dir($uploads_dir['basedir'])) {
            $uploads_size = folderSize($uploads_dir['basedir']) / 1024 / 1024;
        }

        function folderSize($dir) {
            $size = 0;
            if (is_dir($dir)) {
                $files = scandir($dir);
                foreach ($files as $file) {
                    if ($file != "." && $file != "..") {
                        if (is_dir($dir . "/" . $file)) {
                            $size += folderSize($dir . "/" . $file);
                        } else {
                            $size += filesize($dir . "/" . $file);
                        }
                    }
                }
            }
            return $size;
        }

        // Funkcje oceny wydajności
        function getPerformanceClass($value, $good, $warning) {
            if ($value <= $good) return 'metric-good';
            if ($value <= $warning) return 'metric-warning';
            return 'metric-bad';
        }

        function getPerformanceText($value, $good, $warning) {
            if ($value <= $good) return 'Doskonale';
            if ($value <= $warning) return 'Do poprawy';
            return 'Wymaga optymalizacji';
        }
        ?>

        <div class="metric-grid">
            <!-- Czas ładowania strony -->
            <div class="metric-card">
                <div class="metric-title">⏱️ Czas ładowania strony</div>
                <div class="metric-value <?php echo getPerformanceClass($total_time, 500, 1000); ?>">
                    <?php echo number_format($total_time, 2); ?>ms
                </div>
                <div class="metric-description">
                    <?php echo getPerformanceText($total_time, 500, 1000); ?> 
                    (Cel: &lt;500ms, Akceptowalne: &lt;1000ms)
                </div>
            </div>

            <!-- Czas odpowiedzi bazy danych -->
            <div class="metric-card">
                <div class="metric-title">🗄️ Czas odpowiedzi bazy danych</div>
                <div class="metric-value <?php echo getPerformanceClass($db_time, 50, 100); ?>">
                    <?php echo number_format($db_time, 2); ?>ms
                </div>
                <div class="metric-description">
                    <?php echo getPerformanceText($db_time, 50, 100); ?>
                    (Cel: &lt;50ms, Akceptowalne: &lt;100ms)
                </div>
            </div>

            <!-- Użycie pamięci -->
            <div class="metric-card">
                <div class="metric-title">💾 Użycie pamięci</div>
                <div class="metric-value <?php echo getPerformanceClass($memory_usage, 64, 128); ?>">
                    <?php echo number_format($memory_usage, 2); ?>MB
                </div>
                <div class="metric-description">
                    Peak: <?php echo number_format($memory_peak, 2); ?>MB / Limit: <?php echo $memory_limit; ?>
                </div>
            </div>

            <!-- Liczba zapytań do bazy -->
            <div class="metric-card">
                <div class="metric-title">📊 Zapytania do bazy danych</div>
                <div class="metric-value <?php echo getPerformanceClass($query_count, 20, 50); ?>">
                    <?php echo $query_count; ?>
                </div>
                <div class="metric-description">
                    <?php echo getPerformanceText($query_count, 20, 50); ?>
                    (Cel: &lt;20, Akceptowalne: &lt;50)
                </div>
            </div>

            <!-- Rozmiar bazy danych -->
            <div class="metric-card">
                <div class="metric-title">🗃️ Rozmiar bazy danych</div>
                <div class="metric-value <?php echo getPerformanceClass($db_size, 100, 500); ?>">
                    <?php echo $db_size; ?>MB
                </div>
                <div class="metric-description">
                    Posty: <?php echo number_format($total_posts); ?> | 
                    <?php echo getPerformanceText($db_size, 100, 500); ?>
                </div>
            </div>

            <!-- Aktywne pluginy -->
            <div class="metric-card">
                <div class="metric-title">🔌 Aktywne pluginy</div>
                <div class="metric-value <?php echo getPerformanceClass($plugins_count, 20, 40); ?>">
                    <?php echo $plugins_count; ?>
                </div>
                <div class="metric-description">
                    <?php echo getPerformanceText($plugins_count, 20, 40); ?>
                    (Cel: &lt;20, Akceptowalne: &lt;40)
                </div>
            </div>
        </div>

        <!-- Rekomendacje -->
        <div class="recommendations">
            <h3>🎯 Rekomendacje optymalizacji</h3>
            
            <?php if ($total_time > 1000): ?>
            <div class="test-result">
                <strong>⚠️ Wolne ładowanie strony (<?php echo number_format($total_time, 2); ?>ms)</strong>
                <ul>
                    <li>Włącz cache strony (Seraphinite Accelerator)</li>
                    <li>Optymalizuj obrazy (konwersja do WebP)</li>
                    <li>Zminifikuj CSS i JavaScript</li>
                    <li>Usuń niepotrzebne pluginy</li>
                </ul>
            </div>
            <?php endif; ?>

            <?php if ($db_time > 100): ?>
            <div class="test-result">
                <strong>⚠️ Wolna baza danych (<?php echo number_format($db_time, 2); ?>ms)</strong>
                <ul>
                    <li>Wyczyść niepotrzebne rewizje postów</li>
                    <li>Usuń spam komentarze</li>
                    <li>Zoptymalizuj tabele bazy danych</li>
                    <li>Rozważ cache obiektów</li>
                </ul>
            </div>
            <?php endif; ?>

            <?php if ($memory_usage > 128): ?>
            <div class="test-result">
                <strong>⚠️ Wysokie użycie pamięci (<?php echo number_format($memory_usage, 2); ?>MB)</strong>
                <ul>
                    <li>Wyłącz niepotrzebne pluginy</li>
                    <li>Optymalizuj zapytania do bazy danych</li>
                    <li>Zwiększ limit pamięci PHP</li>
                    <li>Użyj cache obiektów</li>
                </ul>
            </div>
            <?php endif; ?>

            <?php if ($query_count > 50): ?>
            <div class="test-result">
                <strong>⚠️ Zbyt wiele zapytań do bazy (<?php echo $query_count; ?>)</strong>
                <ul>
                    <li>Sprawdź pluginy generujące wiele zapytań</li>
                    <li>Optymalizuj motywy i szablony</li>
                    <li>Użyj cache zapytań</li>
                    <li>Rozważ lazy loading dla treści</li>
                </ul>
            </div>
            <?php endif; ?>

            <?php if ($plugins_count > 40): ?>
            <div class="test-result">
                <strong>⚠️ Zbyt wiele aktywnych pluginów (<?php echo $plugins_count; ?>)</strong>
                <ul>
                    <li>Usuń nieużywane pluginy</li>
                    <li>Połącz funkcjonalność podobnych pluginów</li>
                    <li>Sprawdź pluginy spowalniające stronę</li>
                    <li>Rozważ alternatywy w kodzie motywu</li>
                </ul>
            </div>
            <?php endif; ?>

            <?php if ($total_time <= 500 && $db_time <= 50 && $memory_usage <= 64 && $query_count <= 20): ?>
            <div class="test-result">
                <strong style="color: #46b450;">✅ Doskonała wydajność!</strong>
                <p>Twoja strona działa optymalnie. Wszystkie metryki są w zielonych zakresach.</p>
            </div>
            <?php endif; ?>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px;">
            <p>Monitor wydajności FIRMUP.EU | Wygenerowano: <?php echo date('Y-m-d H:i:s'); ?></p>
            <p><strong>⚠️ WAŻNE:</strong> Usuń ten plik po zakończeniu testów ze względów bezpieczeństwa!</p>
        </div>
    </div>
</body>
</html>
