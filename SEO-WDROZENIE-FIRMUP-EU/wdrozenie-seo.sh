#!/bin/bash

# ========================================
# AUTOMATYCZNE WDROŻENIE SEO - FIRMUP.EU
# ========================================
# 
# Skrypt automatycznie wdraża wszystkie optymalizacje SEO
# Data: 2025-01-23
# Wykonawca: Hubert - SEO Specialist
# 
# UŻYCIE:
# 1. Wgraj cały folder SEO-WDROZENIE-FIRMUP-EU na serwer
# 2. Uruchom: chmod +x wdrozenie-seo.sh
# 3. Wykonaj: ./wdrozenie-seo.sh
# ========================================

# Kolory dla lepszej czytelności
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Funkcja wyświetlania nagłówka
print_header() {
    clear
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}🚀 WDROŻENIE SEO - FIRMUP.EU${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo -e "${PURPLE}Data wdrożenia: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo -e "${PURPLE}Wykonawca: Hubert - SEO Specialist${NC}"
    echo ""
}

# Funkcja sprawdzania wymagań
check_requirements() {
    echo -e "${YELLOW}🔍 Sprawdzanie wymagań...${NC}"
    
    # Sprawdź czy jesteśmy w katalogu WordPress
    if [ ! -f "wp-config.php" ]; then
        echo -e "${RED}❌ Błąd: Nie znaleziono wp-config.php${NC}"
        echo -e "${RED}   Uruchom skrypt z głównego katalogu WordPress!${NC}"
        exit 1
    fi
    
    # Sprawdź czy istnieją pliki SEO
    if [ ! -f "SEO-WDROZENIE-FIRMUP-EU/functions-seo-optimized.php" ]; then
        echo -e "${RED}❌ Błąd: Nie znaleziono plików SEO${NC}"
        echo -e "${RED}   Upewnij się, że folder SEO-WDROZENIE-FIRMUP-EU jest w katalogu WordPress!${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Wszystkie wymagania spełnione${NC}"
    echo ""
}

# Funkcja tworzenia backupu
create_backup() {
    echo -e "${YELLOW}💾 Tworzenie backupu...${NC}"
    
    BACKUP_DIR="seo-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup functions.php
    if [ -f "wp-content/themes/oceanwp/functions.php" ]; then
        cp "wp-content/themes/oceanwp/functions.php" "$BACKUP_DIR/functions.php.backup"
        echo "✅ Backup functions.php utworzony"
    fi
    
    # Backup .htaccess
    if [ -f ".htaccess" ]; then
        cp ".htaccess" "$BACKUP_DIR/htaccess.backup"
        echo "✅ Backup .htaccess utworzony"
    fi
    
    # Backup robots.txt
    if [ -f "robots.txt" ]; then
        cp "robots.txt" "$BACKUP_DIR/robots.txt.backup"
        echo "✅ Backup robots.txt utworzony"
    fi
    
    echo -e "${GREEN}✅ Backup utworzony w folderze: $BACKUP_DIR${NC}"
    echo ""
}

# Funkcja wdrażania optymalizacji functions.php
deploy_functions() {
    echo -e "${YELLOW}🔧 Wdrażanie optymalizacji functions.php...${NC}"
    
    FUNCTIONS_FILE="wp-content/themes/oceanwp/functions.php"
    SEO_CODE="SEO-WDROZENIE-FIRMUP-EU/functions-seo-optimized.php"
    
    if [ -f "$FUNCTIONS_FILE" ]; then
        # Sprawdź czy optymalizacje już nie zostały dodane
        if grep -q "SEO OPTYMALIZACJA - FIRMUP.EU" "$FUNCTIONS_FILE"; then
            echo -e "${YELLOW}⚠️  Optymalizacje SEO już istnieją w functions.php${NC}"
            read -p "Czy chcesz je zastąpić nowymi? (y/N): " -n 1 -r
            echo ""
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                echo -e "${YELLOW}Pomijam functions.php${NC}"
                return
            fi
            
            # Usuń stare optymalizacje
            sed -i '/SEO OPTYMALIZACJA - FIRMUP.EU/,/KONIEC OPTYMALIZACJI SEO/d' "$FUNCTIONS_FILE"
        fi
        
        # Dodaj nowe optymalizacje na końcu pliku
        echo "" >> "$FUNCTIONS_FILE"
        echo "// ========================================" >> "$FUNCTIONS_FILE"
        echo "// AUTOMATYCZNIE DODANE PRZEZ SKRYPT SEO" >> "$FUNCTIONS_FILE"
        echo "// Data: $(date '+%Y-%m-%d %H:%M:%S')" >> "$FUNCTIONS_FILE"
        echo "// ========================================" >> "$FUNCTIONS_FILE"
        echo "" >> "$FUNCTIONS_FILE"
        
        # Dodaj kod SEO (bez tagów PHP na początku i końcu)
        sed '1d;$d' "$SEO_CODE" >> "$FUNCTIONS_FILE"
        
        echo -e "${GREEN}✅ Optymalizacje SEO dodane do functions.php${NC}"
    else
        echo -e "${RED}❌ Nie znaleziono pliku functions.php${NC}"
    fi
    echo ""
}

# Funkcja wdrażania .htaccess
deploy_htaccess() {
    echo -e "${YELLOW}🔧 Wdrażanie optymalizacji .htaccess...${NC}"
    
    HTACCESS_FILE=".htaccess"
    SEO_HTACCESS="SEO-WDROZENIE-FIRMUP-EU/htaccess-seo-boost.txt"
    
    # Sprawdź czy optymalizacje już nie zostały dodane
    if [ -f "$HTACCESS_FILE" ] && grep -q "SEO BOOST - FIRMUP.EU" "$HTACCESS_FILE"; then
        echo -e "${YELLOW}⚠️  Optymalizacje SEO już istnieją w .htaccess${NC}"
        read -p "Czy chcesz je zastąpić nowymi? (y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${YELLOW}Pomijam .htaccess${NC}"
            return
        fi
        
        # Usuń stare optymalizacje
        sed -i '/SEO BOOST - FIRMUP.EU/,/KONIEC OPTYMALIZACJI SEO .HTACCESS/d' "$HTACCESS_FILE"
    fi
    
    # Dodaj nowe optymalizacje na końcu pliku
    echo "" >> "$HTACCESS_FILE"
    echo "# ========================================" >> "$HTACCESS_FILE"
    echo "# AUTOMATYCZNIE DODANE PRZEZ SKRYPT SEO" >> "$HTACCESS_FILE"
    echo "# Data: $(date '+%Y-%m-%d %H:%M:%S')" >> "$HTACCESS_FILE"
    echo "# ========================================" >> "$HTACCESS_FILE"
    echo "" >> "$HTACCESS_FILE"
    
    cat "$SEO_HTACCESS" >> "$HTACCESS_FILE"
    
    echo -e "${GREEN}✅ Optymalizacje SEO dodane do .htaccess${NC}"
    echo ""
}

# Funkcja tworzenia robots.txt
create_robots() {
    echo -e "${YELLOW}🤖 Tworzenie zoptymalizowanego robots.txt...${NC}"
    
    ROBOTS_FILE="robots.txt"
    
    if [ -f "$ROBOTS_FILE" ]; then
        echo -e "${YELLOW}⚠️  Plik robots.txt już istnieje${NC}"
        read -p "Czy chcesz go zastąpić zoptymalizowaną wersją? (y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${YELLOW}Pomijam robots.txt${NC}"
            return
        fi
    fi
    
    # Utwórz zoptymalizowany robots.txt
    cat > "$ROBOTS_FILE" << 'EOF'
# Robots.txt dla FIRMUP.EU - Zoptymalizowany dla SEO
# Data utworzenia: 2025-01-23
# Wykonawca: Hubert - SEO Specialist

User-agent: *
Allow: /

# Blokuj niepotrzebne foldery
Disallow: /wp-admin/
Disallow: /wp-includes/
Disallow: /wp-content/plugins/
Disallow: /wp-content/themes/
Disallow: /wp-content/cache/
Disallow: /wp-content/uploads/wc-logs/

# Pozwól na ważne pliki
Allow: /wp-content/uploads/
Allow: /wp-admin/admin-ajax.php

# Blokuj parametry URL
Disallow: /*?*
Disallow: /*#*

# Blokuj strony techniczne
Disallow: /cgi-bin/
Disallow: /wp-login.php
Disallow: /wp-register.php
Disallow: /xmlrpc.php

# Pozwól na sitemaps
Allow: /sitemap*.xml

# Sitemap location
Sitemap: https://firmup.eu/sitemap.xml
Sitemap: https://firmup.eu/sitemap_index.xml

# Crawl-delay dla botów (opcjonalnie)
User-agent: Bingbot
Crawl-delay: 1

User-agent: Slurp
Crawl-delay: 1
EOF
    
    echo -e "${GREEN}✅ Zoptymalizowany robots.txt utworzony${NC}"
    echo ""
}

# Funkcja sprawdzania wyników
check_results() {
    echo -e "${YELLOW}📊 Sprawdzanie wyników wdrożenia...${NC}"
    
    # Sprawdź functions.php
    if grep -q "SEO OPTYMALIZACJA - FIRMUP.EU" "wp-content/themes/oceanwp/functions.php"; then
        echo -e "${GREEN}✅ Functions.php - optymalizacje SEO aktywne${NC}"
    else
        echo -e "${RED}❌ Functions.php - brak optymalizacji SEO${NC}"
    fi
    
    # Sprawdź .htaccess
    if grep -q "SEO BOOST - FIRMUP.EU" ".htaccess"; then
        echo -e "${GREEN}✅ .htaccess - optymalizacje SEO aktywne${NC}"
    else
        echo -e "${RED}❌ .htaccess - brak optymalizacji SEO${NC}"
    fi
    
    # Sprawdź robots.txt
    if [ -f "robots.txt" ] && grep -q "FIRMUP.EU" "robots.txt"; then
        echo -e "${GREEN}✅ robots.txt - zoptymalizowany${NC}"
    else
        echo -e "${YELLOW}⚠️  robots.txt - nie został utworzony lub zoptymalizowany${NC}"
    fi
    
    echo ""
}

# Funkcja czyszczenia
cleanup() {
    echo -e "${YELLOW}🧹 Czyszczenie plików tymczasowych...${NC}"
    
    # Usuń folder SEO po wdrożeniu (opcjonalnie)
    read -p "Czy chcesz usunąć folder SEO-WDROZENIE-FIRMUP-EU? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf "SEO-WDROZENIE-FIRMUP-EU"
        echo -e "${GREEN}✅ Folder SEO-WDROZENIE-FIRMUP-EU usunięty${NC}"
    else
        echo -e "${YELLOW}Folder SEO-WDROZENIE-FIRMUP-EU pozostawiony${NC}"
    fi
    
    echo ""
}

# Funkcja podsumowania
show_summary() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}📊 PODSUMOWANIE WDROŻENIA SEO${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    echo -e "${GREEN}🎉 WDROŻONE OPTYMALIZACJE SEO:${NC}"
    echo ""
    echo -e "${GREEN}✅ Schema Markup:${NC}"
    echo "   • Rich Snippets dla produktów WooCommerce"
    echo "   • Schema dla kursów LearnDash"
    echo "   • Breadcrumbs z Structured Data"
    echo "   • Organization Schema"
    echo ""
    echo -e "${GREEN}✅ Meta Tags:${NC}"
    echo "   • Open Graph dla social media"
    echo "   • Twitter Cards"
    echo "   • Canonical URLs"
    echo "   • Hreflang (jeśli wielojęzyczna)"
    echo ""
    echo -e "${GREEN}✅ Core Web Vitals:${NC}"
    echo "   • Lazy loading z SEO optimization"
    echo "   • Preload krytycznych zasobów"
    echo "   • DNS prefetch"
    echo "   • Image SEO optimization"
    echo ""
    echo -e "${GREEN}✅ Technical SEO:${NC}"
    echo "   • SEO-friendly redirects"
    echo "   • Cache headers"
    echo "   • GZIP compression"
    echo "   • Security headers"
    echo "   • WebP optimization"
    echo "   • Robots.txt optimization"
    echo ""
    echo -e "${YELLOW}📈 OCZEKIWANE REZULTATY:${NC}"
    echo "   • Lepsze pozycje w Google"
    echo "   • Rich snippets w wynikach wyszukiwania"
    echo "   • Wyższy CTR z wyników Google"
    echo "   • Lepsze Core Web Vitals (90+ punktów)"
    echo "   • Szybsze indeksowanie przez Google"
    echo ""
    echo -e "${PURPLE}🔍 NASTĘPNE KROKI:${NC}"
    echo "   1. Sprawdź stronę: https://firmup.eu"
    echo "   2. Test PageSpeed: https://pagespeed.web.dev/"
    echo "   3. Sprawdź Rich Snippets: https://search.google.com/test/rich-results"
    echo "   4. Monitoruj w Google Search Console"
    echo "   5. Sprawdź sitemap: https://firmup.eu/sitemap.xml"
    echo ""
    echo -e "${BLUE}========================================${NC}"
    echo -e "${GREEN}🎉 WDROŻENIE SEO ZAKOŃCZONE POMYŚLNIE!${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
}

# Główna funkcja
main() {
    print_header
    
    echo -e "${YELLOW}⚠️  UWAGA: Ten skrypt zmodyfikuje pliki Twojej strony!${NC}"
    echo -e "${YELLOW}Backup zostanie utworzony automatycznie.${NC}"
    echo ""
    read -p "Czy chcesz kontynuować wdrożenie SEO? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Wdrożenie anulowane${NC}"
        exit 0
    fi
    
    check_requirements
    create_backup
    deploy_functions
    deploy_htaccess
    create_robots
    check_results
    cleanup
    show_summary
    
    echo -e "${GREEN}🎉 WDROŻENIE SEO DLA FIRMUP.EU ZAKOŃCZONE!${NC}"
    echo ""
}

# Uruchom główną funkcję
main "$@"
