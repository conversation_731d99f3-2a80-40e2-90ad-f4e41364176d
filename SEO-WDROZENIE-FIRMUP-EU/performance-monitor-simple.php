<?php
/**
 * 📊 PROSTY MONITOR WYDAJNOŚCI - FIRMUP.EU
 * 
 * Uproszczony skrypt bez zależności od WordPress
 * 
 * UŻYCIE:
 * 1. Wgraj na serwer
 * 2. Otwórz: https://firmup.eu/performance-monitor-simple.php?password=firmup2025
 * 3. USUŃ po testach!
 */

// Proste zabezpieczenie hasłem
$access_password = 'firmup2025'; // ZMIEŃ TO HASŁO!

if (!isset($_GET['password']) || $_GET['password'] !== $access_password) {
    http_response_code(403);
    die('🔒 Dostęp zabroniony. Użyj: ?password=firmup2025');
}

// Rozpocznij pomiary
$start_time = microtime(true);
$start_memory = memory_get_usage();

?>
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitor Wydajności - FIRMUP.EU</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; border-bottom: 2px solid #0073aa; padding-bottom: 20px; margin-bottom: 30px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: #f9f9f9; padding: 20px; border-radius: 8px; border-left: 4px solid #0073aa; }
        .metric-title { font-weight: bold; color: #333; margin-bottom: 10px; font-size: 16px; }
        .metric-value { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .metric-good { color: #46b450; }
        .metric-warning { color: #ffb900; }
        .metric-bad { color: #dc3232; }
        .metric-description { font-size: 12px; color: #666; }
        .test-section { margin-bottom: 30px; }
        .test-title { font-size: 18px; font-weight: bold; color: #333; margin-bottom: 15px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
        .test-result { background: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 10px; }
        .recommendations { background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #0073aa; }
        .recommendations h3 { color: #0073aa; margin-top: 0; }
        .recommendations ul { margin: 10px 0; padding-left: 20px; }
        .recommendations li { margin-bottom: 5px; }
        .refresh-btn { background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .refresh-btn:hover { background: #005a87; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Monitor Wydajności FIRMUP.EU</h1>
            <p>Analiza wydajności serwera i strony</p>
            <button class="refresh-btn" onclick="location.reload()">🔄 Odśwież Testy</button>
        </div>

        <?php
        // =====================================================
        // TESTY WYDAJNOŚCI SERWERA
        // =====================================================

        // Test 1: Informacje o PHP
        $php_version = phpversion();
        $memory_limit = ini_get('memory_limit');
        $max_execution_time = ini_get('max_execution_time');
        $upload_max_filesize = ini_get('upload_max_filesize');

        // Test 2: Test bazy danych (jeśli wp-config.php istnieje)
        $db_time = 0;
        $db_connected = false;
        
        if (file_exists('wp-config.php')) {
            // Spróbuj załadować konfigurację WordPress
            $config_content = file_get_contents('wp-config.php');
            
            // Wyciągnij dane do bazy danych
            preg_match("/define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/", $config_content, $db_name_match);
            preg_match("/define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/", $config_content, $db_user_match);
            preg_match("/define\s*\(\s*['\"]DB_PASSWORD['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/", $config_content, $db_pass_match);
            preg_match("/define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/", $config_content, $db_host_match);
            
            if (isset($db_name_match[1], $db_user_match[1], $db_pass_match[1], $db_host_match[1])) {
                $db_start = microtime(true);
                try {
                    $pdo = new PDO(
                        "mysql:host={$db_host_match[1]};dbname={$db_name_match[1]}", 
                        $db_user_match[1], 
                        $db_pass_match[1],
                        [PDO::ATTR_TIMEOUT => 5]
                    );
                    $pdo->query("SELECT 1");
                    $db_time = (microtime(true) - $db_start) * 1000;
                    $db_connected = true;
                } catch (Exception $e) {
                    $db_time = 0;
                    $db_connected = false;
                }
            }
        }

        // Test 3: Test zapisu pliku
        $file_write_time = 0;
        $file_write_start = microtime(true);
        $test_file = 'test_write_' . time() . '.tmp';
        if (file_put_contents($test_file, 'test')) {
            $file_write_time = (microtime(true) - $file_write_start) * 1000;
            unlink($test_file);
        }

        // Test 4: Pomiar pamięci
        $memory_usage = memory_get_usage(true) / 1024 / 1024;
        $memory_peak = memory_get_peak_usage(true) / 1024 / 1024;

        // Test 5: Czas całkowity
        $total_time = (microtime(true) - $start_time) * 1000;

        // Test 6: Sprawdź rozmiar folderów
        function getFolderSize($dir) {
            $size = 0;
            if (is_dir($dir)) {
                $files = new RecursiveIteratorIterator(
                    new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
                );
                foreach ($files as $file) {
                    $size += $file->getSize();
                }
            }
            return $size;
        }

        $uploads_size = 0;
        if (is_dir('wp-content/uploads')) {
            $uploads_size = getFolderSize('wp-content/uploads') / 1024 / 1024;
        }

        $cache_size = 0;
        if (is_dir('wp-content/cache')) {
            $cache_size = getFolderSize('wp-content/cache') / 1024 / 1024;
        }

        // Funkcje oceny wydajności
        function getPerformanceClass($value, $good, $warning) {
            if ($value <= $good) return 'metric-good';
            if ($value <= $warning) return 'metric-warning';
            return 'metric-bad';
        }

        function getPerformanceText($value, $good, $warning) {
            if ($value <= $good) return 'Doskonale';
            if ($value <= $warning) return 'Do poprawy';
            return 'Wymaga optymalizacji';
        }
        ?>

        <div class="metric-grid">
            <!-- Czas ładowania strony -->
            <div class="metric-card">
                <div class="metric-title">⏱️ Czas ładowania skryptu</div>
                <div class="metric-value <?php echo getPerformanceClass($total_time, 100, 500); ?>">
                    <?php echo number_format($total_time, 2); ?>ms
                </div>
                <div class="metric-description">
                    <?php echo getPerformanceText($total_time, 100, 500); ?> 
                    (Cel: &lt;100ms, Akceptowalne: &lt;500ms)
                </div>
            </div>

            <!-- Czas odpowiedzi bazy danych -->
            <div class="metric-card">
                <div class="metric-title">🗄️ Połączenie z bazą danych</div>
                <div class="metric-value <?php echo $db_connected ? getPerformanceClass($db_time, 50, 200) : 'metric-bad'; ?>">
                    <?php 
                    if ($db_connected) {
                        echo number_format($db_time, 2) . 'ms';
                    } else {
                        echo 'Błąd';
                    }
                    ?>
                </div>
                <div class="metric-description">
                    <?php 
                    if ($db_connected) {
                        echo getPerformanceText($db_time, 50, 200) . ' (Cel: &lt;50ms)';
                    } else {
                        echo 'Nie można połączyć z bazą danych';
                    }
                    ?>
                </div>
            </div>

            <!-- Użycie pamięci -->
            <div class="metric-card">
                <div class="metric-title">💾 Użycie pamięci</div>
                <div class="metric-value <?php echo getPerformanceClass($memory_usage, 32, 64); ?>">
                    <?php echo number_format($memory_usage, 2); ?>MB
                </div>
                <div class="metric-description">
                    Peak: <?php echo number_format($memory_peak, 2); ?>MB / Limit: <?php echo $memory_limit; ?>
                </div>
            </div>

            <!-- Test zapisu pliku -->
            <div class="metric-card">
                <div class="metric-title">💾 Szybkość zapisu pliku</div>
                <div class="metric-value <?php echo getPerformanceClass($file_write_time, 10, 50); ?>">
                    <?php echo number_format($file_write_time, 2); ?>ms
                </div>
                <div class="metric-description">
                    <?php echo getPerformanceText($file_write_time, 10, 50); ?>
                    (Cel: &lt;10ms, Akceptowalne: &lt;50ms)
                </div>
            </div>

            <!-- Rozmiar uploads -->
            <div class="metric-card">
                <div class="metric-title">📁 Rozmiar wp-content/uploads</div>
                <div class="metric-value <?php echo getPerformanceClass($uploads_size, 500, 1000); ?>">
                    <?php echo number_format($uploads_size, 2); ?>MB
                </div>
                <div class="metric-description">
                    <?php echo getPerformanceText($uploads_size, 500, 1000); ?>
                    (Cel: &lt;500MB, Akceptowalne: &lt;1GB)
                </div>
            </div>

            <!-- Informacje o PHP -->
            <div class="metric-card">
                <div class="metric-title">🐘 Konfiguracja PHP</div>
                <div class="metric-value" style="font-size: 16px;">
                    PHP <?php echo $php_version; ?>
                </div>
                <div class="metric-description">
                    Memory: <?php echo $memory_limit; ?> | 
                    Max execution: <?php echo $max_execution_time; ?>s | 
                    Upload: <?php echo $upload_max_filesize; ?>
                </div>
            </div>
        </div>

        <!-- Informacje o plikach -->
        <div class="test-section">
            <div class="test-title">📊 Analiza plików</div>
            
            <?php
            // Sprawdź ważne pliki
            $important_files = [
                '.htaccess' => 'Reguły serwera',
                'robots.txt' => 'Instrukcje dla botów',
                'wp-config.php' => 'Konfiguracja WordPress',
                'wp-content/advanced-cache.php' => 'Cache zaawansowany',
                'wp-content/object-cache.php' => 'Cache obiektów'
            ];

            foreach ($important_files as $file => $description) {
                $exists = file_exists($file);
                $size = $exists ? filesize($file) : 0;
                echo '<div class="test-result">';
                echo '<strong>' . ($exists ? '✅' : '❌') . ' ' . $file . '</strong> - ' . $description;
                if ($exists) {
                    echo ' (' . number_format($size / 1024, 2) . ' KB)';
                }
                echo '</div>';
            }
            ?>
        </div>

        <!-- Rekomendacje -->
        <div class="recommendations">
            <h3>🎯 Rekomendacje optymalizacji</h3>
            
            <?php if ($total_time > 500): ?>
            <div class="test-result">
                <strong>⚠️ Wolne ładowanie skryptu (<?php echo number_format($total_time, 2); ?>ms)</strong>
                <ul>
                    <li>Sprawdź obciążenie serwera</li>
                    <li>Zoptymalizuj konfigurację PHP</li>
                    <li>Rozważ upgrade hostingu</li>
                </ul>
            </div>
            <?php endif; ?>

            <?php if (!$db_connected): ?>
            <div class="test-result">
                <strong>❌ Brak połączenia z bazą danych</strong>
                <ul>
                    <li>Sprawdź konfigurację w wp-config.php</li>
                    <li>Sprawdź czy serwer MySQL działa</li>
                    <li>Skontaktuj się z hostingiem</li>
                </ul>
            </div>
            <?php elseif ($db_time > 200): ?>
            <div class="test-result">
                <strong>⚠️ Wolna baza danych (<?php echo number_format($db_time, 2); ?>ms)</strong>
                <ul>
                    <li>Zoptymalizuj tabele bazy danych</li>
                    <li>Wyczyść niepotrzebne dane</li>
                    <li>Rozważ cache bazy danych</li>
                </ul>
            </div>
            <?php endif; ?>

            <?php if ($memory_usage > 64): ?>
            <div class="test-result">
                <strong>⚠️ Wysokie użycie pamięci (<?php echo number_format($memory_usage, 2); ?>MB)</strong>
                <ul>
                    <li>Sprawdź aktywne pluginy</li>
                    <li>Zwiększ limit pamięci PHP</li>
                    <li>Zoptymalizuj kod</li>
                </ul>
            </div>
            <?php endif; ?>

            <?php if ($uploads_size > 1000): ?>
            <div class="test-result">
                <strong>⚠️ Duży folder uploads (<?php echo number_format($uploads_size, 2); ?>MB)</strong>
                <ul>
                    <li>Skompresuj obrazy</li>
                    <li>Usuń niepotrzebne pliki</li>
                    <li>Rozważ CDN</li>
                </ul>
            </div>
            <?php endif; ?>

            <?php if ($total_time <= 100 && $db_connected && $db_time <= 50 && $memory_usage <= 32): ?>
            <div class="test-result">
                <strong style="color: #46b450;">✅ Doskonała wydajność!</strong>
                <p>Serwer działa optymalnie. Wszystkie metryki są w zielonych zakresach.</p>
            </div>
            <?php endif; ?>
        </div>

        <div class="warning">
            <strong>⚠️ WAŻNE BEZPIECZEŃSTWO:</strong><br>
            Ten plik zawiera wrażliwe informacje o serwerze. <strong>USUŃ GO</strong> po zakończeniu testów!<br>
            Plik: <code><?php echo basename(__FILE__); ?></code> | 
            Wygenerowano: <?php echo date('Y-m-d H:i:s'); ?>
        </div>
    </div>
</body>
</html>
