# ========================================
# SEO BOOST - FIRMUP.EU
# ========================================
# 
# Zaawansowane reguły .htaccess dla SEO
# Data wdrożenia: 2025-01-23
# 
# INSTRUKCJA:
# 1. Zrób backup obecnego .htaccess
# 2. Dodaj poniższy kod na KOŃCU pliku .htaccess
# 3. Nie zastępuj całego pliku!
# ========================================

# SEO FRIENDLY REDIRECTS
<IfModule mod_rewrite.c>
RewriteEngine On

# Przekieruj www na non-www (lub odwrotnie - wy<PERSON>rz jedno)
# RewriteCond %{HTTP_HOST} ^www\.firmup\.eu [NC]
# RewriteRule ^(.*)$ https://firmup.eu/$1 [R=301,L]

# Przekieruj HTTP na HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

# Usuń trailing slash z URL (lepsze dla SEO)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} (.+)/$
RewriteRule ^(.+)/$ /$1 [R=301,L]

# Clean URLs dla produktów WooCommerce
RewriteRule ^produkt/([^/]+)/?$ /product/$1 [R=301,L]
RewriteRule ^kategoria/([^/]+)/?$ /product-category/$1 [R=301,L]

# Clean URLs dla kursów LearnDash
RewriteRule ^kurs/([^/]+)/?$ /courses/$1 [R=301,L]
RewriteRule ^lekcja/([^/]+)/?$ /lessons/$1 [R=301,L]
</IfModule>

# CACHE HEADERS DLA SEO
<IfModule mod_expires.c>
ExpiresActive On

# HTML - krótki cache dla fresh content
ExpiresByType text/html "access plus 1 hour"

# CSS i JavaScript - długi cache z versioning
ExpiresByType text/css "access plus 1 year"
ExpiresByType application/javascript "access plus 1 year"
ExpiresByType application/x-javascript "access plus 1 year"

# Obrazy - długi cache dla lepszego PageSpeed
ExpiresByType image/png "access plus 1 year"
ExpiresByType image/jpg "access plus 1 year"
ExpiresByType image/jpeg "access plus 1 year"
ExpiresByType image/webp "access plus 1 year"
ExpiresByType image/gif "access plus 1 year"
ExpiresByType image/svg+xml "access plus 1 year"

# Czcionki - długi cache
ExpiresByType font/woff "access plus 1 year"
ExpiresByType font/woff2 "access plus 1 year"
ExpiresByType application/font-woff "access plus 1 year"
ExpiresByType application/font-woff2 "access plus 1 year"

# XML Sitemaps - cache na 1 dzień
ExpiresByType application/xml "access plus 1 day"
ExpiresByType text/xml "access plus 1 day"
</IfModule>

# GZIP COMPRESSION DLA LEPSZEGO PAGESPEED
<IfModule mod_deflate.c>
# Kompresja tekstów
AddOutputFilterByType DEFLATE text/plain
AddOutputFilterByType DEFLATE text/html
AddOutputFilterByType DEFLATE text/xml
AddOutputFilterByType DEFLATE text/css
AddOutputFilterByType DEFLATE text/javascript

# Kompresja aplikacji
AddOutputFilterByType DEFLATE application/xml
AddOutputFilterByType DEFLATE application/xhtml+xml
AddOutputFilterByType DEFLATE application/rss+xml
AddOutputFilterByType DEFLATE application/javascript
AddOutputFilterByType DEFLATE application/x-javascript
AddOutputFilterByType DEFLATE application/json
AddOutputFilterByType DEFLATE application/ld+json

# Kompresja SVG
AddOutputFilterByType DEFLATE image/svg+xml

# Wyłącz kompresję dla starych przeglądarek
BrowserMatch ^Mozilla/4 gzip-only-text/html
BrowserMatch ^Mozilla/4\.0[678] no-gzip
BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
</IfModule>

# SECURITY HEADERS DLA SEO I BEZPIECZEŃSTWA
<IfModule mod_headers.c>
# Bezpieczeństwo
Header always set X-Content-Type-Options "nosniff"
Header always set X-Frame-Options "SAMEORIGIN"
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Content Security Policy (podstawowy)
Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' *.firmup.eu *.googleapis.com *.gstatic.com *.google-analytics.com *.googletagmanager.com *.facebook.com *.facebook.net"

# Cache Control dla lepszego SEO
<FilesMatch "\.(css|js|png|jpg|jpeg|webp|gif|ico|svg|woff|woff2|ttf|otf|eot)$">
Header set Cache-Control "max-age=31536000, public, immutable"
Header append Vary "Accept-Encoding"
</FilesMatch>

<FilesMatch "\.(html|htm)$">
Header set Cache-Control "max-age=3600, public"
</FilesMatch>

# Usuń ETag (nie potrzebne z Cache-Control)
Header unset ETag
FileETag None
</IfModule>

# WEBP OPTIMIZATION DLA LEPSZEGO PAGESPEED
<IfModule mod_rewrite.c>
RewriteEngine On

# Serve WebP images if available and supported
RewriteCond %{HTTP_ACCEPT} image/webp
RewriteCond %{REQUEST_FILENAME} \.(jpe?g|png)$
RewriteCond %{REQUEST_FILENAME}.webp -f
RewriteRule ^(.+)\.(jpe?g|png)$ $1.$2.webp [T=image/webp,E=accept:1,L]

# Add Vary header for WebP
Header append Vary Accept env=WEBP
</IfModule>

# MIME TYPES DLA NOWOCZESNYCH FORMATÓW
<IfModule mod_mime.c>
# WebP images
AddType image/webp .webp

# Modern fonts
AddType font/woff .woff
AddType font/woff2 .woff2

# JSON-LD dla Schema Markup
AddType application/ld+json .jsonld
</IfModule>

# BLOKOWANIE SZKODLIWYCH BOTÓW (SEO Protection)
<IfModule mod_rewrite.c>
RewriteEngine On

# Blokuj znane złe boty
RewriteCond %{HTTP_USER_AGENT} (semrushbot|ahrefsbot|mj12bot|dotbot) [NC]
RewriteRule .* - [F,L]

# Blokuj scraping tools
RewriteCond %{HTTP_USER_AGENT} (scrapy|wget|curl) [NC]
RewriteCond %{HTTP_USER_AGENT} !googlebot [NC]
RewriteRule .* - [F,L]

# Chroń wp-config.php
RewriteRule ^wp-config\.php$ - [F,L]

# Chroń .htaccess
RewriteRule ^\.htaccess$ - [F,L]

# Blokuj dostęp do logów
RewriteRule \.(log|txt)$ - [F,L]
</IfModule>

# OPTYMALIZACJA POŁĄCZEŃ DLA WYDAJNOŚCI
<IfModule mod_headers.c>
# Keep-Alive dla lepszej wydajności
Header set Connection "Keep-Alive"
Header set Keep-Alive "timeout=5, max=100"

# Preload headers dla krytycznych zasobów
<FilesMatch "style\.css$">
Header set Link "</wp-content/themes/oceanwp/style.css>; rel=preload; as=style"
</FilesMatch>

# DNS prefetch headers
Header set Link "<//fonts.googleapis.com>; rel=dns-prefetch, <//www.google-analytics.com>; rel=dns-prefetch"
</IfModule>

# SITEMAP OPTIMIZATION
<IfModule mod_rewrite.c>
# Redirect old sitemap URLs to new ones
RewriteRule ^sitemap_index\.xml$ /sitemap.xml [R=301,L]
RewriteRule ^sitemap\.xml\.gz$ /sitemap.xml [R=301,L]

# Ensure sitemaps are served with correct content-type
RewriteRule ^sitemap.*\.xml$ - [E=no-gzip:1]
</IfModule>

# ROBOTS.TXT OPTIMIZATION
<IfModule mod_rewrite.c>
# Serve robots.txt with correct content-type
RewriteRule ^robots\.txt$ - [T=text/plain,L]
</IfModule>

# PERFORMANCE OPTIMIZATION DLA CORE WEB VITALS
<IfModule mod_headers.c>
# Early hints dla krytycznych zasobów
<FilesMatch "\.(css|js)$">
Header set Link "<%{REQUEST_URI}s>; rel=preload; as=style" "expr=%{REQUEST_URI} =~ /style\.css/"
Header set Link "<%{REQUEST_URI}s>; rel=preload; as=script" "expr=%{REQUEST_URI} =~ /main\.js/"
</FilesMatch>

# Resource hints dla lepszego UX
Header set Link "<//fonts.googleapis.com>; rel=preconnect; crossorigin"
Header set Link "<//www.google-analytics.com>; rel=preconnect"
</IfModule>

# CANONICAL URL ENFORCEMENT
<IfModule mod_rewrite.c>
# Enforce canonical domain (wybierz www lub non-www)
# RewriteCond %{HTTP_HOST} !^firmup\.eu$ [NC]
# RewriteRule ^(.*)$ https://firmup.eu/$1 [R=301,L]

# Remove index.php from URLs
RewriteCond %{THE_REQUEST} /index\.php [NC]
RewriteRule ^index\.php/?(.*)$ /$1 [R=301,L]
</IfModule>

# STRUCTURED DATA OPTIMIZATION
<IfModule mod_headers.c>
# Ensure JSON-LD is served with correct content-type
<FilesMatch "\.jsonld$">
Header set Content-Type "application/ld+json; charset=utf-8"
</FilesMatch>
</IfModule>

# ========================================
# KOŃCOWE OPTYMALIZACJE SEO
# ========================================

# Wyłącz server signature dla bezpieczeństwa
ServerSignature Off

# Ustaw domyślny charset
AddDefaultCharset UTF-8

# Optymalizacja dla mobile-first indexing
<IfModule mod_headers.c>
Header set Viewport "width=device-width, initial-scale=1"
</IfModule>

# ========================================
# KONIEC OPTYMALIZACJI SEO .HTACCESS
# Data wdrożenia: 2025-01-23
# Wykonawca: Hubert - SEO Specialist
# ========================================
