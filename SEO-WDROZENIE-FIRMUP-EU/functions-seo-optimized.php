<?php
/**
 * ========================================
 * SEO OPTYMALIZACJA - FIRMUP.EU
 * ========================================
 * 
 * Zaawansowane optymalizacje SEO dla lepszego pozycjonowania
 * Data wdrożenia: 2025-01-23
 * 
 * ZAWIERA:
 * ✅ Schema Markup dla WooCommerce
 * ✅ Schema Markup dla LearnDash
 * ✅ Open Graph i Twitter Cards
 * ✅ Breadcrumbs z Structured Data
 * ✅ Core Web Vitals Optimization
 * ✅ Meta Tags Optimization
 * ✅ Image SEO Optimization
 * ✅ Performance SEO Boosts
 */

// ========================================
// SCHEMA MARKUP - WOOCOMMERCE PRODUKTY
// ========================================

/**
 * 🎯 SCHEMA MARKUP DLA PRODUKTÓW WOOCOMMERCE
 * Rich Snippets w wynikach Google - wyższe CTR
 */
add_action('wp_head', function() {
    if (is_product()) {
        global $product;
        if ($product) {
            $schema = array(
                '@context' => 'https://schema.org/',
                '@type' => 'Product',
                'name' => $product->get_name(),
                'description' => wp_strip_all_tags($product->get_short_description() ?: $product->get_description()),
                'sku' => $product->get_sku(),
                'brand' => array(
                    '@type' => 'Brand',
                    'name' => get_bloginfo('name')
                ),
                'offers' => array(
                    '@type' => 'Offer',
                    'price' => $product->get_price(),
                    'priceCurrency' => get_woocommerce_currency(),
                    'availability' => $product->is_in_stock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
                    'url' => get_permalink(),
                    'seller' => array(
                        '@type' => 'Organization',
                        'name' => get_bloginfo('name')
                    )
                )
            );
            
            // Dodaj obrazek produktu
            if ($product->get_image_id()) {
                $schema['image'] = wp_get_attachment_url($product->get_image_id());
            }
            
            // Dodaj kategorie
            $categories = get_the_terms($product->get_id(), 'product_cat');
            if ($categories && !is_wp_error($categories)) {
                $schema['category'] = $categories[0]->name;
            }
            
            // Dodaj oceny jeśli dostępne
            if ($product->get_average_rating()) {
                $schema['aggregateRating'] = array(
                    '@type' => 'AggregateRating',
                    'ratingValue' => $product->get_average_rating(),
                    'reviewCount' => $product->get_review_count()
                );
            }
            
            echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
        }
    }
    
    // Schema dla strony sklepu
    if (is_shop()) {
        $schema = array(
            '@context' => 'https://schema.org/',
            '@type' => 'WebSite',
            'name' => get_bloginfo('name'),
            'url' => home_url(),
            'description' => get_bloginfo('description'),
            'potentialAction' => array(
                '@type' => 'SearchAction',
                'target' => array(
                    '@type' => 'EntryPoint',
                    'urlTemplate' => home_url('/?s={search_term_string}&post_type=product')
                ),
                'query-input' => 'required name=search_term_string'
            )
        );
        echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
    }
});

// ========================================
// SCHEMA MARKUP - LEARNDASH KURSY
// ========================================

/**
 * 🎯 SCHEMA MARKUP DLA KURSÓW LEARNDASH
 * Rich Snippets dla kursów online
 */
add_action('wp_head', function() {
    if (get_post_type() === 'sfwd-courses') {
        global $post;
        $course_id = $post->ID;
        
        $schema = array(
            '@context' => 'https://schema.org/',
            '@type' => 'Course',
            'name' => get_the_title(),
            'description' => wp_strip_all_tags(get_the_excerpt() ?: get_the_content()),
            'provider' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'url' => home_url(),
                'sameAs' => array(
                    'https://www.facebook.com/firmup.eu',
                    'https://www.linkedin.com/company/firmup'
                )
            ),
            'url' => get_permalink(),
            'courseMode' => 'online',
            'educationalLevel' => 'professional'
        );
        
        // Dodaj cenę kursu jeśli dostępna
        if (function_exists('learndash_get_course_price')) {
            $course_price = learndash_get_course_price($course_id);
            if ($course_price && isset($course_price['price']) && $course_price['price'] > 0) {
                $schema['offers'] = array(
                    '@type' => 'Offer',
                    'price' => $course_price['price'],
                    'priceCurrency' => 'PLN',
                    'availability' => 'https://schema.org/InStock'
                );
            }
        }
        
        // Dodaj instruktora jeśli dostępny
        $author_id = get_post_field('post_author', $course_id);
        if ($author_id) {
            $schema['instructor'] = array(
                '@type' => 'Person',
                'name' => get_the_author_meta('display_name', $author_id),
                'description' => get_the_author_meta('description', $author_id)
            );
        }
        
        // Dodaj obrazek kursu
        if (has_post_thumbnail()) {
            $schema['image'] = get_the_post_thumbnail_url($course_id, 'large');
        }
        
        echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
    }
});

// ========================================
// BREADCRUMBS Z SCHEMA MARKUP
// ========================================

/**
 * 🔍 BREADCRUMBS Z STRUCTURED DATA
 * Lepsze zrozumienie struktury strony przez Google
 */
add_action('wp_head', function() {
    if (is_singular() && !is_front_page()) {
        $breadcrumbs = array();
        $position = 1;
        
        // Strona główna
        $breadcrumbs[] = array(
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => 'Strona główna',
            'item' => home_url()
        );
        
        // Kategoria produktu
        if (is_product()) {
            $terms = get_the_terms(get_the_ID(), 'product_cat');
            if ($terms && !is_wp_error($terms)) {
                $term = array_shift($terms);
                $breadcrumbs[] = array(
                    '@type' => 'ListItem',
                    'position' => $position++,
                    'name' => $term->name,
                    'item' => get_term_link($term)
                );
            }
            
            $breadcrumbs[] = array(
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => 'Sklep',
                'item' => get_permalink(wc_get_page_id('shop'))
            );
        }
        
        // Kategoria kursu LearnDash
        if (get_post_type() === 'sfwd-courses') {
            $breadcrumbs[] = array(
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => 'Kursy',
                'item' => home_url('/kursy/')
            );
        }
        
        // Obecna strona
        $breadcrumbs[] = array(
            '@type' => 'ListItem',
            'position' => $position,
            'name' => get_the_title(),
            'item' => get_permalink()
        );
        
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $breadcrumbs
        );
        
        echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
    }
});

// ========================================
// OPEN GRAPH I TWITTER CARDS
// ========================================

/**
 * 🔍 ZAAWANSOWANE META TAGI SOCIAL MEDIA
 * Lepsze udostępnianie w social media
 */
add_action('wp_head', function() {
    // Open Graph dla produktów
    if (is_product()) {
        global $product;
        echo '<meta property="og:type" content="product">' . "\n";
        echo '<meta property="og:title" content="' . esc_attr($product->get_name()) . '">' . "\n";
        echo '<meta property="og:description" content="' . esc_attr(wp_strip_all_tags($product->get_short_description())) . '">' . "\n";
        echo '<meta property="og:price:amount" content="' . $product->get_price() . '">' . "\n";
        echo '<meta property="og:price:currency" content="' . get_woocommerce_currency() . '">' . "\n";
        echo '<meta property="product:availability" content="' . ($product->is_in_stock() ? 'in stock' : 'out of stock') . '">' . "\n";
        
        if ($product->get_image_id()) {
            $image_url = wp_get_attachment_url($product->get_image_id());
            echo '<meta property="og:image" content="' . esc_url($image_url) . '">' . "\n";
            echo '<meta property="og:image:width" content="1200">' . "\n";
            echo '<meta property="og:image:height" content="630">' . "\n";
        }
    }
    
    // Open Graph dla kursów
    if (get_post_type() === 'sfwd-courses') {
        echo '<meta property="og:type" content="article">' . "\n";
        echo '<meta property="article:section" content="Edukacja">' . "\n";
        echo '<meta property="article:author" content="' . get_the_author() . '">' . "\n";
    }
    
    // Twitter Cards
    echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
    echo '<meta name="twitter:site" content="@firmup_eu">' . "\n";
    echo '<meta name="twitter:creator" content="@firmup_eu">' . "\n";
    
    // Canonical URLs
    if (is_singular()) {
        echo '<link rel="canonical" href="' . get_permalink() . '">' . "\n";
    }
    
    // Hreflang dla wielojęzyczności (jeśli używane)
    if (function_exists('pll_the_languages')) {
        $languages = pll_the_languages(array('raw' => 1));
        foreach ($languages as $lang) {
            echo '<link rel="alternate" hreflang="' . $lang['locale'] . '" href="' . $lang['url'] . '">' . "\n";
        }
    }
}, 1);

// ========================================
// CORE WEB VITALS OPTIMIZATION
// ========================================

/**
 * 🚀 OPTYMALIZACJA CORE WEB VITALS
 * Lepsze wyniki w PageSpeed Insights
 */

// Lazy loading z SEO optimization
add_filter('wp_get_attachment_image_attributes', function($attr, $attachment, $size) {
    static $first_image = true;
    
    // Pierwszy obraz - priorytet (LCP)
    if ($first_image) {
        $first_image = false;
        $attr['fetchpriority'] = 'high';
        $attr['loading'] = 'eager';
        return $attr;
    }
    
    // Pozostałe obrazy - lazy loading
    $attr['loading'] = 'lazy';
    $attr['decoding'] = 'async';
    
    // Dodaj alt text jeśli brakuje (SEO)
    if (empty($attr['alt'])) {
        $alt_text = get_post_meta($attachment->ID, '_wp_attachment_image_alt', true);
        if ($alt_text) {
            $attr['alt'] = $alt_text;
        } else {
            // Generuj alt text z nazwy pliku
            $attr['alt'] = sanitize_text_field(pathinfo(get_attached_file($attachment->ID), PATHINFO_FILENAME));
        }
    }
    
    return $attr;
}, 10, 3);

// Preload krytycznych zasobów
add_action('wp_head', function() {
    // Preload krytycznego CSS
    echo '<link rel="preload" href="' . get_template_directory_uri() . '/style.css" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";
    
    // Preload głównej czcionki
    $font_files = glob(ABSPATH . 'wp-content/uploads/oceanwp-webfonts/*.woff2');
    if (!empty($font_files)) {
        $font_url = str_replace(ABSPATH, home_url('/'), $font_files[0]);
        echo '<link rel="preload" href="' . $font_url . '" as="font" type="font/woff2" crossorigin>' . "\n";
    }
    
    // DNS prefetch dla zewnętrznych domen
    echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">' . "\n";
    echo '<link rel="dns-prefetch" href="//www.google-analytics.com">' . "\n";
    echo '<link rel="dns-prefetch" href="//www.googletagmanager.com">' . "\n";
}, 1);

// ========================================
// IMAGE SEO OPTIMIZATION
// ========================================

/**
 * 📸 AUTOMATYCZNA OPTYMALIZACJA SEO OBRAZÓW
 * Lepsze pozycjonowanie w Google Images
 */
add_filter('wp_get_attachment_image_attributes', function($attr, $attachment, $size) {
    $attachment_id = $attachment->ID;
    
    // Dodaj srcset dla responsive images
    $srcset = wp_get_attachment_image_srcset($attachment_id, $size);
    if ($srcset) {
        $attr['srcset'] = $srcset;
        $attr['sizes'] = wp_get_attachment_image_sizes($attachment_id, $size);
    }
    
    // Popraw alt text dla SEO
    if (empty($attr['alt'])) {
        $alt_text = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);
        if (empty($alt_text)) {
            // Generuj alt text z kontekstu
            $post = get_post($attachment_id);
            if ($post) {
                $parent_post = get_post($post->post_parent);
                if ($parent_post) {
                    $attr['alt'] = $parent_post->post_title . ' - ' . get_bloginfo('name');
                } else {
                    $attr['alt'] = $post->post_title ?: sanitize_text_field(pathinfo($post->guid, PATHINFO_FILENAME));
                }
            }
        } else {
            $attr['alt'] = $alt_text;
        }
    }
    
    // Dodaj title dla lepszego UX
    if (empty($attr['title']) && !empty($attr['alt'])) {
        $attr['title'] = $attr['alt'];
    }
    
    return $attr;
}, 10, 3);

// ========================================
// PERFORMANCE SEO BOOSTS
// ========================================

/**
 * ⚡ DODATKOWE OPTYMALIZACJE WYDAJNOŚCI DLA SEO
 * Szybsza strona = lepsze pozycje w Google
 */

// Optymalizacja Heartbeat API
add_filter('heartbeat_settings', function($settings) {
    $settings['interval'] = 60; // 60 sekund zamiast 15
    return $settings;
});

// Wyłącz heartbeat na front-endzie
add_action('init', function() {
    if (!is_admin()) {
        wp_deregister_script('heartbeat');
    }
});

// Usuń niepotrzebne zasoby
remove_action('wp_head', 'print_emoji_detection_script', 7);
remove_action('wp_print_styles', 'print_emoji_styles');
remove_action('wp_head', 'wp_generator');
remove_action('wp_head', 'wlwmanifest_link');
remove_action('wp_head', 'rsd_link');
remove_action('wp_head', 'wp_shortlink_wp_head');

// Wyłącz REST API dla niezalogowanych (bezpieczeństwo)
add_filter('rest_authentication_errors', function($result) {
    if (!is_user_logged_in()) {
        return new WP_Error('rest_not_logged_in', 'API requires authentication.', array('status' => 401));
    }
    return $result;
});

// ========================================
// ORGANIZATION SCHEMA
// ========================================

/**
 * 🏢 SCHEMA MARKUP DLA ORGANIZACJI
 * Lepsze pozycjonowanie lokalne
 */
add_action('wp_head', function() {
    if (is_front_page()) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => get_bloginfo('name'),
            'url' => home_url(),
            'description' => get_bloginfo('description'),
            'logo' => array(
                '@type' => 'ImageObject',
                'url' => get_site_icon_url(512)
            ),
            'sameAs' => array(
                'https://www.facebook.com/firmup.eu',
                'https://www.linkedin.com/company/firmup',
                'https://twitter.com/firmup_eu'
            ),
            'contactPoint' => array(
                '@type' => 'ContactPoint',
                'contactType' => 'customer service',
                'email' => '<EMAIL>',
                'availableLanguage' => 'Polish'
            )
        );
        
        echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
    }
});

/**
 * ========================================
 * KONIEC OPTYMALIZACJI SEO
 * ========================================
 * 
 * Wszystkie powyższe optymalizacje zostały wdrożone
 * w celu poprawy pozycjonowania strony FIRMUP.EU
 * 
 * Data wdrożenia: 2025-01-23
 * Wykonawca: Hubert - SEO Specialist
 * ========================================
 */
