<?php
/**
 * Skrypt do czyszczenia bazy danych WordPress
 * Optymalizacja wydajności dla firmup.eu
 * Data: 2025-01-23
 */

// Załaduj WordPress
define('WP_USE_THEMES', false);
require_once(dirname(__FILE__) . '/wp-load.php');

// Zabezpieczenie - uruchom tylko przez administratora
if (!current_user_can('administrator') && !defined('WP_CLI')) {
    die('Brak uprawnień do uruchomienia tego skryptu');
}

global $wpdb;

echo "=== OPTYMALIZACJA BAZY DANYCH FIRMUP.EU ===\n";
echo "Data: " . date('Y-m-d H:i:s') . "\n\n";

// 1. Sprawdź obecny stan bazy danych
echo "1. SPRAWDZANIE OBECNEGO STANU BAZY DANYCH...\n";

$stats = array();

// Rewizje postów
$stats['revisions'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'revision'");

// Auto-drafts
$stats['auto_drafts'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'auto-draft'");

// Posty w koszu
$stats['trash_posts'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'trash'");

// Spam komentarze
$stats['spam_comments'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->comments} WHERE comment_approved = 'spam'");

// Komentarze w koszu
$stats['trash_comments'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->comments} WHERE comment_approved = 'trash'");

// Orphaned postmeta
$stats['orphan_postmeta'] = $wpdb->get_var("
    SELECT COUNT(*) FROM {$wpdb->postmeta} pm 
    LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID 
    WHERE p.ID IS NULL
");

// Orphaned commentmeta
$stats['orphan_commentmeta'] = $wpdb->get_var("
    SELECT COUNT(*) FROM {$wpdb->commentmeta} cm 
    LEFT JOIN {$wpdb->comments} c ON cm.comment_id = c.comment_ID 
    WHERE c.comment_ID IS NULL
");

// Expired transients
$stats['expired_transients'] = $wpdb->get_var("
    SELECT COUNT(*) FROM {$wpdb->options} 
    WHERE option_name LIKE '_transient_timeout_%' 
    AND option_value < UNIX_TIMESTAMP()
");

// Wyświetl statystyki
foreach ($stats as $type => $count) {
    echo "   - " . ucfirst(str_replace('_', ' ', $type)) . ": {$count}\n";
}

echo "\n2. ROZPOCZYNANIE CZYSZCZENIA...\n";

$cleaned = array();

// Czyść tylko jeśli są elementy do wyczyszczenia
if ($stats['revisions'] > 0) {
    echo "   Usuwanie rewizji postów...\n";
    $cleaned['revisions'] = $wpdb->query("DELETE FROM {$wpdb->posts} WHERE post_type = 'revision'");
    echo "   Usunięto: {$cleaned['revisions']} rewizji\n";
}

if ($stats['auto_drafts'] > 0) {
    echo "   Usuwanie auto-drafts...\n";
    $cleaned['auto_drafts'] = $wpdb->query("DELETE FROM {$wpdb->posts} WHERE post_status = 'auto-draft' AND post_modified < DATE_SUB(NOW(), INTERVAL 7 DAY)");
    echo "   Usunięto: {$cleaned['auto_drafts']} auto-drafts\n";
}

if ($stats['trash_posts'] > 0) {
    echo "   Usuwanie postów z kosza...\n";
    $cleaned['trash_posts'] = $wpdb->query("DELETE FROM {$wpdb->posts} WHERE post_status = 'trash' AND post_modified < DATE_SUB(NOW(), INTERVAL 7 DAY)");
    echo "   Usunięto: {$cleaned['trash_posts']} postów z kosza\n";
}

if ($stats['spam_comments'] > 0) {
    echo "   Usuwanie spam komentarzy...\n";
    $cleaned['spam_comments'] = $wpdb->query("DELETE FROM {$wpdb->comments} WHERE comment_approved = 'spam' AND comment_date < DATE_SUB(NOW(), INTERVAL 7 DAY)");
    echo "   Usunięto: {$cleaned['spam_comments']} spam komentarzy\n";
}

if ($stats['trash_comments'] > 0) {
    echo "   Usuwanie komentarzy z kosza...\n";
    $cleaned['trash_comments'] = $wpdb->query("DELETE FROM {$wpdb->comments} WHERE comment_approved = 'trash' AND comment_date < DATE_SUB(NOW(), INTERVAL 7 DAY)");
    echo "   Usunięto: {$cleaned['trash_comments']} komentarzy z kosza\n";
}

if ($stats['orphan_postmeta'] > 0) {
    echo "   Usuwanie orphaned postmeta...\n";
    $cleaned['orphan_postmeta'] = $wpdb->query("
        DELETE pm FROM {$wpdb->postmeta} pm 
        LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID 
        WHERE p.ID IS NULL
    ");
    echo "   Usunięto: {$cleaned['orphan_postmeta']} orphaned postmeta\n";
}

if ($stats['orphan_commentmeta'] > 0) {
    echo "   Usuwanie orphaned commentmeta...\n";
    $cleaned['orphan_commentmeta'] = $wpdb->query("
        DELETE cm FROM {$wpdb->commentmeta} cm 
        LEFT JOIN {$wpdb->comments} c ON cm.comment_id = c.comment_ID 
        WHERE c.comment_ID IS NULL
    ");
    echo "   Usunięto: {$cleaned['orphan_commentmeta']} orphaned commentmeta\n";
}

if ($stats['expired_transients'] > 0) {
    echo "   Usuwanie expired transients...\n";
    // Usuń expired transients
    $expired_transients = $wpdb->get_col("
        SELECT option_name FROM {$wpdb->options} 
        WHERE option_name LIKE '_transient_timeout_%' 
        AND option_value < UNIX_TIMESTAMP()
    ");
    
    foreach ($expired_transients as $transient) {
        $transient_name = str_replace('_transient_timeout_', '', $transient);
        delete_transient($transient_name);
    }
    $cleaned['expired_transients'] = count($expired_transients);
    echo "   Usunięto: {$cleaned['expired_transients']} expired transients\n";
}

echo "\n3. OPTYMALIZACJA TABEL...\n";

// Pobierz wszystkie tabele WordPress
$tables = $wpdb->get_col("SHOW TABLES LIKE '{$wpdb->prefix}%'");

foreach ($tables as $table) {
    echo "   Optymalizacja tabeli: {$table}...\n";
    $wpdb->query("OPTIMIZE TABLE {$table}");
}

echo "\n=== CZYSZCZENIE ZAKOŃCZONE ===\n";
echo "Podsumowanie:\n";
$total_cleaned = array_sum($cleaned);
echo "Łącznie usunięto: {$total_cleaned} elementów\n";
echo "Zoptymalizowano: " . count($tables) . " tabel\n";
echo "Data zakończenia: " . date('Y-m-d H:i:s') . "\n";

// Wyczyść cache po optymalizacji
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo "Cache został wyczyszczony\n";
}

echo "\nREKOMENDACJE:\n";
echo "1. Sprawdź działanie strony po optymalizacji\n";
echo "2. Uruchom ten skrypt regularnie (raz w miesiącu)\n";
echo "3. Monitoruj wydajność przez następne 24h\n";

?>
