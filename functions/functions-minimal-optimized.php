<?php
/**
 * ========================================
 * MINIMALNE OPTYMALIZACJE WYDAJNOŚCI - FIRMUP.EU
 * Data: 2025-01-23
 * Wykonawca: Hubert - SEO Specialist
 * ========================================
 * 
 * Ten kod dodaj na KOŃCU oryginalnego pliku functions.php
 * Zawiera tylko sprawdzone optymalizacje, które nie wpływają negatywnie na PageSpeed
 */

// ========================================
// PODSTAWOWE OPTYMALIZACJE WYDAJNOŚCI
// ========================================

/**
 * 🔧 OPTYMALIZACJA HEARTBEAT API
 * Zmniejszenie częstotliwości heartbeat dla lepszej wyd<PERSON>ci
 */
add_filter('heartbeat_settings', function($settings) {
    $settings['interval'] = 60; // 60 sekund zamiast 15
    return $settings;
});

/**
 * 🔧 WYŁĄCZENIE HEARTBEAT NA FRONT-ENDZIE
 * Heartbeat nie jest potrzebny na stronie głównej
 */
add_action('init', function() {
    if (!is_admin()) {
        wp_deregister_script('heartbeat');
    }
});

/**
 * 🔧 USUŃ NIEPOTRZEBNE ZASOBY WP
 * Zmniejszenie obciążenia serwera
 */
// Wyłącz emoji (nie wpływa na PageSpeed negatywnie)
remove_action('wp_head', 'print_emoji_detection_script', 7);
remove_action('wp_print_styles', 'print_emoji_styles');

// Wyłącz generator meta tag (bezpieczeństwo)
remove_action('wp_head', 'wp_generator');

// Wyłącz RSD link (niepotrzebny)
remove_action('wp_head', 'rsd_link');

// Wyłącz wlwmanifest link (niepotrzebny)
remove_action('wp_head', 'wlwmanifest_link');

/**
 * 🔧 OPTYMALIZACJA AUTOSAVE
 * Zmniejszenie częstotliwości autozapisu
 */
add_filter('autosave_interval', function() {
    return 300; // 5 minut zamiast 60 sekund
});

/**
 * 🔧 WYŁĄCZENIE PINGBACKS I TRACKBACKS
 * Zmniejszenie spam i obciążenia serwera
 */
add_filter('xmlrpc_enabled', '__return_false');
add_filter('wp_headers', function($headers) {
    unset($headers['X-Pingback']);
    return $headers;
});

/**
 * 🔧 CONDITIONAL LOADING PLUGINÓW
 * Ładuj pluginy tylko tam gdzie są potrzebne
 */
add_action('wp_enqueue_scripts', function() {
    // WooCommerce - tylko na stronach sklepu
    if (!is_woocommerce() && !is_cart() && !is_checkout() && !is_account_page()) {
        wp_dequeue_script('wc-cart-fragments');
    }
    
    // Contact Form 7 - tylko na stronach kontaktu
    if (!is_page('kontakt') && !is_page('contact')) {
        wp_dequeue_script('contact-form-7');
        wp_dequeue_style('contact-form-7');
    }
    
    // Wyłącz wp-embed dla niezalogowanych
    if (!is_admin() && !is_user_logged_in()) {
        wp_deregister_script('wp-embed');
    }
}, 99);

// ========================================
// PODSTAWOWE SEO (BEZ WPŁYWU NA PAGESPEED)
// ========================================

/**
 * 🎯 CANONICAL URLS
 * Eliminacja duplicate content
 */
add_action('wp_head', function() {
    if (is_singular()) {
        echo '<link rel="canonical" href="' . get_permalink() . '">' . "\n";
    }
}, 1);

/**
 * 🎯 BASIC OPEN GRAPH
 * Lepsze udostępnianie w social media
 */
add_action('wp_head', function() {
    if (is_singular()) {
        echo '<meta property="og:title" content="' . esc_attr(get_the_title()) . '">' . "\n";
        echo '<meta property="og:url" content="' . get_permalink() . '">' . "\n";
        echo '<meta property="og:type" content="article">' . "\n";
        
        if (has_post_thumbnail()) {
            $image_url = get_the_post_thumbnail_url(get_the_ID(), 'large');
            echo '<meta property="og:image" content="' . esc_url($image_url) . '">' . "\n";
        }
    }
}, 1);

/**
 * 🎯 TWITTER CARDS
 * Lepsze udostępnianie na Twitterze
 */
add_action('wp_head', function() {
    echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
    echo '<meta name="twitter:site" content="@firmup_eu">' . "\n";
}, 1);

// ========================================
// OPTYMALIZACJE OBRAZÓW (BEZPIECZNE)
// ========================================

/**
 * 📸 PODSTAWOWA OPTYMALIZACJA OBRAZÓW
 * Dodaj alt text jeśli brakuje
 */
add_filter('wp_get_attachment_image_attributes', function($attr, $attachment, $size) {
    // Dodaj alt text jeśli brakuje (SEO)
    if (empty($attr['alt'])) {
        $alt_text = get_post_meta($attachment->ID, '_wp_attachment_image_alt', true);
        if (empty($alt_text)) {
            // Generuj alt text z nazwy pliku
            $attr['alt'] = sanitize_text_field(pathinfo(get_attached_file($attachment->ID), PATHINFO_FILENAME));
        } else {
            $attr['alt'] = $alt_text;
        }
    }
    
    return $attr;
}, 10, 3);

// ========================================
// DNS PREFETCH (BEZPIECZNE)
// ========================================

/**
 * 🚀 DNS PREFETCH
 * Szybsze ładowanie zewnętrznych zasobów
 */
add_action('wp_head', function() {
    echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">' . "\n";
    echo '<link rel="dns-prefetch" href="//www.google-analytics.com">' . "\n";
    echo '<link rel="dns-prefetch" href="//www.googletagmanager.com">' . "\n";
}, 1);

/**
 * ========================================
 * PODSUMOWANIE MINIMALNYCH OPTYMALIZACJI
 * ========================================
 * 
 * ✅ WYDAJNOŚĆ:
 * - Heartbeat API optimization (60s)
 * - Wyłączenie niepotrzebnych zasobów WP
 * - Conditional loading pluginów
 * - DNS prefetch
 * - Autosave optimization
 * 
 * ✅ SEO (PODSTAWOWE):
 * - Canonical URLs
 * - Basic Open Graph
 * - Twitter Cards
 * - Image alt text automation
 * 
 * ✅ BEZPIECZEŃSTWO:
 * - XML-RPC disabled
 * - Generator tags removed
 * - Pingbacks disabled
 * 
 * ⚠️ UWAGA:
 * Te optymalizacje są BEZPIECZNE i nie powinny wpłynąć
 * negatywnie na PageSpeed Insights. Jeśli nadal są problemy,
 * sprawdź konfigurację Seraphinite Accelerator.
 * 
 * Data wdrożenia: 2025-01-23
 * Wykonawca: Hubert - SEO Specialist
 * Status: BEZPIECZNE DO PRODUKCJI
 * ========================================
 */
