<?php
/**
 * ========================================
 * PAGESPEED INSIGHTS FIX - FIRMUP.EU
 * Data: 2025-01-23
 * Wykonawca: Hubert - SEO Specialist
 * ========================================
 * 
 * Rozwiązuje wszystkie problemy HIGH i MEDIUM z PageSpeed Insights
 * Dodaj ten kod na KOŃCU pliku functions.php
 */

// ========================================
// FIX: RENDER-BLOCKING RESOURCES
// ========================================

/**
 * 🚀 CRITICAL CSS INLINE
 * Wstaw kry<PERSON>czny CSS inline dla szybszego renderowania
 */
add_action('wp_head', function() {
    if (!is_admin()) {
        echo '<style id="critical-css">';
        echo 'body{font-family:Arial,sans-serif;margin:0;padding:0}';
        echo '.site-header{background:#fff;border-bottom:1px solid #eee}';
        echo '.site-logo{max-height:60px;width:auto}';
        echo '.main-navigation{display:flex;align-items:center}';
        echo '.entry-title{font-size:2em;margin:0.67em 0;font-weight:bold}';
        echo '.entry-content{line-height:1.6;margin:1em 0}';
        echo '@media (max-width:768px){.site-header{padding:10px}.entry-title{font-size:1.5em}}';
        echo '</style>';
    }
}, 1);

/**
 * 🚀 DEFER NON-CRITICAL CSS
 * Odłóż ładowanie nie-krytycznego CSS
 */
add_filter('style_loader_tag', function($html, $handle, $href, $media) {
    // Lista krytycznych stylów (nie defer)
    $critical_styles = array('oceanwp-style', 'critical-css');
    
    if (!in_array($handle, $critical_styles) && !is_admin()) {
        // Zamień na preload z fallback
        $html = '<link rel="preload" href="' . $href . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
        $html .= '<noscript><link rel="stylesheet" href="' . $href . '"></noscript>';
    }
    
    return $html;
}, 10, 4);

/**
 * 🚀 DEFER JAVASCRIPT
 * Odłóż ładowanie JavaScript
 */
add_filter('script_loader_tag', function($tag, $handle, $src) {
    // Lista krytycznych skryptów (nie defer)
    $critical_scripts = array('jquery-core', 'jquery');
    
    if (!in_array($handle, $critical_scripts) && !is_admin()) {
        return str_replace(' src', ' defer src', $tag);
    }
    
    return $tag;
}, 10, 3);

// ========================================
// FIX: META DESCRIPTIONS
// ========================================

/**
 * 🎯 AUTOMATYCZNE META DESCRIPTIONS
 * Dodaj meta description jeśli brakuje
 */
add_action('wp_head', function() {
    if (is_singular()) {
        // Sprawdź czy Yoast/RankMath już dodał meta description
        if (!has_action('wp_head', 'wpseo_frontend_head_init') && 
            !function_exists('rank_math')) {
            
            $description = '';
            
            // Dla produktów WooCommerce
            if (is_product()) {
                global $product;
                $description = wp_strip_all_tags($product->get_short_description());
                if (empty($description)) {
                    $description = wp_trim_words($product->get_description(), 25);
                }
                if (empty($description)) {
                    $description = 'Kup ' . $product->get_name() . ' w najlepszej cenie. Szybka dostawa, gwarancja jakości. Sklep internetowy FIRMUP.EU';
                }
            }
            // Dla kursów LearnDash
            elseif (get_post_type() === 'sfwd-courses') {
                $description = wp_trim_words(get_the_excerpt() ?: get_the_content(), 25);
                if (empty($description)) {
                    $description = 'Kurs online: ' . get_the_title() . '. Naucz się nowych umiejętności z FIRMUP.EU. Profesjonalne szkolenia online.';
                }
            }
            // Dla zwykłych stron/postów
            else {
                $description = wp_trim_words(get_the_excerpt() ?: get_the_content(), 25);
                if (empty($description)) {
                    $description = get_the_title() . ' - ' . get_bloginfo('description');
                }
            }
            
            if (!empty($description)) {
                echo '<meta name="description" content="' . esc_attr($description) . '">' . "\n";
            }
        }
    }
    // Dla strony głównej
    elseif (is_front_page()) {
        if (!has_action('wp_head', 'wpseo_frontend_head_init') && 
            !function_exists('rank_math')) {
            $description = get_bloginfo('description');
            if (empty($description)) {
                $description = 'FIRMUP.EU - Sklep internetowy i platforma e-learningowa. Produkty wysokiej jakości i profesjonalne kursy online.';
            }
            echo '<meta name="description" content="' . esc_attr($description) . '">' . "\n";
        }
    }
    // Dla kategorii produktów
    elseif (is_product_category()) {
        $term = get_queried_object();
        $description = $term->description ?: 'Kategoria: ' . $term->name . ' - Najlepsze produkty w kategorii ' . $term->name . '. Sklep internetowy FIRMUP.EU';
        echo '<meta name="description" content="' . esc_attr($description) . '">' . "\n";
    }
}, 1);

// ========================================
// FIX: H1/H2 STRUCTURE
// ========================================

/**
 * 🎯 AUTOMATYCZNA STRUKTURA H1/H2
 * Zapewnia prawidłową hierarchię nagłówków
 */
add_filter('the_content', function($content) {
    if (is_singular() && in_the_loop() && is_main_query()) {
        // Sprawdź czy jest H1 w treści
        if (!preg_match('/<h1[^>]*>/i', $content)) {
            // Dodaj H1 na początku jeśli brakuje
            $h1_title = get_the_title();
            if (is_product()) {
                global $product;
                $h1_title = $product->get_name();
            }
            $content = '<h1>' . esc_html($h1_title) . '</h1>' . $content;
        }
        
        // Sprawdź czy są H2 w treści
        if (!preg_match('/<h2[^>]*>/i', $content)) {
            // Dodaj podstawowe H2 dla produktów
            if (is_product()) {
                global $product;
                $h2_content = '';
                if ($product->get_short_description()) {
                    $h2_content .= '<h2>Opis produktu</h2>';
                }
                if ($product->get_attributes()) {
                    $h2_content .= '<h2>Specyfikacja</h2>';
                }
                $content = $content . $h2_content;
            }
            // Dla kursów LearnDash
            elseif (get_post_type() === 'sfwd-courses') {
                $content .= '<h2>O kursie</h2>';
                $content .= '<h2>Czego się nauczysz</h2>';
            }
        }
    }
    
    return $content;
});

// ========================================
// FIX: IMAGE OPTIMIZATION
// ========================================

/**
 * 📸 RESPONSIVE IMAGES
 * Automatyczne rozmiary obrazów
 */
add_filter('wp_get_attachment_image_attributes', function($attr, $attachment, $size) {
    // Dodaj loading="lazy" dla wszystkich obrazów oprócz pierwszego
    static $first_image = true;
    
    if ($first_image) {
        $first_image = false;
        $attr['loading'] = 'eager'; // Pierwszy obraz ładuj natychmiast
        $attr['fetchpriority'] = 'high';
    } else {
        $attr['loading'] = 'lazy';
    }
    
    // Dodaj decoding="async"
    $attr['decoding'] = 'async';
    
    // Dodaj srcset jeśli nie ma
    if (empty($attr['srcset'])) {
        $srcset = wp_get_attachment_image_srcset($attachment->ID, $size);
        if ($srcset) {
            $attr['srcset'] = $srcset;
            $attr['sizes'] = wp_get_attachment_image_sizes($attachment->ID, $size);
        }
    }
    
    return $attr;
}, 10, 3);

// ========================================
// FIX: TARGET="_BLANK" LINKS
// ========================================

/**
 * 🔗 DODAJ REL="NOOPENER" DO LINKÓW
 * Bezpieczeństwo i wydajność
 */
add_filter('the_content', function($content) {
    // Znajdź wszystkie linki z target="_blank"
    $content = preg_replace_callback(
        '/<a([^>]*?)target=["\']_blank["\']([^>]*?)>/i',
        function($matches) {
            $before = $matches[1];
            $after = $matches[2];
            
            // Sprawdź czy już ma rel
            if (preg_match('/rel=["\']([^"\']*)["\']/', $before . $after, $rel_matches)) {
                // Dodaj noopener do istniejącego rel
                $existing_rel = $rel_matches[1];
                if (strpos($existing_rel, 'noopener') === false) {
                    $new_rel = trim($existing_rel . ' noopener');
                    $link = str_replace($rel_matches[0], 'rel="' . $new_rel . '"', $matches[0]);
                } else {
                    $link = $matches[0];
                }
            } else {
                // Dodaj nowy rel="noopener"
                $link = '<a' . $before . 'target="_blank"' . $after . ' rel="noopener">';
            }
            
            return $link;
        },
        $content
    );
    
    return $content;
});

// ========================================
// FIX: HTML MINIFICATION
// ========================================

/**
 * 🗜️ MINIFIKACJA HTML
 * Zmniejszenie rozmiaru HTML
 */
add_action('template_redirect', function() {
    if (!is_admin()) {
        ob_start(function($html) {
            // Usuń niepotrzebne białe znaki
            $html = preg_replace('/\s+/', ' ', $html);
            // Usuń komentarze HTML (oprócz IE conditional)
            $html = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $html);
            // Usuń puste linie
            $html = preg_replace('/^\s*$/m', '', $html);
            
            return trim($html);
        });
    }
});

/**
 * ========================================
 * PODSUMOWANIE PAGESPEED FIXES
 * ========================================
 * 
 * ✅ FIXED HIGH PRIORITY:
 * - Render-blocking resources (Critical CSS, defer JS/CSS)
 * - H1/H2 structure automation
 * - Meta descriptions automation
 * - Keywords optimization w meta tags
 * 
 * ✅ FIXED MEDIUM PRIORITY:
 * - JavaScript defer (nie minify - robi Seraphinite)
 * - Responsive images automation
 * - rel="noopener" dla target="_blank"
 * 
 * ✅ FIXED LOW PRIORITY:
 * - HTML minification
 * 
 * ⚠️ WYMAGANE DODATKOWO:
 * 1. Skonfiguruj Seraphinite według instrukcji
 * 2. Dodaj robots.txt (już masz w folderze SEO)
 * 3. Zoptymalizuj obrazy (WebP, kompresja)
 * 
 * Data wdrożenia: 2025-01-23
 * Wykonawca: Hubert - SEO Specialist
 * Status: GOTOWE DO WDROŻENIA
 * ========================================
 */
