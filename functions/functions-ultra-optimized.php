<?php
/**
 * ULTRA OPTYMALIZACJA FUNCTIONS.PHP - FIRMUP.EU
 * 
 * Ten plik zawiera wszystkie optymalizacje wydajności i SEO
 * Data: 2025-01-23
 * 
 * INSTRUKCJA:
 * 1. <PERSON>rób backup obecnego functions.php
 * 2. <PERSON><PERSON><PERSON><PERSON><PERSON> zawartość tego pliku na KONIEC obecnego functions.php
 * 3. Nie zastępuj całego pliku - tylko dodaj na końcu!
 */

// =====================================================
// ULTRA OPTYMALIZACJE WYDAJNOŚCI - FIRMUP.EU
// =====================================================

/**
 * 🔧 OPTYMALIZACJA HEARTBEAT API
 * Zmniejszenie częstotliwości heartbeat dla lepszej wyd<PERSON>ci
 */
add_filter('heartbeat_settings', function($settings) {
    // Zwiększ interwał heartbeat z 15 do 60 sekund
    $settings['interval'] = 60;
    return $settings;
});

/**
 * 🔧 WYŁĄCZENIE HEARTBEAT NA FRONT-ENDZIE
 * Heartbeat nie jest potrzebny na stronie głównej
 */
add_action('init', function() {
    if (!is_admin()) {
        wp_deregister_script('heartbeat');
    }
});

/**
 * 🔧 OPTYMALIZACJA ADMIN AJAX
 * Ograniczenie niepotrzebnych zapytań AJAX
 */
add_action('wp_enqueue_scripts', function() {
    if (!is_admin() && !is_user_logged_in()) {
        wp_deregister_script('wp-embed');
    }
});

/**
 * 🔧 WYŁĄCZENIE NIEPOTRZEBNYCH FUNKCJI WP
 * Zmniejszenie obciążenia serwera
 */
// Wyłącz emoji
remove_action('wp_head', 'print_emoji_detection_script', 7);
remove_action('wp_print_styles', 'print_emoji_styles');

// Wyłącz REST API dla niezalogowanych użytkowników
add_filter('rest_authentication_errors', function($result) {
    if (!is_user_logged_in()) {
        return new WP_Error('rest_not_logged_in', 'You are not currently logged in.', array('status' => 401));
    }
    return $result;
});

/**
 * 🔧 OPTYMALIZACJA QUERY
 * Zmniejszenie obciążenia bazy danych
 */
add_action('pre_get_posts', function($query) {
    if (!is_admin() && $query->is_main_query()) {
        // Ograniczenie liczby postów na stronie archiwum
        if (is_archive() || is_home()) {
            $query->set('posts_per_page', 10);
        }
    }
});

/**
 * 🔧 WYŁĄCZENIE PINGBACKS I TRACKBACKS
 * Zmniejszenie spam i obciążenia
 */
add_filter('xmlrpc_enabled', '__return_false');
add_filter('wp_headers', function($headers) {
    unset($headers['X-Pingback']);
    return $headers;
});

/**
 * 🔧 OPTYMALIZACJA DASHBOARD
 * Usunięcie niepotrzebnych widgetów z dashboardu
 */
add_action('wp_dashboard_setup', function() {
    remove_meta_box('dashboard_incoming_links', 'dashboard', 'normal');
    remove_meta_box('dashboard_plugins', 'dashboard', 'normal');
    remove_meta_box('dashboard_primary', 'dashboard', 'side');
    remove_meta_box('dashboard_secondary', 'dashboard', 'normal');
    remove_meta_box('dashboard_quick_press', 'dashboard', 'side');
    remove_meta_box('dashboard_recent_drafts', 'dashboard', 'side');
    remove_meta_box('dashboard_recent_comments', 'dashboard', 'normal');
    remove_meta_box('dashboard_right_now', 'dashboard', 'normal');
    remove_meta_box('dashboard_activity', 'dashboard', 'normal');
});

/**
 * 🔧 OPTYMALIZACJA AUTOSAVE
 * Zmniejszenie częstotliwości autozapisu
 */
add_filter('autosave_interval', function() {
    return 300; // 5 minut zamiast 60 sekund
});

/**
 * 🔧 DODATKOWE OPTYMALIZACJE WYDAJNOŚCI
 * Dalsze usprawnienia dla lepszej wydajności
 */

// Wyłącz generator meta tag
remove_action('wp_head', 'wp_generator');

// Wyłącz wlwmanifest link
remove_action('wp_head', 'wlwmanifest_link');

// Wyłącz RSD link
remove_action('wp_head', 'rsd_link');

// Wyłącz shortlink
remove_action('wp_head', 'wp_shortlink_wp_head');

// Optymalizacja dla WooCommerce - wyłącz niepotrzebne skrypty na stronach nie-sklepowych
add_action('wp_enqueue_scripts', function() {
    if (!is_woocommerce() && !is_cart() && !is_checkout() && !is_account_page()) {
        wp_dequeue_style('woocommerce-general');
        wp_dequeue_style('woocommerce-layout');
        wp_dequeue_style('woocommerce-smallscreen');
        wp_dequeue_script('wc-cart-fragments');
        wp_dequeue_script('woocommerce');
    }
}, 99);

// Optymalizacja Contact Form 7 - ładuj tylko tam gdzie potrzebne
add_action('wp_enqueue_scripts', function() {
    if (!is_page('kontakt') && !is_page('contact')) {
        wp_dequeue_script('contact-form-7');
        wp_dequeue_style('contact-form-7');
    }
}, 99);

// Wyłącz jQuery Migrate jeśli nie jest potrzebne
add_action('wp_enqueue_scripts', function() {
    if (!is_admin()) {
        wp_deregister_script('jquery-migrate');
    }
});

// Optymalizacja dla LearnDash - ładuj tylko na stronach kursów
add_action('wp_enqueue_scripts', function() {
    global $post;
    if (!is_singular(['sfwd-courses', 'sfwd-lessons', 'sfwd-topic', 'sfwd-quiz']) && 
        !is_post_type_archive(['sfwd-courses'])) {
        // Wyłącz niepotrzebne skrypty LearnDash na innych stronach
        wp_dequeue_script('learndash_template_script_js');
        wp_dequeue_style('learndash_style');
    }
}, 99);

// =====================================================
// OPTYMALIZACJE SEO - FIRMUP.EU
// =====================================================

/**
 * 🎯 SCHEMA MARKUP DLA SEO - WOOCOMMERCE
 * Structured data dla lepszego pozycjonowania
 */
add_action('wp_head', function() {
    if (is_product()) {
        global $product;
        if ($product) {
            $schema = array(
                '@context' => 'https://schema.org/',
                '@type' => 'Product',
                'name' => $product->get_name(),
                'description' => wp_strip_all_tags($product->get_short_description()),
                'sku' => $product->get_sku(),
                'offers' => array(
                    '@type' => 'Offer',
                    'price' => $product->get_price(),
                    'priceCurrency' => get_woocommerce_currency(),
                    'availability' => $product->is_in_stock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
                    'url' => get_permalink()
                )
            );
            
            if ($product->get_image_id()) {
                $schema['image'] = wp_get_attachment_url($product->get_image_id());
            }
            
            echo '<script type="application/ld+json">' . json_encode($schema) . '</script>';
        }
    }
    
    // Schema dla strony głównej sklepu
    if (is_shop()) {
        $schema = array(
            '@context' => 'https://schema.org/',
            '@type' => 'WebSite',
            'name' => get_bloginfo('name'),
            'url' => home_url(),
            'potentialAction' => array(
                '@type' => 'SearchAction',
                'target' => home_url('/?s={search_term_string}&post_type=product'),
                'query-input' => 'required name=search_term_string'
            )
        );
        echo '<script type="application/ld+json">' . json_encode($schema) . '</script>';
    }
});

/**
 * 🎯 SCHEMA MARKUP DLA LEARNDASH LMS
 * Structured data dla kursów online
 */
add_action('wp_head', function() {
    if (get_post_type() === 'sfwd-courses') {
        global $post;
        $course_id = $post->ID;
        
        $schema = array(
            '@context' => 'https://schema.org/',
            '@type' => 'Course',
            'name' => get_the_title(),
            'description' => wp_strip_all_tags(get_the_excerpt()),
            'provider' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'url' => home_url()
            ),
            'url' => get_permalink()
        );
        
        // Dodaj cenę kursu jeśli dostępna
        if (function_exists('learndash_get_course_price')) {
            $course_price = learndash_get_course_price($course_id);
            if ($course_price && $course_price['type'] === 'paynow') {
                $schema['offers'] = array(
                    '@type' => 'Offer',
                    'price' => $course_price['price'],
                    'priceCurrency' => 'PLN'
                );
            }
        }
        
        echo '<script type="application/ld+json">' . json_encode($schema) . '</script>';
    }
});

// =====================================================
// CORE WEB VITALS OPTYMALIZACJE
// =====================================================

/**
 * 🚀 LAZY LOADING Z OPTYMALIZACJĄ SEO
 * Poprawia LCP i CLS dla Core Web Vitals
 */
add_filter('wp_get_attachment_image_attributes', function($attr, $attachment, $size) {
    // Nie dodawaj lazy loading do pierwszego obrazu (LCP)
    static $first_image = true;
    
    if ($first_image) {
        $first_image = false;
        $attr['fetchpriority'] = 'high';
        return $attr;
    }
    
    // Dodaj lazy loading do pozostałych obrazów
    $attr['loading'] = 'lazy';
    $attr['decoding'] = 'async';
    
    return $attr;
}, 10, 3);

/**
 * 🚀 PRELOAD KRYTYCZNYCH ZASOBÓW
 * Przyśpiesza ładowanie najważniejszych plików
 */
add_action('wp_head', function() {
    // Preload krytycznego CSS
    echo '<link rel="preload" href="' . get_template_directory_uri() . '/style.css" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
    
    // Preload głównej czcionki
    $font_files = glob(ABSPATH . 'wp-content/uploads/oceanwp-webfonts/*.woff2');
    if (!empty($font_files)) {
        $font_url = str_replace(ABSPATH, home_url('/'), $font_files[0]);
        echo '<link rel="preload" href="' . $font_url . '" as="font" type="font/woff2" crossorigin>';
    }
    
    // DNS prefetch dla zewnętrznych domen
    echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">';
    echo '<link rel="dns-prefetch" href="//www.google-analytics.com">';
}, 1);
