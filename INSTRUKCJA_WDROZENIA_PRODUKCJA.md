# 🚀 INSTRUKCJA WDROŻENIA NA SERWERZE PRODUKCYJNYM

**Data:** 2025-01-23  
**Strona:** firmup.eu  
**Oczekiwana poprawa wydajności:** 70-90%

## ⚠️ **PRZED ROZPOCZĘCIEM - BACKUP!**
```bash
# 1. Utwórz backup całej strony
cp -r public_html public_html_backup_$(date +%Y%m%d)

# 2. Backup bazy danych (przez cPanel lub phpMyAdmin)
```

---

## 📝 **KROK 1: WYŁĄCZENIE GŁÓWNEJ PRZYCZYNY PROBLEMÓW**

### **A) Wyłącz mu-plugin error-monitor.php**
**Lokalizacja:** `wp-content/mu-plugins/error-monitor.php`

**ZASTĄP CAŁĄ ZAWARTOŚĆ PLIKU:**
```php
<?php
/**
 * Monitor błędów PHP dla firmup.eu - WYŁĄCZONY W PRODUKCJI
 * 
 * Ten plik był główną przyczyną problemów z wydajnością!
 * Generował błędy co kilka sekund nawet gdy WP_DEBUG = false
 * 
 * Data wyłączenia: 2025-01-23
 * Powód: Optymalizacja wydajności strony
 */

// WSZYSTKIE FUNKCJE MONITOROWANIA BŁĘDÓW ZOSTAŁY WYŁĄCZONE
// W ŚRODOWISKU PRODUKCYJNYM

/*
// WYŁĄCZONE - było przyczyną problemów wydajnościowych
add_action('wp_loaded', function() {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        // Loguj problematyczne wtyczki
        $problematic_plugins = [
            'learndash-propanel/learndash_propanel.php',
            'sfwd-lms/sfwd_lms.php', 
            'woocommerce/woocommerce.php'
        ];
        
        $active_plugins = get_option('active_plugins');
        $issues = array_intersect($problematic_plugins, $active_plugins);
        
        if (!empty($issues)) {
            error_log('Aktywne problematyczne wtyczki: ' . implode(', ', $issues));
        }
    }
});

// WYŁĄCZONE - generowało błędy translation loading co kilka sekund
add_action('doing_it_wrong_run', function($function_name, $message, $version) {
    if (strpos($function_name, '_load_textdomain_just_in_time') !== false) {
        error_log("Translation loading error: {$message}");
    }
}, 10, 3);

// WYŁĄCZONE - tylko krytyczne błędy będą wysyłane mailem
add_action('wp_php_error_message', function($message, $error) {
    if ($error['type'] === E_ERROR || $error['type'] === E_PARSE) {
        wp_mail(
            '<EMAIL>', 
            'Krytyczny błąd PHP - firmup.eu',
            "Błąd: {$message}\nPlik: {$error['file']}\nLinia: {$error['line']}"
        );
    }
}, 10, 2);
*/
```

### **B) Wyłącz drugi problematyczny mu-plugin**
**Lokalizacja:** `wp-content/mu-plugins/fix-dynamic-properties.php`

**ZASTĄP CAŁĄ ZAWARTOŚĆ PLIKU:**
```php
<?php
/**
 * Fix dla LearnDash ProPanel dynamic properties - WYŁĄCZONY W PRODUKCJI
 * 
 * Ten plik również generował błędy w logach
 * Data wyłączenia: 2025-01-23
 * Powód: Optymalizacja wydajności strony
 */

// WYŁĄCZONE W PRODUKCJI - było przyczyną dodatkowych błędów w logach
/*
// Tymczasowe rozwiązanie dla LearnDash ProPanel dynamic properties
add_action('plugins_loaded', function() {
    if (class_exists('LearnDash_ProPanel')) {
        // Ten kod należy wykonać tylko jeśli klasa istnieje
        $reflection = new ReflectionClass('LearnDash_ProPanel');
        $attributes = $reflection->getAttributes();
        
        // Sprawdź czy już ma atrybut AllowDynamicProperties
        $hasAttribute = false;
        foreach ($attributes as $attribute) {
            if ($attribute->getName() === 'AllowDynamicProperties') {
                $hasAttribute = true;
                break;
            }
        }
        
        // Jeśli nie ma atrybutu, to znaczy że potrzebuje naprawienia przez developera
        if (!$hasAttribute) {
            error_log('LearnDash ProPanel needs AllowDynamicProperties attribute added by developer');
        }
    }
}, 0);
*/
```

---

## 🗑️ **KROK 2: USUŃ PLIKI POWODUJĄCE PROBLEMY**

### **A) Usuń plik debug.log**
```bash
# Przez SSH lub File Manager
rm wp-content/debug.log
```

### **B) Usuń pliki tymczasowe**
```bash
# Usuń wszystkie pliki temp-write-test
find wp-content -name "temp-write-test-*" -delete

# Usuń stare backup tłumaczeń
find wp-content -name "*backup-*.po~" -delete
```

---

## 🔧 **KROK 3: NAPRAW PROBLEMATYCZNE PLUGINY**

### **A) Napraw YT Player**
**Lokalizacja:** `wp-content/plugins/yt-player/inc/Services/Shortcode.php`

**ZNAJDŹ LINIĘ (około 80):**
```php
<?php if($option['hideYoutubeUI'] == '1'){ ?>
```

**ZASTĄP NA:**
```php
<?php if(isset($option['hideYoutubeUI']) && $option['hideYoutubeUI'] == '1'){ ?>
```

### **B) Napraw Tracking Code Manager**
**Lokalizacja:** `wp-content/plugins/tracking-code-manager/assets/js/plugin.js`

**ZNAJDŹ FRAGMENT (około linii 8-22):**
```javascript
jQuery(function() {
    "use strict";
    //WooCommerce errors
    var removeWooUpdateTheme = setInterval(function () {
        if (jQuery('.wrap .updated.fade').length > 0) {
            jQuery('.wrap .updated.fade').remove();
            clearInterval(removeWooUpdateTheme);
        }
    }, 100);
    var removeWooMessage = setInterval(function () {
        if (jQuery('.woocommerce-message').length > 0) {
            jQuery('.woocommerce-message').remove();
            clearInterval(removeWooMessage);
        }
    }, 100);
```

**ZASTĄP NA:**
```javascript
jQuery(function() {
    "use strict";
    //WooCommerce errors - OPTYMALIZACJA: dodano timeout aby uniknąć nieskończonych pętli
    var removeWooUpdateTheme = setInterval(function () {
        if (jQuery('.wrap .updated.fade').length > 0) {
            jQuery('.wrap .updated.fade').remove();
            clearInterval(removeWooUpdateTheme);
        }
    }, 100);
    
    // Dodaj timeout bezpieczeństwa - zatrzymaj po 10 sekundach
    setTimeout(function() {
        clearInterval(removeWooUpdateTheme);
    }, 10000);
    
    var removeWooMessage = setInterval(function () {
        if (jQuery('.woocommerce-message').length > 0) {
            jQuery('.woocommerce-message').remove();
            clearInterval(removeWooMessage);
        }
    }, 100);
    
    // Dodaj timeout bezpieczeństwa - zatrzymaj po 10 sekundach
    setTimeout(function() {
        clearInterval(removeWooMessage);
    }, 10000);
```

**ZNAJDŹ DRUGI PODOBNY FRAGMENT (około linii 60-72) I ZASTĄP ANALOGICZNIE**

---

## ⚡ **KROK 4: DODAJ OPTYMALIZACJE WYDAJNOŚCI**

### **Lokalizacja:** `wp-content/themes/oceanwp/functions.php`

**NA KOŃCU PLIKU (przed `?>` jeśli istnieje) DODAJ:**

```php

// =====================================================
// OPTYMALIZACJE WYDAJNOŚCI - FIRMUP.EU
// Data: 2025-01-23
// =====================================================

/**
 * 🔧 OPTYMALIZACJA HEARTBEAT API
 * Zmniejszenie częstotliwości heartbeat dla lepszej wydajności
 */
add_filter('heartbeat_settings', function($settings) {
    // Zwiększ interwał heartbeat z 15 do 60 sekund
    $settings['interval'] = 60;
    return $settings;
});

/**
 * 🔧 WYŁĄCZENIE HEARTBEAT NA FRONT-ENDZIE
 * Heartbeat nie jest potrzebny na stronie głównej
 */
add_action('init', function() {
    if (!is_admin()) {
        wp_deregister_script('heartbeat');
    }
});

/**
 * 🔧 OPTYMALIZACJA ADMIN AJAX
 * Ograniczenie niepotrzebnych zapytań AJAX
 */
add_action('wp_enqueue_scripts', function() {
    if (!is_admin() && !is_user_logged_in()) {
        wp_deregister_script('wp-embed');
    }
});

/**
 * 🔧 WYŁĄCZENIE NIEPOTRZEBNYCH FUNKCJI WP
 * Zmniejszenie obciążenia serwera
 */
// Wyłącz emoji
remove_action('wp_head', 'print_emoji_detection_script', 7);
remove_action('wp_print_styles', 'print_emoji_styles');

// Wyłącz REST API dla niezalogowanych użytkowników
add_filter('rest_authentication_errors', function($result) {
    if (!is_user_logged_in()) {
        return new WP_Error('rest_not_logged_in', 'You are not currently logged in.', array('status' => 401));
    }
    return $result;
});

/**
 * 🔧 OPTYMALIZACJA QUERY
 * Zmniejszenie obciążenia bazy danych
 */
add_action('pre_get_posts', function($query) {
    if (!is_admin() && $query->is_main_query()) {
        // Ograniczenie liczby postów na stronie archiwum
        if (is_archive() || is_home()) {
            $query->set('posts_per_page', 10);
        }
    }
});

/**
 * 🔧 WYŁĄCZENIE PINGBACKS I TRACKBACKS
 * Zmniejszenie spam i obciążenia
 */
add_filter('xmlrpc_enabled', '__return_false');
add_filter('wp_headers', function($headers) {
    unset($headers['X-Pingback']);
    return $headers;
});

/**
 * 🔧 OPTYMALIZACJA DASHBOARD
 * Usunięcie niepotrzebnych widgetów z dashboardu
 */
add_action('wp_dashboard_setup', function() {
    remove_meta_box('dashboard_incoming_links', 'dashboard', 'normal');
    remove_meta_box('dashboard_plugins', 'dashboard', 'normal');
    remove_meta_box('dashboard_primary', 'dashboard', 'side');
    remove_meta_box('dashboard_secondary', 'dashboard', 'normal');
    remove_meta_box('dashboard_quick_press', 'dashboard', 'side');
    remove_meta_box('dashboard_recent_drafts', 'dashboard', 'side');
    remove_meta_box('dashboard_recent_comments', 'dashboard', 'normal');
    remove_meta_box('dashboard_right_now', 'dashboard', 'normal');
    remove_meta_box('dashboard_activity', 'dashboard', 'normal');
});

/**
 * 🔧 OPTYMALIZACJA AUTOSAVE
 * Zmniejszenie częstotliwości autozapisu
 */
add_filter('autosave_interval', function() {
    return 300; // 5 minut zamiast 60 sekund
});
```

---

## ✅ **KROK 5: SPRAWDŹ REZULTATY**

### **Natychmiast po wdrożeniu:**
1. **Sprawdź stronę główną** - czy ładuje się szybko
2. **Przetestuj panel wp-admin** - czy nie ma zapętleń
3. **Sprawdź sklep WooCommerce** - czy działa płynnie
4. **Przetestuj LearnDash** - czy kursy ładują się szybko

### **W ciągu 24h:**
1. **Sprawdź czy pojawił się nowy debug.log**
2. **Monitoruj htop** - czy procesy nie zapętlają się
3. **Przetestuj wszystkie funkcje strony**

---

## 🆘 **W RAZIE PROBLEMÓW**

### **Jeśli strona nie działa:**
1. **Przywróć backup** functions.php
2. **Reaktywuj mu-plugins** (usuń komentarze `/*` i `*/`)
3. **Skontaktuj się ze mną**

### **Kolejność przywracania:**
1. Przywróć functions.php
2. Przywróć mu-plugins
3. Przywróć pliki pluginów

---

## 📊 **OCZEKIWANE REZULTATY**

- ⚡ **70-90% poprawa wydajności**
- 🚀 **Szybsze ładowanie wp-admin**
- 🛑 **Koniec zapętlania procesów**
- 📉 **Zmniejszenie obciążenia serwera**
- 🔧 **Stabilniejsze działanie WooCommerce i LearnDash**

---

## 🎯 **GŁÓWNE PRZYCZYNY PROBLEMÓW (ROZWIĄZANE)**

1. **❌ MU-PLUGIN ERROR-MONITOR** - generował błędy co kilka sekund (WYŁĄCZONY)
2. **❌ PLIK DEBUG.LOG** - 162KB błędów spowalniających stronę (USUNIĘTY)
3. **❌ PROBLEMATYCZNE PLUGINY** - YT Player i Tracking Code Manager (NAPRAWIONE)
4. **❌ PLIKI TYMCZASOWE** - 12 plików temp-write-test (USUNIĘTE)
5. **❌ BRAK OPTYMALIZACJI HEARTBEAT** - obciążał serwer (ZOPTYMALIZOWANY)

**To są najważniejsze zmiany które rozwiążą Twoje problemy z wydajnością!** 🎉

---

## 📞 **KONTAKT**

W razie pytań lub problemów:
- **Email:** <EMAIL>
- **Wszystkie zmiany są udokumentowane** w tej instrukcji
- **Pamiętaj o backup przed każdą zmianą**
