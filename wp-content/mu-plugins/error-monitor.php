<?php
/**
 * Monitor błędów PHP dla firmup.eu - WYŁĄCZONY W PRODUKCJI
 *
 * Ten plik był główną przyczyną problemów z wydajnością!
 * Generował błędy co kilka sekund nawet gdy WP_DEBUG = false
 *
 * Data wyłączenia: 2025-01-23
 * Powód: Optymalizacja wydajności strony
 */

// WSZYSTKIE FUNKCJE MONITOROWANIA BŁĘDÓW ZOSTAŁY WYŁĄCZONE
// W ŚRODOWISKU PRODUKCYJNYM

/*
// WYŁĄCZONE - było przyczyną problemów wydajnościowych
add_action('wp_loaded', function() {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        // Loguj problematyczne wtyczki
        $problematic_plugins = [
            'learndash-propanel/learndash_propanel.php',
            'sfwd-lms/sfwd_lms.php',
            'woocommerce/woocommerce.php'
        ];

        $active_plugins = get_option('active_plugins');
        $issues = array_intersect($problematic_plugins, $active_plugins);

        if (!empty($issues)) {
            error_log('Aktywne problematyczne wtyczki: ' . implode(', ', $issues));
        }
    }
});

// WYŁĄCZONE - generowało błędy translation loading co kilka sekund
add_action('doing_it_wrong_run', function($function_name, $message, $version) {
    if (strpos($function_name, '_load_textdomain_just_in_time') !== false) {
        error_log("Translation loading error: {$message}");
    }
}, 10, 3);

// WYŁĄCZONE - tylko krytyczne błędy będą wysyłane mailem
add_action('wp_php_error_message', function($message, $error) {
    if ($error['type'] === E_ERROR || $error['type'] === E_PARSE) {
        wp_mail(
            '<EMAIL>',
            'Krytyczny błąd PHP - firmup.eu',
            "Błąd: {$message}\nPlik: {$error['file']}\nLinia: {$error['line']}"
        );
    }
}, 10, 2);
*/

