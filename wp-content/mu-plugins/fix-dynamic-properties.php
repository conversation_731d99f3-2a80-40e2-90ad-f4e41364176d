<?php
/**
 * Fix dla LearnDash ProPanel dynamic properties - WYŁĄCZONY W PRODUKCJI
 *
 * Ten plik również generował błędy w logach
 * Data wyłączenia: 2025-01-23
 * Powód: Optymalizacja wydajności strony
 */

// WYŁĄCZONE W PRODUKCJI - było przyczyną dodatkowych błędów w logach
/*
// Tymczasowe rozwiązanie dla LearnDash ProPanel dynamic properties
add_action('plugins_loaded', function() {
    if (class_exists('LearnDash_ProPanel')) {
        // Ten kod należy wykonać tylko jeśli klasa istnieje
        $reflection = new ReflectionClass('LearnDash_ProPanel');
        $attributes = $reflection->getAttributes();

        // Sprawdź czy już ma atrybut AllowDynamicProperties
        $hasAttribute = false;
        foreach ($attributes as $attribute) {
            if ($attribute->getName() === 'AllowDynamicProperties') {
                $hasAttribute = true;
                break;
            }
        }

        // Jeśli nie ma atrybutu, to znaczy że potrzebuje naprawienia przez developera
        if (!$hasAttribute) {
            error_log('LearnDash ProPanel needs AllowDynamicProperties attribute added by developer');
        }
    }
}, 0);
*/
