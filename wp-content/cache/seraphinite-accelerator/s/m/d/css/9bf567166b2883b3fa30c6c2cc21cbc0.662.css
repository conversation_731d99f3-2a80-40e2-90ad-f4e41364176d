.ytPlayer{display:flex;float:none!important;width:100%}.ytPlayer .ytWrapper{position:relative;width:100%}.ytPlayer .plyr{background:0 0;width:100%}.ytPlayer .plyr .plyr__control--overlaid{-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);height:auto}.ytPlayer .plyr .plyr__control{height:auto}.ytPlayer.left{justify-content:left}.ytPlayer.center{justify-content:center}.ytPlayer.right{justify-content:right}.hideYoutubeUI .plyr iframe{height:200%;top:-50%}.custom-overlay img{height:auto}.hideControlsWhenPause .plyr--paused:not(.plyr--loading) .plyr__controls{opacity:0;transition:opacity .5s}.plyr__poster{background-position:50%;background-size:cover;height:100%;left:0;opacity:0;pointer-events:none;position:absolute;top:0;transition:opacity .3s ease;width:100%;z-index:2}.plyr__poster.show{display:block!important;opacity:1}.components-base-control.ej5x27r4{margin-bottom:0!important}.ytPlayer .ytWrapper.floating-player{background:#000;bottom:20px;box-shadow:0 4px 8px rgba(0,0,0,.2);height:170px;position:fixed;right:20px;width:300px;z-index:999}.floating-player .ytWrapper{height:100%;width:100%}.floating-player .custom-overlay,.floating-player .plyr__time--duration,.floating-player [data-plyr=download],.floating-player [data-plyr=fast-forward],.floating-player [data-plyr=restart],.floating-player [data-plyr=rewind]{display:none}.floating-player .plyr__volume input[type=range]{width:50px}.floating-player iframe{height:100%;width:100%}.close-mini-player{border:none;border-radius:50%;box-shadow:0 2px 5px rgba(0,0,0,.3);color:#fff;cursor:pointer;font-size:14px;padding:10px;position:absolute;right:5px;top:5px}