.elementor-widget-text-editor{font-family:var(--e-global-typography-text-font-family),Sans-serif;font-weight:var(--e-global-typography-text-font-weight);color:var(--e-global-color-text)}.elementor-widget-text-editor.elementor-drop-cap-view-stacked .elementor-drop-cap{background-color:var(--e-global-color-primary)}.elementor-widget-text-editor.elementor-drop-cap-view-framed .elementor-drop-cap,.elementor-widget-text-editor.elementor-drop-cap-view-default .elementor-drop-cap{color:var(--e-global-color-primary);border-color:var(--e-global-color-primary)}.elementor-widget-button .elementor-button{background-color:var(--e-global-color-accent);font-family:var(--e-global-typography-accent-font-family),Sans-serif;font-weight:var(--e-global-typography-accent-font-weight)}.elementor-1086 .elementor-element.elementor-element-edba0a0 .elementor-button{background-color:#9961ce}.elementor-1086 .elementor-element.elementor-element-edba0a0>.elementor-widget-container{margin:0 0 50px}.elementor-widget-image .widget-image-caption{color:var(--e-global-color-text);font-family:var(--e-global-typography-text-font-family),Sans-serif;font-weight:var(--e-global-typography-text-font-weight)}