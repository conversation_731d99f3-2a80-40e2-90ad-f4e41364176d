//IntellyWP
jQuery('.wrap .updated.fade').remove();
jQuery('.woocommerce-message').remove();
jQuery('.error').remove();
jQuery('.info').remove();
jQuery('.update-nag').remove();

jQuery(function() {
    "use strict";
    //WooCommerce errors - OPTYMALIZACJA: dodano timeout aby uniknąć nieskończonych pętli
    var removeWooUpdateTheme = setInterval(function () {
        if (jQuery('.wrap .updated.fade').length > 0) {
            jQuery('.wrap .updated.fade').remove();
            clearInterval(removeWooUpdateTheme);
        }
    }, 100);

    // Dodaj timeout bezpieczeństwa - zatrzymaj po 10 sekundach
    setTimeout(function() {
        clearInterval(removeWooUpdateTheme);
    }, 10000);

    var removeWooMessage = setInterval(function () {
        if (jQuery('.woocommerce-message').length > 0) {
            jQuery('.woocommerce-message').remove();
            clearInterval(removeWooMessage);
        }
    }, 100);

    // Dodaj timeout bezpieczeństwa - zatrzymaj po 10 sekundach
    setTimeout(function() {
        clearInterval(removeWooMessage);
    }, 10000);

    jQuery('.wrap .updated.fade').remove();
    jQuery('.woocommerce-message').remove();
    jQuery('.error').remove();
    jQuery('.info').remove();
    jQuery('.update-nag').remove();
});

jQuery(function() {
    if(jQuery('.wrap .updated.fade').length>0) {
        jQuery('.wrap .updated.fade').remove();
    }
    if(jQuery('.woocommerce-message').length>0) {
        jQuery('.woocommerce-message').remove();
    }
    jQuery('.update-nag,.updated,.error').each(function() {
        var $self=jQuery(this);
        if(!$self.hasClass('iwp')) {
            $self.remove();
        }
    });
});

jQuery(function() {
    "use strict";

    //WooCommerce errors - OPTYMALIZACJA: dodano timeout aby uniknąć nieskończonych pętli
    var removeWooUpdateTheme=setInterval(function () {
        if (jQuery('.wrap .updated.fade').length > 0) {
            jQuery('.wrap .updated.fade').remove();
            clearInterval(removeWooUpdateTheme);
        }
    }, 100);

    // Dodaj timeout bezpieczeństwa - zatrzymaj po 10 sekundach
    setTimeout(function() {
        clearInterval(removeWooUpdateTheme);
    }, 10000);

    var removeWooMessage=setInterval(function () {
        if (jQuery('.woocommerce-message').length > 0) {
            jQuery('.woocommerce-message').remove();
            clearInterval(removeWooMessage);
        }
    }, 100);

    // Dodaj timeout bezpieczeństwa - zatrzymaj po 10 sekundach
    setTimeout(function() {
        clearInterval(removeWooMessage);
    }, 10000);
});

jQuery(function() {
    jQuery('.tcmp-select-onfocus').click(function() {
        var $self=jQuery(this);
        $self.select();
    });

    jQuery(".tcmp-hideShow").click(function() {
        tcmp_hideShow(this);
    });
    jQuery(".tcmp-hideShow").each(function() {
        tcmp_hideShow(this);
    });

    function tcmp_hideShow(v) {
        var $source=jQuery(v);
        if($source.attr('tcmp-hideIfTrue') && $source.attr('tcmp-hideShow')) {
            var $destination=jQuery('[name='+$source.attr('tcmp-hideShow')+']');
            if($destination.length==0) {
                $destination=jQuery('#'+$source.attr('tcmp-hideShow'));
            }
            if($destination.length>0) {
                var isChecked=$source.is(":checked");
                var hideIfTrue=($source.attr('tcmp-hideIfTrue').toLowerCase()=='true');

                if(isChecked) {
                    if(hideIfTrue) {
                        $destination.hide();
                    } else {
                        $destination.show();
                    }
                } else {
                    if(hideIfTrue) {
                        $destination.show();
                    } else {
                        $destination.hide();
                    }
                }
            }
        }
    }
});

jQuery(function() {
    jQuery(".starrr").starrr();
    jQuery('#tcmp-rate').on('starrr:change', function(e, value){
        var url='https://wordpress.org/support/view/plugin-reviews/tracking-code-manager?rate=5#postform';
        window.open(url);
    });
    jQuery('#rate-box').show();
});