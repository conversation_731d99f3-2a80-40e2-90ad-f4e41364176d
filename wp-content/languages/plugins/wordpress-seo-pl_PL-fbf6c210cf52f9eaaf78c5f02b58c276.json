{"translation-revision-date": "2025-09-16 13:57:50+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n == 1) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "pl"}, "Black Friday: Our biggest sale just dropped!": ["Czarny Piątek: Właśnie ruszyła nasza największa wyprzedaż!"], "Thumbnail for the Black Friday announcement": ["Miniaturka ogłoszenia na Czarny Piątek"], "We added even more value than ever this year: %1$s, %2$s, %3$s, and seamless %4$s Docs add-on, all included. If you've been waiting to upgrade, now’s the time.": ["W tym roku dodaliśmy jeszcze więcej wartości niż kiedykolwiek: %1$s, %2$s, %3$s i bezproblemowy dodatek do Dokumentów %4$s – wszystko w pakiecie. <PERSON><PERSON><PERSON> czekałeś na aktualizację, teraz jest na to czas."], "We added even more value than ever this year: %1$s, %2$s, %3$s, %4$s, and seamless %5$s Docs add-on, all included. If you've been waiting to upgrade, now’s the time..": ["W tym roku dodaliśmy jeszcze więcej wartości niż kiedykolwiek: %1$s, %2$s, %3$s, %4$s i bezproblemowy dodatek do Dokumentów %5$s – wszystko w pakiecie. <PERSON><PERSON><PERSON> czekałeś na aktualizację, teraz jest na to czas."], "Get %1$s with 30%% off": ["Zdobądź %1$s ze zniżką 30%%"], "How does your brand show up in AI responses?": ["Jak Twoja marka prezentuje się w odpowiedziach AI?"], "Web chart showing aspects of brand visibility in AI responses": ["Wykres sieciowy przedstawiający aspekty widoczności marki w odpowiedziach AI"], "Track visibility, control perception, and stay ahead - tools to manage your AI presence are coming soon!": ["<PERSON><PERSON><PERSON> w<PERSON>, kontroluj percepcję i bądź na bieżąco - narzędzia do zarządzania obecnością AI będą już wkrótce dostępne!"], "Introducing %1$s": ["Przedstawiamy %1$s"], "Join the waitlist": ["Zapisz się na listę oczekujących"], "(Opens in a new browser tab)": ["(Otworzy się w nowej zakładce)"], "Close": ["Zamknij"]}}, "comment": {"reference": "js/dist/introductions.js"}}