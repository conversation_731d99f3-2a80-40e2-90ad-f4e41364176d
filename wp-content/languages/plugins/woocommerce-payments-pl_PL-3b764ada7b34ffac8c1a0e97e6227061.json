{"locale_data": {"messages": {"": {"plural_forms": "nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);", "language": "pl", "project_id_version": "WooCommerce - WooCommerce Payments"}, "Customer statements": ["Wyciągi klientów"], "Provide contact information where customers can reach you for support.": ["Podaj dane kontaktowe zespołu pomocy dla klientów."], "Customer bank statement (kana)": ["Wyciąg bankowy klienta (kana)"], "Use only kana characters.": ["Użyj tylko znaków kana."], "Customer bank statement (kanji)": ["Wyciąg bankowy klienta (kanji)"], "Use only kanji characters.": ["Użyj tylko znaków kanji."], "Use only latin characters.": ["Użyj tylko znaków alfabetu łacińskiego."], "Customer support": ["D<PERSON>ł obsługi klienta"], "When enabled, users will be able to pay with a saved card during checkout. Card details are stored in our platform, not on your store.": ["Po włączeniu użytkownicy będą mogli zapłacić zapisaną kartą podczas realizacji zamówienia. Dane karty są zapisywane na naszej platformie, a nie w <PERSON><PERSON> sklepie."], "Enable payments via saved cards": ["Włącz płatności za pomocą zapisanych kart"], "Transaction preferences": ["Preferencje dotyczące transakcji"], "Edit the way your store name appears on your customers' bank statements.": ["<PERSON><PERSON><PERSON><PERSON>, w jaki nazwa Twojego sklepu jest wyświetlana na wyciągach bankowych Twoich klientów."], "Customer bank statement": ["Wyciąg bankowy klienta"]}}}