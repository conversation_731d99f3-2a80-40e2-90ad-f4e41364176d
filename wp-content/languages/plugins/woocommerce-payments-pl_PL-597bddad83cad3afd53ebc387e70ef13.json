{"locale_data": {"messages": {"": {"plural_forms": "nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);", "language": "pl", "project_id_version": "WooCommerce - WooCommerce Payments"}, "When enabled, payment methods powered by %s will appear on checkout.": ["<PERSON><PERSON> ta funkcja będzie włączona, metody pła<PERSON>ności dostępne w usłudze %s pojawią się podczas finalizacji płatności."], "Enable %s": ["Włącz %s"]}}}