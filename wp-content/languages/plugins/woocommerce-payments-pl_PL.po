msgid ""
msgstr ""
"PO-Revision-Date: 2025-08-10 20:08:47+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: pl\n"
"Project-Id-Version: WooCommerce - WooCommerce Payments\n"

#. translators: 1: Anchor opening tag; 2: Anchor closing tag; 3: Anchor opening
#. tag; 4: Anchor closing tag
#: includes/admin/class-wc-payments-admin-settings.php:224
msgid "To begin accepting real payments you will need to first %1$sreset your account%2$s and, then, provide additional details about your business. %3$sLearn more%4$s"
msgstr ""

#: includes/admin/class-wc-payments-admin-settings.php:219
msgid "You are using a sandbox test account."
msgstr ""

#. translators: 1: Anchor opening tag; 2: Anchor closing tag; 3: Anchor opening
#. tag; 4: Anchor closing tag
#: includes/admin/class-wc-payments-admin-settings.php:154
#: includes/admin/class-wc-payments-admin-settings.php:235
msgid "To begin accepting real payments, please go to the live store or change your %1$sWordPress environment%2$s to a production one. %3$sLearn more%4$s"
msgstr ""

#: includes/admin/class-wc-payments-admin-settings.php:150
#: includes/admin/class-wc-payments-admin-settings.php:231
msgid "⚠️ Development mode is enabled for the store! There can be no live onboarding process while using development, testing, or staging WordPress environments!"
msgstr ""

#. translators: %s: URL to learn more
#: includes/admin/class-wc-payments-admin-settings.php:145
msgid "Provide additional details about your business so you can begin accepting real payments. %1$sLearn more%2$s"
msgstr ""

#. translators: 1: Anchor opening tag; 2: Anchor closing tag
#: includes/admin/class-wc-payments-admin-settings.php:101
msgid "You can use %1$stest card numbers%2$s to simulate various types of transactions."
msgstr ""

#. translators: %s: WooPayments
#: includes/admin/class-wc-payments-admin-settings.php:93
msgid "%s is in test mode — all transactions are simulated!"
msgstr ""

#: client/payment-details/transaction-breakdown/fees-breakdown/index.tsx:77
msgid "Transaction fees breakdown"
msgstr ""

#: client/checkout/blocks/payment-methods-logos/logo-popover.tsx:77
msgid "Supported Credit Card Brands"
msgstr ""

#. translators: %1$s terms page link, %2$s privacy page link.
#: includes/woopay/class-woopay-session.php:1032
msgid "By proceeding with your purchase you agree to our %1$s and %2$s"
msgstr ""

#: includes/payment-methods/class-cc-payment-method.php:42
msgid "Card"
msgstr ""

#: client/components/sandbox-mode-switch-to-live-notice/index.tsx:38
msgid "Learn more about test accounts"
msgstr "Dowiedz się więcej o kontach testowych"

#: includes/admin/class-wc-payments-admin-settings.php:140
msgid "You are using a test account."
msgstr "Korzystasz z konta testowego. "

#: client/disputes/new-evidence/index.tsx:515
#: client/payment-details/dispute-details/dispute-steps.tsx:162
msgid "<strong>The outcome of this dispute will be determined by the cardholder's bank.</strong> WooPayments has no influence over the decision and is not liable for any chargebacks."
msgstr "<strong>O wyniku sporu zadecyduje bank posiadacza karty.</strong> WooPayments nie ma wpływu na decyzję i nie odpowiada za obciążenia zwrotne."

#: client/disputes/new-evidence/index.tsx:514
#: client/payment-details/dispute-details/dispute-steps.tsx:161
msgid "<strong>The outcome of this dispute will be determined by %1$s.</strong> WooPayments has no influence over the decision and is not liable for any chargebacks."
msgstr "<strong>O wyniku sporu zadecyduje %1$s.</strong> WooPayments nie ma wpływu na decyzję i nie odpowiada za obciążenia zwrotne."

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:90
msgid "<strong>You accepted this dispute on %1$s.</strong>"
msgstr "<strong>Ten wniosek o rozstrzygnięcie sporu został przez Ciebie zaakceptowany %1$s.</strong>"

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:57
msgid "<strong>Good news — you've won this dispute! The customer's bank reached this decision on %1$s.</strong> Your account has been credited with the disputed amount and fee. <a>Learn more about preventing disputes.</a>"
msgstr "<strong>Dobra wiadomość — spór rozstrzygnięto na Twoją korzyść! Bank klienta podjął tę decyzję w dniu %1$s.</strong> Na Twoje konto wpłynęła kwestionowana kwota i opłata. <a>Dowiedz się więcej o zapobieganiu sporom.</a>"

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:54
msgid "<strong>Good news — you've won this dispute! The customer's bank, %1$s, reached this decision on %2$s.</strong> Your account has been credited with the disputed amount and fee. <a>Learn more about preventing disputes.</a>"
msgstr "<strong>Dobra wiadomość — spór rozstrzygnięto na Twoją korzyść! Bank klienta, %1$s, podjął tę decyzję w dniu %2$s.</strong> Na Twoje konto wpłynęła kwestionowana kwota i opłata. <a>Dowiedz się więcej o zapobieganiu sporom.</a>"

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:26
msgid "<strong>The customer's bank is currently reviewing the evidence you submitted on %1$s.</strong> This process can sometimes take more than 60 days — we'll let you know once a decision has been made. <a>Learn more about the dispute process.</a>"
msgstr "<strong>Bank klienta analizuje obecnie dowody przesłane przez Ciebie w dniu %1$s.</strong> Ten proces może potrwać ponad 60 dni — gdy decyzja zostanie podjęta, powiadomimy Cię o tym. <a>Dowiedz się więcej o procesie rozstrzygania sporów.</a>"

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:23
msgid "<strong>The customer's bank, %1$s, is currently reviewing the evidence you submitted on %2$s.</strong> This process can sometimes take more than 60 days — we'll let you know once a decision has been made. <a>Learn more about the dispute process.</a>"
msgstr "<strong>Bank klienta, %1$s, obecnie sprawdza dowody przesłane przez Ciebie w dniu %2$s.</strong> Ten proces może potrwać ponad 60 dni — gdy decyzja zostanie podjęta, powiadomimy Cię o tym. <a>Dowiedz się więcej o procesie rozstrzygania sporów.</a>"

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:106
msgid "<strong>Unfortunately, you've lost this dispute. The customer's bank reached this decision on %1$s.</strong>"
msgstr "<strong>Niestety, spór został rozstrzygnięty na Twoją niekorzyść. Bank klienta podjął tę decyzję w dniu %1$s.</strong>"

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:101
msgid "<strong>Unfortunately, you've lost this dispute. The customer's bank, %1$s, reached this decision on %2$s.</strong>"
msgstr "<strong>Niestety, spór został rozstrzygnięty na Twoją niekorzyść. Bank klienta, %1$s, podjął tę decyzję w dniu %2$s.</strong>"

#: client/payment-details/dispute-details/dispute-due-by-date.tsx:24
msgid " (Last day today)"
msgstr "(Dzisiaj ostatni dzień)"

#: client/disputes/new-evidence/recommended-document-fields.ts:241
msgid "A receipt from the shipping carrier or a tracking number, for example."
msgstr "Na przykład pokwitowanie od przewoźnika lub numer przesyłki."

#: client/components/dispute-status-chip/mappings.ts:9
#: client/disputes/strings.ts:172
msgid "Inquiry: Response needed"
msgstr "Zapytanie: wymagana odpowiedź"

#: client/components/dispute-status-chip/mappings.ts:21
#: client/disputes/strings.ts:175
msgid "Response needed"
msgstr "Wymagana odpowiedź"

#: client/disputes/new-evidence/recommended-document-fields.ts:214
msgid "A screenshot of your store's terms of service."
msgstr "Zrzut ekranu z warunkami świadczenia usług Twojego sklepu."

#: client/disputes/new-evidence/recommended-document-fields.ts:213
msgid "Terms of service"
msgstr "Warunki świadczenia usług"

#: client/disputes/new-evidence/recommended-document-fields.ts:120
msgid "Any documents showing the billing history, subscription status, or cancellation logs, for example."
msgstr "Na przykład dokumenty przedstawiające historię rozliczeń, status subskrypcji lub dzienniki anulowania."

#: client/disputes/new-evidence/recommended-document-fields.ts:48
msgid "Any other relevant documents that will support your case."
msgstr "Inne dokumenty istotne dla sprawy."

#: client/disputes/new-evidence/recommended-document-fields.ts:188
msgid "Any relevant documents showing the customer's signature, such as signed proof of delivery."
msgstr "Dokumenty z podpisem klienta, np. podpisany dowód dostawy."

#: client/disputes/new-evidence/recommended-document-fields.ts:42
msgid "Any correspondence with the customer regarding this purchase."
msgstr "Korespondencja z klientem dotycząca zakupu."

#: client/disputes/new-evidence/recommended-document-fields.ts:36
msgid "A copy of the customer's receipt, which can be found in the receipt history for this transaction."
msgstr "Kopia paragonu klienta, który można znaleźć w historii paragonów dla tej transakcji."

#: client/disputes/new-evidence/recommended-document-fields.ts:202
msgid "Such as billing history, subscription status, or cancellation logs."
msgstr "Na przykład historia rozliczeń, status subskrypcji lub dzienniki anulowania."

#: client/disputes/new-evidence/recommended-document-fields.ts:208
msgid "A screenshot of your store's refund policy."
msgstr "Zrzut ekranu przedstawiający zasady zwrotu kosztów obowiązujące w Twoim sklepie."

#: client/disputes/new-evidence/recommended-document-fields.ts:174
msgid "A screenshot of the item condition."
msgstr "Zrzut ekranu pokazujący stan produktu."

#: client/disputes/new-evidence/recommended-document-fields.ts:47
msgid "Other documents"
msgstr "Inne dokumenty"

#: client/disputes/new-evidence/recommended-document-fields.ts:201
msgid "Proof of active subscription"
msgstr "Dowód aktywnej subskrypcji"

#: client/disputes/new-evidence/recommended-document-fields.ts:173
msgid "Item condition"
msgstr "Stan produktu"

#: client/disputes/new-evidence/recommended-document-fields.ts:187
msgid "Customer's signature"
msgstr "Podpis klienta"

#. translators: %1$s: WooCommerce Subscriptions
#: includes/subscriptions/class-wc-payments-subscriptions-admin-notices.php:66
msgid "WooPayments no longer supports subscriptions capabilities and subscriptions data can no longer be accessed. Please install <a target=\"_blank\" href=\"%1$s\">WooCommerce Subscriptions</a> to continue managing your subscriptions."
msgstr "WooPayments nie obsługuje już funkcji subskrypcji ani nie zapewnia dostępu do związanych z nimi danych. Aby dalej zarządzać subskrypcjami, zainstaluj <a target=\"_blank\" href=\"%1$s\">WooCommerce Subscriptions</a>."

#. translators: %1$s: WooCommerce Subscriptions
#: includes/subscriptions/class-wc-payments-subscriptions-admin-notices.php:60
msgid "WooPayments no longer supports billing for existing customer subscriptions. All subscriptions data is read-only. Please install <a target=\"_blank\" href=\"%1$s\">WooCommerce Subscriptions</a> to continue managing your subscriptions."
msgstr "WooPayments nie obsługuje już rozliczania istniejących subskrypcji klientów. Wszystkie dane subskrypcji są dostępne w trybie tylko do odczytu. Aby dalej zarządzać subskrypcjami, zainstaluj <a target=\"_blank\" href=\"%1$s\">WooCommerce Subscriptions</a>."

#. translators: %1$s: WooCommerce Subscriptions
#: includes/subscriptions/class-wc-payments-subscriptions-admin-notices.php:54
msgid "WooPayments no longer allows customers to create new subscriptions. Beginning in version 9.8, billing for existing customer subscriptions will no longer be supported. To ensure there is no interruption of service, please install <a target=\"_blank\" href=\"%1$s\">WooCommerce Subscriptions</a>."
msgstr "WooPayments nie pozwala już klientom na tworzenie nowych subskrypcji. Od wersji 9.8 rozliczanie istniejących subskrypcji klientów przestanie być obsługiwane. Aby zapewnić ciągłość działania usługi, zainstaluj <a target=\"_blank\" href=\"%1$s\">WooCommerce Subscriptions</a>."

#. translators: %1$s: WooCommerce Subscriptions
#: includes/subscriptions/class-wc-payments-subscriptions-admin-notices.php:48
msgid "<strong>Important:</strong> From version 9.7 of WooPayments (scheduled for 23 July, 2025), you'll <strong>no longer be able to offer new product subscriptions</strong>. To avoid disruption, please install <a target=\"_blank\" href=\"%1$s\">WooCommerce Subscriptions</a>."
msgstr "<strong>Ważne:</strong> Od wersji 9.7 WooPayments (zaplanowanej na 23 lipca 2025 r.) <strong>nie będzie już można oferować nowych subskrypcji produktów</strong>. Aby uniknąć zakłóceń, zainstaluj <a target=\"_blank\" href=\"%1$s\">WooCommerce Subscriptions</a>."

#: includes/notes/class-wc-payments-notes-stripe-billing-deprecation.php:74
msgid "Install WooCommerce Subscriptions"
msgstr "Zainstaluj WooCommerce Subscriptions"

#: includes/notes/class-wc-payments-notes-stripe-billing-deprecation.php:68
msgid "WooPayments no longer supports subscriptions capabilities and subscriptions data can no longer be accessed. Please install WooCommerce Subscriptions to continue managing your subscriptions."
msgstr "WooPayments nie obsługuje już funkcji subskrypcji ani nie zapewnia dostępu do związanych z nimi danych. Aby dalej zarządzać subskrypcjami, zainstaluj WooCommerce Subscriptions."

#: includes/notes/class-wc-payments-notes-stripe-billing-deprecation.php:65
msgid "WooPayments no longer supports billing for existing customer subscriptions. All subscriptions data is read-only. Please install WooCommerce Subscriptions to continue managing your subscriptions."
msgstr "WooPayments nie obsługuje już rozliczania istniejących subskrypcji klientów. Wszystkie dane subskrypcji są dostępne w trybie tylko do odczytu. Aby dalej zarządzać subskrypcjami, zainstaluj WooCommerce Subscriptions."

#: includes/notes/class-wc-payments-notes-stripe-billing-deprecation.php:62
msgid "WooPayments no longer allows customers to create new subscriptions. Beginning in version 9.8, billing for existing customer subscriptions will no longer be supported. To ensure there is no interruption of service, please install WooCommerce Subscriptions."
msgstr "WooPayments nie pozwala już klientom na tworzenie nowych subskrypcji. Od wersji 9.8 rozliczanie istniejących subskrypcji klientów przestanie być obsługiwane. Aby zapewnić ciągłość działania usługi, zainstaluj WooCommerce Subscriptions."

#: includes/notes/class-wc-payments-notes-stripe-billing-deprecation.php:61
#: includes/notes/class-wc-payments-notes-stripe-billing-deprecation.php:64
#: includes/notes/class-wc-payments-notes-stripe-billing-deprecation.php:67
msgid "WooPayments subscriptions update"
msgstr "Aktualizacja subskrypcji WooPayments"

#: includes/notes/class-wc-payments-notes-stripe-billing-deprecation.php:59
msgid "From version 9.7 of WooPayments (scheduled for 23 July, 2025), you'll no longer be able to offer new product subscriptions using the built-in subscriptions functionality. To avoid disruption, please install WooCommerce Subscriptions for free."
msgstr "Od wersji 9.7 WooPayments (zaplanowanej na 23 lipca 2025 r.) nie będzie już można oferować nowych subskrypcji produktów za pomocą wbudowanej funkcji subskrypcji. Aby uniknąć zakłóceń, zainstaluj bezpłatnie WooCommerce Subscriptions."

#: includes/notes/class-wc-payments-notes-stripe-billing-deprecation.php:58
msgid "Important information regarding subscriptions in WooPayments"
msgstr "Ważne informacje dotyczące subskrypcji w WooPayments"

#: client/payment-details/utils/tax-descriptions.ts:36
msgid "NO VAT"
msgstr "BEZ PODATKU VAT"

#: client/disputes/new-evidence/recommended-document-fields.ts:35
msgid "Order receipt"
msgstr "Potwierdzenie zamówienia"

#: includes/class-wc-payments-onboarding-service.php:831
msgid "Failed to activate the account: account does not exist."
msgstr "Nie udało się aktywować konta: konto nie istnieje."

#: includes/class-wc-payments-onboarding-service.php:776
msgid "Failed to reset the account: account does not exist."
msgstr "Nie udało się zresetować konta: konto nie istnieje."

#: client/vat/form/tasks/vat-number-task.tsx:85
msgid "11-digit number, for example 12 ***********."
msgstr "Numer 11-cyfrowy, na przykład 12 ***********."

#: client/vat/form/tasks/vat-number-task.tsx:91
msgid "Enter your UEN (e.g., 200312345A) or GST Registration Number (e.g., M91234567X)."
msgstr "Wprowadź numer UEN (np. 200312345A) lub numer rejestracyjny GST (np. M91234567X)."

#: client/vat/form/tasks/vat-number-task.tsx:89
msgid "8-digit or 9-digit number, for example 99-999-999 or 999-999-999."
msgstr "Numer 8- lub 9-cyfrowy, na przykład 99-999-999 lub 999-999-999."

#: client/vat/form/tasks/vat-number-task.tsx:74
msgid "By providing your UEN or GST number you confirm you are a Singapore GST registered business and you are going to account for the GST."
msgstr "Podając numer UEN lub GST, potwierdzasz, że podmiot jest zarejestrowany jako płatnik podatku GST w Singapurze i zobowiązujesz się do rozliczania tego podatku."

#: client/vat/form/tasks/vat-number-task.tsx:72
msgid "By inputting your IRD number you confirm that you are going to account for the GST."
msgstr "Wprowadzając numer IRD, potwierdzasz zamiar rozliczania podatku GST."

#: client/vat/form/tasks/vat-number-task.tsx:70
msgid "By inputting your VAT number you confirm you are a Norway VAT registered business and that you are going to account for the VAT."
msgstr "Wprowadzając numer VAT, potwierdzasz, że podmiot jest zarejestrowany jako płatnik podatku VAT w Norwegii i zobowiązujesz się do rozliczania tego podatku."

#: client/vat/form/tasks/vat-number-task.tsx:64
msgid "By inputting your ABN number you confirm that you are going to account for the GST."
msgstr "Wprowadzając numer ABN, potwierdzasz zamiar rozliczania podatku GST."

#: client/vat/form/tasks/vat-number-task.tsx:56
msgid "UEN or GST Registration Number"
msgstr "Numer rejestracyjny UEN lub GST"

#: client/vat/form/tasks/vat-number-task.tsx:54
msgid "IRD Number"
msgstr "Numer IRD"

#: client/vat/form/tasks/vat-number-task.tsx:50
msgid "ABN"
msgstr "ABN"

#: client/vat/form/tasks/vat-number-task.tsx:153
msgid "I have a valid %1$s"
msgstr "Posiadam ważny %1$s"

#: client/settings/payment-methods-list/use-payment-method-availability.tsx:83
msgid "%s won't be visible to your customers until you provide the required information. Follow the instructions sent by our partner Stripe to your email."
msgstr "%s nie będzie widoczny dla klientów, dopóki nie uzupełnisz wymaganych danych. Postępuj zgodnie z instrukcjami przesłanymi na Twój adres e-mail przez naszego partnera, firmę Stripe."

#: client/settings/payment-methods-list/use-payment-method-availability.tsx:72
msgid "%s requires your store to be live and fully functional before it can be reviewed for use with their service. This approval process usually takes 2-3 days."
msgstr "%s wymaga, aby Twój sklep był aktywny i w pełni gotowy do działania, zanim będzie można go zweryfikować pod kątem korzystania z usług firmy. Proces zatwierdzania trwa zazwyczaj 2–3 dni."

#: client/settings/payment-methods-list/use-payment-method-availability.tsx:70
msgid "Approval pending"
msgstr "Oczekiwanie na zatwierdzenie"

#: client/settings/payment-methods-list/use-payment-method-availability.tsx:94
msgid "Your application to use %s has been rejected, please check your email for more information. Need help? {{contactSupportLink}}Contact support{{/contactSupportLink}}"
msgstr "Twój wniosek o korzystanie z usługi %s został odrzucony. Szczegóły znajdziesz w wiadomości e-mail. Potrzebujesz pomocy? {{contactSupportLink}}Skontaktuj się z działem obsługi klienta{{/contactSupportLink}}"

#: client/payment-details/utils/tax-descriptions.ts:50
msgid "JP JCT"
msgstr "JP JCT"

#: client/payment-details/utils/tax-descriptions.ts:49
msgid "CH VAT"
msgstr "CH VAT"

#: client/payment-details/utils/tax-descriptions.ts:47
msgid "SG GST"
msgstr "SG GST"

#: client/payment-details/utils/tax-descriptions.ts:46
msgid "NZ GST"
msgstr "NZ GST"

#: client/payment-details/utils/tax-descriptions.ts:45
msgid "AU GST"
msgstr "AU GST"

#: client/payment-details/utils/tax-descriptions.ts:43
msgid "SK VAT"
msgstr "SK VAT"

#: client/payment-details/utils/tax-descriptions.ts:42
msgid "SI VAT"
msgstr "SI VAT"

#: client/payment-details/utils/tax-descriptions.ts:41
msgid "SE VAT"
msgstr "SE VAT"

#: client/payment-details/utils/tax-descriptions.ts:40
msgid "RO VAT"
msgstr "RO VAT"

#: client/payment-details/utils/tax-descriptions.ts:39
msgid "PT VAT"
msgstr "PT VAT"

#: client/payment-details/utils/tax-descriptions.ts:38
msgid "PL VAT"
msgstr "PL VAT"

#: client/payment-details/utils/tax-descriptions.ts:37
msgid "NL VAT"
msgstr "NL VAT"

#: client/payment-details/utils/tax-descriptions.ts:35
msgid "MT VAT"
msgstr "MT VAT"

#: client/payment-details/utils/tax-descriptions.ts:34
msgid "LV VAT"
msgstr "LV VAT"

#: client/payment-details/utils/tax-descriptions.ts:33
msgid "LU VAT"
msgstr "LU VAT"

#: client/payment-details/utils/tax-descriptions.ts:32
msgid "LT VAT"
msgstr "LT VAT"

#: client/payment-details/utils/tax-descriptions.ts:31
msgid "IT VAT"
msgstr "VAT IT"

#: client/payment-details/utils/tax-descriptions.ts:30
msgid "IE VAT"
msgstr "IE VAT"

#: client/payment-details/utils/tax-descriptions.ts:29
msgid "HU VAT"
msgstr "HU VAT"

#: client/payment-details/utils/tax-descriptions.ts:28
msgid "HR VAT"
msgstr "VAT HR"

#: client/payment-details/utils/tax-descriptions.ts:27
msgid "GR VAT"
msgstr "GR VAT"

#: client/payment-details/utils/tax-descriptions.ts:26
msgid "UK VAT"
msgstr "UK VAT"

#: client/payment-details/utils/tax-descriptions.ts:25
msgid "FR VAT"
msgstr "FR VAT"

#: client/payment-details/utils/tax-descriptions.ts:24
msgid "FI VAT"
msgstr "FI VAT"

#: client/payment-details/utils/tax-descriptions.ts:23
msgid "ES VAT"
msgstr "ES VAT"

#: client/payment-details/utils/tax-descriptions.ts:22
msgid "EE VAT"
msgstr "EE VAT"

#: client/payment-details/utils/tax-descriptions.ts:21
msgid "DK VAT"
msgstr "DK VAT"

#: client/payment-details/utils/tax-descriptions.ts:20
msgid "DE VAT"
msgstr "DE VAT"

#: client/payment-details/utils/tax-descriptions.ts:19
msgid "CZ VAT"
msgstr "CZ VAT"

#: client/payment-details/utils/tax-descriptions.ts:18
msgid "CY VAT"
msgstr "CY VAT"

#: client/payment-details/utils/tax-descriptions.ts:17
msgid "BG VAT"
msgstr "BG VAT"

#: client/payment-details/utils/tax-descriptions.ts:16
msgid "BE VAT"
msgstr "BE VAT"

#: client/payment-details/utils/tax-descriptions.ts:15
msgid "AT VAT"
msgstr "AT VAT"

#: client/payment-details/transaction-breakdown/utils.ts:13
msgid "Tax on fee"
msgstr "Podatek od opłaty"

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:145
msgid "You submitted evidence for this inquiry on %1$s. <strong>%2$s</strong> is reviewing the case, which can take 120 days or more. You will be alerted when they make their final decision. <a>Learn more</a>."
msgstr "%1$s przesłano dowody dotyczące tego zapytania. <strong>%2$s</strong> rozpatruje sprawę, co może potrwać 120 dni lub dłużej. Po podjęciu ostatecznej decyzji otrzymasz powiadomienie. <a>Dowiedz się więcej</a>."

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:100
msgid "Learn more about responding to disputes"
msgstr "Dowiedz się więcej o sposobach reagowania na spory"

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:98
msgid "Learn more about payment inquiries"
msgstr "Dowiedz się więcej o zapytaniach związanych z płatnościami"

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:96
msgid "Please see this document for more information"
msgstr "Aby uzyskać więcej informacji, zapoznaj się z tym dokumentem"

#: includes/class-wc-payments-onboarding-service.php:669
#: includes/class-wc-payments-onboarding-service.php:771
#: includes/class-wc-payments-onboarding-service.php:826
msgid "Your store is not connected to WordPress.com. Please connect it first."
msgstr "Twój sklep nie jest połączony z witryną WordPress.com. Najpierw go połącz."

#: includes/class-wc-payments-onboarding-service.php:295
#: includes/class-wc-payments-onboarding-service.php:674
msgid "Onboarding initialization is already in progress. Please wait for it to finish."
msgstr "Inicjowanie wdrażania jest już rozpoczęty. Poczekaj na jego zakończenie."

#: includes/class-wc-payment-gateway-wcpay.php:3859
msgid "You cannot add a new payment method so soon after the previous one. Please try again later."
msgstr "Nowej metody płatności nie można dodać tak szybko po dodaniu poprzedniej. Spróbuj ponownie później."

#: client/settings/fraud-protection/advanced-settings/index.tsx:44
msgid "Set up advanced fraud filters. Enable at least one filter to activate advanced protection."
msgstr "Skonfiguruj zaawansowane filtry antyfraudowe. Włącz co najmniej jeden filtr, aby aktywować ochronę zaawansowaną."

#: client/settings/fraud-protection/advanced-settings/index.tsx:43
msgid "Filter configuration"
msgstr "Konfiguracja filtra"

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:57
msgid "This filter compares the purchase price of an order to the minimum and maximum purchase amounts that you specify. When enabled the payment will be blocked."
msgstr "Ten filtr porównuje cenę zakupu zamówienia z określoną przez Ciebie minimalną i maksymalną kwotą zakupu. Po włączeniu filtra płatność zostanie zablokowana."

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:57
msgid "Enable Purchase Price Threshold filter"
msgstr "Włącz filtr progu ceny zakupu"

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:43
msgid "This filter compares the amount of items in an order to the minimum and maximum counts that you specify. When enabled the payment will be blocked."
msgstr "Ten filtr porównuje liczbę pozycji na zamówieniu z określoną przez Ciebie liczbą minimalną i maksymalną. Po włączeniu filtra płatność zostanie zablokowana."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:43
msgid "Enable Order Items Threshold filter"
msgstr "Włącz filtr progu pozycji zamówienia"

#: client/settings/fraud-protection/advanced-settings/cards/ip-address-mismatch.tsx:16
msgid "This filter screens for customer's {{ipAddressLink}}IP address{{/ipAddressLink}} to see if it is in a different country than indicated in their billing address. When enabled the payment will be blocked."
msgstr "Ten filtr sprawdza, czy {{ipAddressLink}}adres IP{{/ipAddressLink}} klienta pochodzi z innego kraju niż adres rozliczeniowy. Po włączeniu filtra płatność zostanie zablokowana."

#: client/settings/fraud-protection/advanced-settings/cards/ip-address-mismatch.tsx:15
msgid "Enable IP Address Mismatch filter"
msgstr "Włącz filtr niezgodności adresów IP"

#: client/settings/fraud-protection/advanced-settings/cards/international-ip-address.tsx:23
msgid "This filter screens for {{ipAddressLink}}IP addresses{{/ipAddressLink}} outside of your {{supportedCountriesLink}}supported countries{{/supportedCountriesLink}}. When enabled the payment will be blocked."
msgstr "Ten filtr wykrywa {{ipAddressLink}}adresy IP{{/ipAddressLink}} spoza {{supportedCountriesLink}}obsługiwanych krajów{{/supportedCountriesLink}}. Po włączeniu filtra płatność zostanie zablokowana."

#: client/settings/fraud-protection/advanced-settings/cards/international-ip-address.tsx:22
msgid "Enable International IP Address filter"
msgstr "Włącz filtr międzynarodowych adresów IP"

#: client/settings/fraud-protection/advanced-settings/cards/avs-mismatch.tsx:14
msgid "This filter compares the street number and the post code submitted by the customer against the data on file with the card issuer. When enabled the payment will be blocked."
msgstr "Ten filtr porównuje przesłany przez klienta adres i kod pocztowy z danymi w systemie wydawcy karty. Po włączeniu filtra płatność zostanie zablokowana."

#: client/settings/fraud-protection/advanced-settings/cards/avs-mismatch.tsx:14
msgid "Enable AVS Mismatch filter"
msgstr "Włącz filtr niezgodności AVS"

#: client/settings/fraud-protection/advanced-settings/cards/address-mismatch.tsx:13
msgid "This filter screens for differences between the shipping information and the billing information (country). When enabled the payment will be blocked."
msgstr "Ten filtr wykrywa różnice między danymi wysyłkowymi i rozliczeniowymi (kraj). Po włączeniu filtra płatność zostanie zablokowana."

#: client/settings/fraud-protection/advanced-settings/cards/address-mismatch.tsx:13
msgid "Enable Address Mismatch filter"
msgstr "Włącz filtr niezgodności adresów"

#: includes/class-wc-payments-onboarding-service.php:855
msgid "Failed to disable test drive account."
msgstr "Nie udało się wyłączyć konta testowego."

#: includes/class-wc-payments-onboarding-service.php:787
msgid "Failed to delete account."
msgstr "Nie udało się usunąć konta."

#: client/payment-details/transaction-breakdown/utils.ts:15
msgid "International payment fee"
msgstr "Opłata za płatność międzynarodową"

#: includes/woopay/class-woopay-order-status-sync.php:65
msgid "WooPayments woopay order status sync"
msgstr "Synchronizacja statusu zamówienia WooPayments woopay"

#. Translators: %1$s contains failure message. %2$s contains error code.
#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:285
msgid "Payment for the order failed with the following message: \"%1$s\" and failure code <code>%2$s</code>"
msgstr "Płatność za zamówienie nie powiodła się z komunikatem: „%1$s” oraz kodem błędu <code>%2$s</code>"

#. Translators: %1$d Number of failed renewal attempts. %2$s contains failure
#. message, %3$s contains error code.
#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:271
msgid "WooPayments subscription renewal attempt %1$d failed with the following message \"%2$s\" and failure code <code>%3$s</code>"
msgid_plural "WooPayments subscription renewal attempt %1$d failed with the following message \"%2$s\" and failure code <code>%3$s</code>"
msgstr[0] "Próba odnowienia subskrypcji WooPayments %1$d nie powiodła się z komunikatem „%2$s” oraz kodem błędu <code>%3$s</code>"
msgstr[1] "Próba odnowienia subskrypcji WooPayments %1$d nie powiodła się z komunikatem „%2$s” oraz kodem błędu <code>%3$s</code>"
msgstr[2] "Próba odnowienia subskrypcji WooPayments %1$d nie powiodła się z komunikatem „%2$s” oraz kodem błędu <code>%3$s</code>"

#: includes/constants/class-refund-failure-reason.php:43
msgid "An unknown error occurred while processing the refund."
msgstr "Podczas przetwarzania zwrotu kosztów wystąpił nieznany błąd."

#: includes/constants/class-refund-failure-reason.php:42
msgid "The refund was canceled at your request."
msgstr "Zwrot kosztów został anulowany na Twoją prośbę."

#: includes/constants/class-refund-failure-reason.php:41
msgid "The refund was declined by the card issuer."
msgstr "Zwrot kosztów został odrzucony przez wydawcę karty."

#: includes/constants/class-refund-failure-reason.php:40
msgid "Insufficient funds in your WooPayments balance."
msgstr "Niewystarczające środki na saldzie WooPayments."

#: includes/constants/class-refund-failure-reason.php:39
msgid "The charge for this refund is being disputed by the customer."
msgstr "Obciążenie za ten zwrot kosztów jest kwestionowane przez klienta."

#: includes/constants/class-refund-failure-reason.php:38
msgid "The card used for the original payment has expired or been canceled."
msgstr "Karta pierwotnie użyta do płatności wygasła lub została anulowana."

#: includes/constants/class-refund-failure-reason.php:37
msgid "The card used for the original payment has been reported lost or stolen."
msgstr "Karta pierwotnie użyta do płatności została zgłoszona jako zgubiona lub skradziona."

#: includes/payment-methods/class-afterpay-payment-method.php:54
msgid "Cash App Afterpay"
msgstr "Cash App Afterpay"

#: includes/class-wc-payment-gateway-wcpay.php:1022
#: includes/class-wc-payment-gateway-wcpay.php:1027
msgid "Return to payments"
msgstr "Wróć do płatności"

#: client/settings/transactions/manual-capture-control.tsx:53
msgid "Manual capture is not available when Stripe Billing is active."
msgstr "Ręczne przechwytywanie nie jest dostępne, gdy aktywna jest funkcja Stripe Billing."

#: client/settings/advanced-settings/stripe-billing-section.tsx:84
msgid "Stripe Billing is not available with <b>manual capture enabled</b>. To use Stripe Billing, disable manual capture in your settings list."
msgstr "Funkcja Stripe Billing nie jest dostępna przy <b>włączonym ręcznym przechwytywaniu</b>. Aby skorzystać z funkcji Stripe Billing, wyłącz ręczne przechwytywanie na liście ustawień."

#: client/settings/advanced-settings/stripe-billing-section.tsx:83
msgid "OK"
msgstr "OK"

#: client/settings/advanced-settings/stripe-billing-section.tsx:82
msgid "Enable Stripe Billing"
msgstr "Włącz Stripe Billing"

#: templates/emails/email-ipp-receipt-store-details.php:92
msgid "Date:"
msgstr "Data:"

#: templates/emails/email-ipp-receipt-store-details.php:74
msgid "Contact:"
msgstr "Kontakt:"

#: templates/emails/email-ipp-receipt-store-details.php:37
msgid "Address:"
msgstr "Addres:"

#: templates/emails/email-ipp-receipt-store-details.php:28
msgid "Store Name:"
msgstr "Nazwa sklepu:"

#: templates/emails/email-ipp-receipt-store-details.php:22
msgid "Store Details"
msgstr "Szczegóły sklepu"

#: includes/subscriptions/class-wc-payments-subscription-service.php:904
msgid "Subscription was made when WooPayments was in the live mode and cannot be renewed in the test mode."
msgstr "Subskrypcja została wykupiona, gdy usługa WooPayments działała w trybie produkcyjnym i nie można jej odnowić w trybie testowym."

#: includes/subscriptions/class-wc-payments-subscription-service.php:902
msgid "Subscription was made when WooPayments was in the test mode and cannot be renewed in the live mode."
msgstr "Subskrypcja została wykupiona, gdy usługa WooPayments działała w trybie testowym i nie można jej odnowić w trybie produkcyjnym."

#. translators: %1: the refund amount, %2: WooPayments, %3: reason, %4: refund
#. id, %5: status text
#: includes/class-wc-payments-order-service.php:2007
msgid "A refund of %1$s %5$s using %2$s. Reason: %3$s. (<code>%4$s</code>)"
msgstr "Zwrot kosztów w kwocie %1$s %5$s przy użyciu %2$s. Powód: %3$s. (<code>%4$s</code>)"

#. translators: %1: the refund amount, %2: WooPayments, %3: ID of the refund,
#. %4: status text
#: includes/class-wc-payments-order-service.php:1993
msgid "A refund of %1$s %4$s using %2$s (<code>%3$s</code>)."
msgstr "Zwrot kosztów w kwocie %1$s %4$s przy użyciu %2$s (<code>%3$s</code>)."

#: includes/class-wc-payments-order-service.php:1987
msgid "was successfully processed"
msgstr "został przetworzony pomyślnie"

#: includes/class-wc-payments-order-service.php:1985
msgid "is pending"
msgstr "jest w toku"

#: includes/class-wc-payments-order-service.php:1531
msgid "unsuccessful"
msgstr "niepowodzenie"

#: includes/admin/class-wc-rest-payments-settings-option-controller.php:87
msgid "Invalid value type; must be either boolean or array"
msgstr "Nieprawidłowy typ wartości; wymagana jest wartość logiczna lub tablica"

#: includes/payment-methods/class-multibanco-payment-method.php:67
msgid "A voucher based payment method for your customers in Portugal."
msgstr "Metoda płatności wykorzystująca kupony dla klientów w Portugalii."

#. translators: %1$s: The current site domain name. %2$s: The original site
#. domain name.
#: woocommerce-payments.php:411
msgid "<span>Your site is in Safe Mode because <hostname>%1$s</hostname> appears to be a staging or development copy of <hostname>%2$s</hostname>.</span> Two sites that are telling WooPayments they’re the same site. <safeModeLink>Learn more about Safe Mode issues</safeModeLink>."
msgstr "<span>Witryna działa w trybie bezpiecznym, ponieważ <hostname>%1$s</hostname> wygląda na wersję tymczasową lub deweloperską <hostname>%2$s</hostname>.</span> Dwie witryny zgłaszają WooPayments, że są tą samą witryną. <safeModeLink>Dowiedz się więcej o problemach z trybem bezpiecznym</safeModeLink>."

#. translators: %s: WooPayments.
#: woocommerce-payments.php:402
msgid "<p><strong>Recommended for</strong></p><list><item>short-lived test sites</item><item>sites that will be cloned back to production after testing</item></list><p><strong>Please note</strong> that staying in Safe Mode will cause issues for some %s features such as dispute and refund updates, payment confirmations for local payment methods. <safeModeLink>Learn more</safeModeLink>.</p>"
msgstr "<p><strong>Zalecane w przypadku</strong></p><list><item>tymczasowych witryn testowych</item><item>, które po testach zostaną sklonowane do środowiska produkcyjnego</item></list><p><strong>Warto pamiętać</strong>, że pozostanie w trybie bezpiecznym spowoduje problemy z niektórymi funkcjami %s, takimi jak aktualizacje dotyczące sporów i zwrotów kosztów oraz potwierdzenia płatności w przypadku lokalnych metod płatności. <safeModeLink>Dowiedz się więcej</safeModeLink>.</p>"

#. translators: %1$s: The current site domain name. %2$s: The original site
#. domain name. %3$s: WooPayments.
#: woocommerce-payments.php:390
msgid "<p><strong>Recommended for</strong></p><list><item>development sites</item><item>sites that need access to all %3$s features</item></list><p><strong>Please note</strong> that creating a fresh connection for <hostname>%1$s</hostname> would require restoring the connection on <hostname>%2$s</hostname> if that site is cloned back to production. <safeModeLink>Learn more</safeModeLink>.</p>"
msgstr "<p><strong>Zalecane w przypadku</strong></p><list><item>witryn deweloperskich</item><item>, które potrzebują dostępu do wszystkich %3$sfunkcji</item></list><p><strong>Warto pamiętać</strong>, że utworzenie nowego połączenia dla <hostname>%1$s</hostname> wymagałoby przywrócenia połączenia w witrynie <hostname>%2$s</hostname> w przypadku jej sklonowania do środowiska produkcyjnego. <safeModeLink>Dowiedz się więcej</safeModeLink>.</p>"

#: woocommerce-payments.php:350
msgid "Stay in Safe Mode"
msgstr "Pozostań w trybie awaryjnym"

#. translators: %s: expiry date
#: includes/class-wc-payments-order-success-page.php:98
#: includes/class-wc-payments-order-success-page.php:562
msgid "Expires <strong>%s</strong>"
msgstr "Data ważności <strong>%s</strong>"

#: includes/class-wc-payments-order-success-page.php:520
msgid "Multibanco Payment instructions"
msgstr "Instrukcje dotycząca płatności Multibanco"

#. translators: %s: checkout URL
#: includes/class-wc-payments-order-success-page.php:425
msgid "Unfortunately, your order has failed. Please <a href=\"%s\">try checking out again</a>."
msgstr "Zamówienie nie powiodło się. <a href=\"%s\">Spróbuj sfinalizować płatność ponownie</a>."

#: includes/class-wc-payments-order-success-page.php:146
msgid "Copy link for sharing"
msgstr "Skopiuj link do udostępniania"

#: includes/class-wc-payments-order-success-page.php:145
msgid "Print"
msgstr "Wydrukuj"

#: includes/class-wc-payments-order-success-page.php:132
#: includes/class-wc-payments-order-success-page.php:529
#: includes/class-wc-payments-order-success-page.php:585
msgid "Entity"
msgstr "Podmiot"

#: includes/class-wc-payments-order-success-page.php:126
#: includes/class-wc-payments-order-success-page.php:528
#: includes/class-wc-payments-order-success-page.php:579
msgid "Enter the entity number, reference number, and amount."
msgstr "Umożliwia wprowadzenie numeru podmiotu, numeru referencyjnego i kwoty."

#: includes/class-wc-payments-order-success-page.php:125
#: includes/class-wc-payments-order-success-page.php:527
#: includes/class-wc-payments-order-success-page.php:578
msgid "Click \"Payments of services/shopping\"."
msgstr "Kliknij opcję „Płatności za usługi/zakupy”."

#: includes/class-wc-payments-order-success-page.php:124
#: includes/class-wc-payments-order-success-page.php:526
#: includes/class-wc-payments-order-success-page.php:577
msgid "In your online bank account or from an ATM, choose \"Payment and other services\"."
msgstr "Na rachunku bankowym online lub w bankomacie wybierz „Płatności i inne usługi”."

#: includes/class-wc-payments-order-success-page.php:122
#: includes/class-wc-payments-order-success-page.php:575
msgid "Payment instructions"
msgstr "Instrukcje dotyczące płatności"

#. translators: %s: expiry date
#: includes/class-wc-payments-order-success-page.php:523
msgid "Expires %s"
msgstr "Wygasa %s"

#. translators: %s: order number
#: includes/class-wc-payments-order-success-page.php:90
#: includes/class-wc-payments-order-success-page.php:552
msgid "Order #%s"
msgstr "Zamówienie: #%s"

#: includes/class-wc-payments-order-success-page.php:76
msgid "Your order is on hold until payment is received. Please follow the payment instructions by the expiry date."
msgstr "Zamówienie jest wstrzymane do czasu otrzymania płatności. Postępuj zgodnie z instrukcjami dotyczącymi płatności przed upływem daty ważności."

#: includes/class-wc-payment-gateway-wcpay.php:3597
msgid "Payment method updated for all your current subscriptions."
msgstr "Metoda płatności zaktualizowana dla wszystkich twoich obecnych subskrypcji."

#: includes/class-wc-payment-gateway-wcpay.php:3594
msgid "Payment method updated."
msgstr "Metoda płatności zaktualizowana."

#: client/settings/google-pay-test-mode-compatibility-notice.tsx:28
msgid "Google Pay is incompatible with test mode. {{learnMore}}Learn more{{/learnMore}}."
msgstr "Usługa Google Pay jest niezgodna z trybem testowym. {{learnMore}}Dowiedz się więcej{{/learnMore}}."

#: client/merchant-feedback-prompt/positive-modal.tsx:18
msgid "Share your feedback"
msgstr "Podziel się swoimi uwagami"

#: client/utils/charge/index.ts:149
msgid "Online store"
msgstr "Sklep internetowy"

#: client/transactions/filters/config.ts:272
#: client/transactions/list/index.tsx:109
msgid "Sales channel"
msgstr "Kanał sprzedaży"

#: includes/payment-methods/Configs/Definitions/AlipayDefinition.php:77
msgid "Alipay is a popular wallet in China, operated by Ant Financial Services Group, a financial services provider affiliated with Alibaba."
msgstr "Alipay to popularny portfel w Chinach, obsługiwany przez Ant Financial Services Group, dostawcę usług finansowych powiązanego z Alibabą."

#: client/disputes/index.tsx:316 client/transactions/list/index.tsx:454
msgid "We’re processing your export. 🎉 The file will download automatically and be emailed to %s."
msgstr "Trwa przetwarzanie eksportu. 🎉 Plik zostanie pobrany automatycznie i wysłany e-mailem na adres %s."

#: includes/payment-methods/Configs/Definitions/WechatPayDefinition.php:77
msgid "A digital wallet popular with customers from China."
msgstr "Portfel cyfrowy popularny wśród klientów z Chin."

#: includes/payment-methods/class-grabpay-payment-method.php:69
msgid "A popular digital wallet for cashless payments in Singapore."
msgstr "Popularny portfel cyfrowy do płatności bezgotówkowych w Singapurze."

#: includes/payment-methods/Configs/Definitions/WechatPayDefinition.php:56
msgid "WeChat Pay"
msgstr "WeChat Pay"

#: includes/payment-methods/class-grabpay-payment-method.php:48
msgid "GrabPay"
msgstr "GrabPay"

#. translators: %1$s: Formatted refund amount
#: includes/class-wc-payments-order-service.php:2426
msgid "Refund of %1$s <strong>failed</strong> due to insufficient funds in your WooPayments balance."
msgstr "Zwrot %1$s <strong>nie powiódł się</strong> z powodu niewystarczających środków na saldzie WooPayments."

#. translators: %s: Formatted refund amount
#: includes/class-wc-payments-order-service.php:2406
msgid "Refund of %s <strong>failed</strong> due to insufficient funds in your WooPayments balance. To prevent delays in refunding customers, please consider adding funds to your Future Refunds or Disputes (FROD) balance. <a>Learn more</a>."
msgstr "Zwrot %s <strong>nie powiódł się</strong> z powodu niewystarczających środków na saldzie WooPayments. Aby zapobiec opóźnieniom w zwrocie kosztów klientom, rozważ dodanie środków do salda przyszłych zwrotów kosztów lub sporów (FROD). <a>Dowiedz się więcej</a>."

#: includes/admin/class-wc-rest-payments-refunds-controller.php:61
msgid "Failed to create refund"
msgstr "Nie udało się utworzyć zwrotu kosztów"

#. translators: %1$s terms page link, %2$s privacy page link.
#: includes/woopay/class-woopay-session.php:1024
msgid "You must accept our %1$s and %2$s to continue with your purchase."
msgstr "Aby kontynuować zakup, konieczne jest zaakceptowanie dokumentów: %1$s i %2$s."

#: includes/woopay/class-woopay-session.php:1019
msgid "Terms and Conditions"
msgstr "Regulamin"

#. translators: %1: the authorized amount, %2: WooPayments, %3: transaction ID
#. of the payment, %4: timestamp
#: includes/class-wc-payments-order-service.php:1629
msgid "A terminal payment of %1$s <strong>failed</strong> using %2$s (<a>%3$s</a>)"
msgstr "Płatność przez terminal w kwocie %1$s <strong>nie powiodła się</strong> przy użyciu %2$s (<a>%3$s</a>)"

#: client/data/authorizations/actions.ts:23
msgid "The minimum amount that can be processed is %1$s %2$s."
msgstr "Minimalna kwota, która może zostać przetworzona, to %1$s %2$s."

#: client/data/authorizations/actions.ts:17
msgid "The payment amount is too small to be processed."
msgstr "Kwota płatności jest zbyt mała, aby można ją było przetworzyć."

#. translators: %s: formatted minimum amount with currency
#: includes/class-wc-payment-gateway-wcpay.php:3329
msgid "The minimum amount to capture is %s."
msgstr "Minimalna kwota, która może zostać przechwycona, to %s."

#: client/payment-details/transaction-breakdown/utils.ts:16
#: client/utils/account-fees.tsx:108
msgid "Currency conversion fee"
msgstr "Opłata za przeliczenie waluty"

#: client/data/authorizations/actions.ts:111
msgid "There has been an error capturing the payment for order #%s."
msgstr "Podczas rejestrowania płatności za zamówienie nr %s wystąpił błąd."

#: client/data/authorizations/actions.ts:45
msgid "Unable to process the payment. Please try again later."
msgstr "Nie można przetworzyć płatności. Spróbuj ponownie później."

#: client/data/authorizations/actions.ts:39
msgid "An unexpected error occurred. Please try again later."
msgstr "Wystąpił nieoczekiwany błąd. Spróbuj ponownie później."

#: client/data/authorizations/actions.ts:38
msgid "The payment cancellation failed to complete."
msgstr "Nie udało się anulować płatności."

#: client/data/authorizations/actions.ts:36
msgid "The payment capture failed to complete."
msgstr "Nie udało się zarejestrować płatności."

#: client/data/authorizations/actions.ts:29
msgid "This payment cannot be processed in its current state."
msgstr "Nie można przetworzyć płatności w obecnym stanie."

#: client/data/authorizations/actions.ts:28
msgid "The payment cannot be processed due to a mismatch with order details."
msgstr "Nie można przetworzyć płatności z powodu niezgodnych danych zamówienia."

#: client/data/authorizations/actions.ts:27
msgid "Payment cannot be processed for partially or fully refunded orders."
msgstr "Nie można przetworzyć płatności za zamówienia częściowo lub w całości zwrócone."

#: client/data/authorizations/actions.ts:26
msgid "The order could not be found."
msgstr "Nie znaleziono zamówienia."

#: client/data/authorizations/actions.ts:153
msgid "There has been an error canceling the payment for order #%s."
msgstr "Podczas anulowania płatności za zamówienie nr %s wystąpił błąd."

#: src/Internal/Service/Level3Service.php:192
msgid "Rounding fix"
msgstr "Poprawka zaokrąglania"

#: client/components/sandbox-mode-switch-to-live-notice/modal/index.tsx:59
#: client/overview/task-list/strings.tsx:89
msgid "Activate payments"
msgstr "Aktywuj płatności"

#: client/components/sandbox-mode-switch-to-live-notice/modal/index.tsx:57
msgid "In order to receive payouts, you will need to provide your bank details."
msgstr "Aby otrzymywać wypłaty, musisz podać swoje dane bankowe."

#: client/components/sandbox-mode-switch-to-live-notice/modal/index.tsx:53
msgid "To use %s, you will need to verify your business details."
msgstr "Aby korzystać z usługi %s, musisz zweryfikować dane swojej firmy."

#: client/components/sandbox-mode-switch-to-live-notice/modal/index.tsx:47
msgid "Your test account will be deactivated, but your transactions can be found in your order history."
msgstr "Twoje konto testowe zostanie zdezaktywowane, ale transakcje będą dostępne w historii zamówień."

#: client/components/sandbox-mode-switch-to-live-notice/modal/index.tsx:43
msgid "Before continuing, please make sure that you're aware of the following:"
msgstr "Zanim przejdziesz dalej, weź pod uwagę te kwestie:"

#: client/components/sandbox-mode-switch-to-live-notice/modal/index.tsx:40
msgid "Activate payments on your store"
msgstr "Aktywuj płatności w swoim sklepie"

#: client/components/sandbox-mode-switch-to-live-notice/index.tsx:32
msgid "{{div}}{{strong}}You're using a test account.{{/strong}} To accept payments from shoppers, {{switchToLiveLink}}activate your %1$s account.{{/switchToLiveLink}}{{/div}}{{learnMoreIcon/}}"
msgstr "{{div}}{{strong}}Korzystasz z konta testowego.{{/strong}} Aby akceptować płatności od kupujących, {{switchToLiveLink}}aktywuj swoje konto %1$s.{{/switchToLiveLink}}{{/div}}{{learnMoreIcon/}}"

#: client/utils/charge/index.ts:145
msgid "In-Person (POS)"
msgstr "Osobiście (POS)"

#. translators: %1: the dispute status
#: includes/class-wc-payments-order-service.php:1943
msgid "Payment inquiry has been closed with status %1$s. See <a>payment status</a> for more details."
msgstr "Zapytanie dotyczące płatności zostało zamknięte ze statusem %1$s. Aby uzyskać więcej informacji, sprawdź <a>status płatności</a>."

#. translators: %1: the disputed amount and currency; %2: the dispute reason;
#. %3 the deadline date for responding to the inquiry
#: includes/class-wc-payments-order-service.php:1900
msgid "A payment inquiry has been raised for %1$s with reason \"%2$s\". <a>Response due by %3$s</a>."
msgstr "Utworzono zgłoszenie dotyczące kwoty %1$s z powodem: „%2$s”. <a>Termin odpowiedzi: %3$s</a>."

#: client/deposits/list/index.tsx:87
msgid "Bank reference ID"
msgstr "Identyfikator referencyjny banku"

#: includes/class-wc-payments-order-success-page.php:235
msgid "•••"
msgstr "•••"

#: client/transactions/list/index.tsx:212
msgid "Payout date"
msgstr "Data wypłaty"

#: client/deposits/list/index.tsx:199
msgid "Payout history"
msgstr "Historia wypłat"

#. Translators: %s is a test card number.
#: includes/payment-methods/class-cc-payment-method.php:78
msgid "Use test card <number>%s</number> or refer to our <a>testing guide</a>."
msgstr "Użyj testowej karty <number>%s</number> lub zapoznaj się z naszym <a>podręcznikiem testowania</a>."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:238
msgid "This order has a high risk of being fraudulent. We suggest contacting the customer to confirm their details before fulfilling it."
msgstr "W przypadku tego zamówienia istnieje duże ryzyko oszustwa. Przed zrealizowaniem zamówienia zalecamy skontaktowanie się z klientem, aby potwierdzić jego dane."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:237
msgid "This order has a moderate risk of being fraudulent. We suggest contacting the customer to confirm their details before fulfilling it."
msgstr "W przypadku tego zamówienia istnieje umiarkowane ryzyko oszustwa. Przed zrealizowaniem zamówienia zalecamy skontaktowanie się z klientem, aby potwierdzić jego dane."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:236
msgid "This payment shows a lower than normal risk of fraudulent activity."
msgstr "W przypadku tej płatności ryzyko nieuczciwego działania jest mniejsze niż zwykle."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:108
msgid "Adjust risk filters"
msgstr "Dostosuj filtry ryzyka"

#: includes/class-wc-payments-onboarding-service.php:399
msgid "Failed to finalize onboarding session."
msgstr "Nie udało się sfinalizować sesji wdrażania."

#. translators: link to Stripe testing page
#: includes/class-wc-payments-checkout.php:372
#: includes/class-wc-payments-checkout.php:481
msgid "Click to copy the test number to clipboard"
msgstr "Kliknij, aby skopiować numer testowy do schowka"

#. translators: 1: WooPayments.
#: includes/admin/class-wc-payments-admin.php:1197
msgid "Please <b>complete your %1$s setup</b> to continue using it."
msgstr "<b>Dokończ konfigurację usługi %1$s</b>, aby nadal z niej korzystać."

#: includes/class-wc-payments-checkout.php:372
#: client/components/copy-button/index.tsx:19
msgid "Copy to clipboard"
msgstr "Skopiuj do schowka"

#: includes/payment-methods/class-klarna-payment-method.php:104
msgid "Allow customers to pay over time or pay now with Klarna."
msgstr "Daj klientom możliwość płacenia z opóźnieniem lub od razu przy użyciu Klarna."

#: includes/payment-methods/class-afterpay-payment-method.php:121
msgid "Allow customers to pay over time with Afterpay."
msgstr "Daj klientom możliwość płacenia z opóźnieniem przy użyciu Afterpay."

#: includes/payment-methods/class-afterpay-payment-method.php:115
msgid "Allow customers to pay over time with Clearpay."
msgstr "Daj klientom możliwość płacenia z opóźnieniem przy użyciu Clearpay."

#: includes/payment-methods/class-affirm-payment-method.php:70
msgid "Allow customers to pay over time with Affirm."
msgstr "Daj klientom możliwość płacenia z opóźnieniem przy użyciu Affirm."

#: includes/multi-currency/client/utils/missing-currencies-message/index.ts:9
msgid "%1$s requires the %2$s currency. In order to enable the payment method, you must add this currency to your store."
msgid_plural "%1$s requires the %2$s currencies. In order to enable the payment method, you must add these currencies to your store."
msgstr[0] "%1$s wymaga %2$s waluty. Aby włączyć metodę płatności, musisz dodać tę walutę do sklepu."
msgstr[1] "%1$s wymaga %2$s walut. Aby włączyć metodę płatności, musisz dodać te waluty do sklepu."
msgstr[2] "%1$s wymaga %2$s walut. Aby włączyć metodę płatności, musisz dodać te waluty do sklepu."

#: includes/class-wc-payments-webhook-processing-service.php:809
msgid "The mandate used for this renewal payment is invalid. You may need to bring the customer back to your store and ask them to resubmit their payment information."
msgstr "Upoważnienie użyte do płatności za odnowienie jest nieprawidłowe. Być może trzeba poprosić klienta o ponowne przesłanie informacji o płatności w Twoim sklepie."

#. translators: %s: WooPayments.
#: includes/class-wc-payments-account.php:1709
msgid "There was a problem setting up your %s account. Please try again."
msgstr "Podczas konfigurowania konta %s wystąpił problem. Spróbuj ponownie."

#. translators: 1: anchor opening markup 2: closing anchor markup
#: includes/class-wc-payments-account.php:1659
msgid "Another account setup session is already in progress. Please finish it or %1$sclick here to start again%2$s."
msgstr "Inna sesja konfiguracji konta jest już w toku. Zakończ ją lub %1$skliknij tutaj, aby rozpocząć od nowa%2$s."

#. translators: 1: WooPayments.
#: includes/class-wc-payments-account.php:1510
msgid "Please <b>complete your %1$s setup</b> to process transactions."
msgstr "<b>Dokończ konfigurację %1$s</b>, aby przetwarzać transakcje."

#: includes/class-wc-payments.php:2112
msgid "is required to create an WooPay account."
msgstr "— to pole jest wymagane do utworzenia konta WooPay."

#: includes/class-wc-payments.php:2112
msgid "Mobile Number"
msgstr "Numer telefonu komórkowego"

#. translators: %1$s flat fee addon price in order
#: includes/multi-currency/Compatibility/WooCommerceProductAddOns.php:303
msgctxt "flat fee addon price in order"
msgid " (+ %1$s)"
msgstr " (+ %1$s)"

#. translators: %1$s custom addon price in order
#: includes/multi-currency/Compatibility/WooCommerceProductAddOns.php:309
msgctxt "custom addon price in order"
msgid " (%1$s)"
msgstr " (%1$s)"

#: includes/class-wc-payments-payment-request-session-handler.php:65
msgid "Invalid token: cookie and session customer mismatch"
msgstr "Nieprawidłowy token: niezgodność danych klienta między plikiem cookie a sesją"

#: client/vat/form/tasks/vat-number-task.tsx:87
msgid "13-digit number, for example *********0123."
msgstr "Numer 13-cyfrowy, na przykład *********0123."

#: client/vat/form/tasks/vat-number-task.tsx:52
msgid "Corporate Number"
msgstr "Numer firmy"

#: client/vat/form/tasks/vat-number-task.tsx:151
msgid "The information you provide here will be used for all of your account's tax documents."
msgstr "Podane tutaj informacje będą używane w dokumentach podatkowych na Twoim koncie."

#: client/vat/form/tasks/vat-number-task.tsx:148
msgid "Set your %1$s"
msgstr "Ustaw: %1$s"

#: includes/woopay/class-woopay-utilities.php:279
msgid "WooPay blog_token is currently misconfigured."
msgstr "WooPay blog_token jest obecnie nieprawidłowo skonfigurowany."

#: includes/admin/class-wc-rest-payments-settings-controller.php:252
msgid "1-click checkout button border radius."
msgstr "Promień krawędzi przycisku płatności 1 kliknięciem."

#: includes/wc-payment-api/class-wc-payments-api-client.php:243
#: includes/wc-payment-api/class-wc-payments-api-client.php:564
#: includes/wc-payment-api/class-wc-payments-api-client.php:643
#: includes/wc-payment-api/class-wc-payments-api-client.php:673
#: includes/wc-payment-api/class-wc-payments-api-client.php:709
#: includes/wc-payment-api/class-wc-payments-api-client.php:836
#: includes/wc-payment-api/class-wc-payments-api-client.php:861
#: includes/wc-payment-api/class-wc-payments-api-client.php:894
#: includes/wc-payment-api/class-wc-payments-api-client.php:1286
#: includes/wc-payment-api/class-wc-payments-api-client.php:1354
#: includes/wc-payment-api/class-wc-payments-api-client.php:1386
#: includes/wc-payment-api/class-wc-payments-api-client.php:1411
#: includes/wc-payment-api/class-wc-payments-api-client.php:1438
#: includes/wc-payment-api/class-wc-payments-api-client.php:1463
#: includes/wc-payment-api/class-wc-payments-api-client.php:1488
#: includes/wc-payment-api/class-wc-payments-api-client.php:1512
#: includes/wc-payment-api/class-wc-payments-api-client.php:1537
#: includes/wc-payment-api/class-wc-payments-api-client.php:1562
#: includes/wc-payment-api/class-wc-payments-api-client.php:1605
#: includes/wc-payment-api/class-wc-payments-api-client.php:1630
#: includes/wc-payment-api/class-wc-payments-api-client.php:1656
#: includes/wc-payment-api/class-wc-payments-api-client.php:1681
#: includes/wc-payment-api/class-wc-payments-api-client.php:1707
#: includes/wc-payment-api/class-wc-payments-api-client.php:1734
#: includes/wc-payment-api/class-wc-payments-api-client.php:1763
#: includes/wc-payment-api/class-wc-payments-api-client.php:1942
#: includes/wc-payment-api/class-wc-payments-api-client.php:1974
#: includes/wc-payment-api/class-wc-payments-api-client.php:2018
#: includes/wc-payment-api/class-wc-payments-api-client.php:2104
#: includes/wc-payment-api/class-wc-payments-api-client.php:2389
#: includes/wc-payment-api/class-wc-payments-api-client.php:2423
msgid "Route param validation failed."
msgstr "Weryfikacja parametrów trasy nie powiodła się."

#: client/components/sandbox-mode-switch-to-live-notice/index.tsx:41
msgid "A test account gives you access to all %1$s features while checkout transactions are simulated. {{learnMoreLink}}Learn more{{/learnMoreLink}}"
msgstr "Konto testowe zapewnia dostęp do wszystkich funkcji %1$s, ale transakcje finalizacji płatności są symulowane. {{learnMoreLink}}Dowiedz się więcej{{/learnMoreLink}}"

#: client/settings/fraud-protection/advanced-settings/index.tsx:99
msgid "At least one risk filter needs to be enabled for advanced protection."
msgstr "Co najmniej jeden filtr ryzyka musi być włączony na potrzeby ochrony zaawansowanej."

#: client/settings/phone-input/index.tsx:139
msgid "Mobile number"
msgstr "Numer telefonu komórkowego"

#. Translators: %1$s and %2$s are both currency codes, e.g. `USD` or `EUR`.
#: includes/subscriptions/class-wc-payments-subscription-service.php:440
msgid "The subscription couldn't be created because it uses a different currency (%1$s) from your existing subscriptions (%2$s). Please ensure all subscriptions use the same currency."
msgstr "Nie można utworzyć subskrypcji, ponieważ używa innej waluty (%1$s) niż Twoje obecne subskrypcje (%2$s). Upewnij się, że wszystkie subskrypcje używają tej samej waluty."

#: includes/class-wc-payments-utils.php:287
msgid "Puerto Rico"
msgstr "Portoryko"

#. translators: %s: WooPay,
#: includes/class-wc-payment-gateway-wcpay.php:4519
msgid "Payments made simple — including %s, a new express checkout feature."
msgstr "Proste płatności, w tym %s — nowa funkcja ekspresowej finalizacji płatności."

#. translators: %1$s: WooPayments
#: includes/class-wc-payment-gateway-wcpay.php:4509
msgid "%1$s gives your store flexibility to accept credit cards, debit cards, and Apple Pay. Enable popular local payment methods and other digital wallets like Google Pay to give customers even more choice."
msgstr "Dzięki usłudze %1$s Twój sklep może akceptować płatności kartami kredytowymi i debetowymi oraz za pomocą Apple Pay. Akceptuj popularne lokalne metody płatności oraz inne portfele cyfrowe, np. Google Pay, aby dać klientom jeszcze większy wybór."

#: client/constants/payment-method.ts:50 client/payment-methods-icons.tsx:41
msgid "Cartes Bancaires"
msgstr "Cartes Bancaires"

#: client/components/account-status/account-tools/strings.tsx:13
msgid "You are using a test account. If you are experiencing problems completing account setup, or wish to test with a different email/country associated with your account, you can reset your account and start from the beginning."
msgstr "Korzystasz z konta testowego. Jeśli masz problemy z konfiguracją konta lub chcesz przetestować usługę z innym adresem e-mail lub krajem, możesz zresetować konto i zacząć jeszcze raz."

#: client/components/deposits-overview/deposit-notices.tsx:93
msgid "Payouts are currently paused because a recent payout failed. Please {{updateLink}}update your bank account details{{/updateLink}}."
msgstr "Wypłaty są obecnie wstrzymane z powodu niepowodzenia ostatniej wypłaty. {{updateLink}}Zaktualizuj dane konta bankowego{{/updateLink}}."

#: includes/class-wc-payments-webhook-processing-service.php:626
msgid "Payment dispute and fees have been deducted from your next payout"
msgstr "Sporne kwoty płatności i opłaty zostały potrącone z Twojej następnej wypłaty"

#. translators: %1: charge ID
#: includes/class-wc-payments-webhook-processing-service.php:880
msgid "The refund amount is not valid for charge ID: %1$s"
msgstr "Kwota zwrotu jest nieprawidłowa w przypadku obciążenia o identyfikatorze %1$s"

#. translators: %1$: order id.
#: includes/class-wc-payment-gateway-wcpay.php:2371
msgid "A refund cannot be found for order: %1$s"
msgstr "Nie można znaleźć zwrotu dotyczącego zamówienia: %1$s"

#: client/settings/transactions/manual-capture-control.tsx:59
msgid "Do you want to continue?"
msgstr "Czy chcesz kontynuować?"

#: client/settings/transactions/manual-capture-control.tsx:58
msgid "Additionally, only card payments support manual capture. Non-card payments will be hidden from checkout."
msgstr "Ponadto ręczna rejestracja jest obsługiwana tylko w przypadku płatności kartą. Metody płatności inne niż karta będą ukryte na stronie realizacji transakcji."

#: client/settings/transactions/manual-capture-control.tsx:57
msgid "Payments must be captured within 7 days or the authorization will expire and money will be returned to the shopper."
msgstr "Płatności trzeba zarejestrować w ciągu 7 dni. W przeciwnym wypadku autoryzacja wygaśnie i pieniądze zostaną zwrócone kupującemu."

#: client/settings/fraud-protection/protection-levels/index.tsx:102
msgid "Basic level help icon"
msgstr "Ikona pomocy na poziomie podstawowym"

#: client/payment-details/payment-method/base-payment-method-details/index.tsx:33
msgid "address placeholder"
msgstr "symbol zastępczy adresu"

#: client/payment-details/payment-method/base-payment-method-details/index.tsx:32
msgid "email placeholder"
msgstr "symbol zastępczy adresu e-mail"

#: client/payment-details/payment-method/base-payment-method-details/index.tsx:31
msgid "name placeholder"
msgstr "symbol zastępczy nazwy"

#: client/payment-details/payment-method/base-payment-method-details/index.tsx:30
msgid "id placeholder"
msgstr "symbol zastępczy identyfikatora"

#. translators: %1$s and %2$s are both numbers
#: includes/core/server/class-request.php:694
msgid "Invalid number passed. Number %1$s needs to be larger than %2$s"
msgstr "Podano nieprawidłową liczbę. Liczba %1$s musi być większa niż %2$s"

#: client/order/order-status-change-strategies/index.tsx:16
msgid "cancelled"
msgstr "Anulowano"

#: includes/class-wc-payments-order-service.php:1424
msgid "Capture authorization <strong>failed</strong> to complete."
msgstr "Autoryzacja rejestracji zakończyła się <strong>niepowodzeniem</strong>."

#: client/components/deposits-overview/deposit-notices.tsx:61
msgid "Payouts are paused while your available funds balance remains below %s. {{learnMoreLink}}Learn more{{/learnMoreLink}}"
msgstr "Wypłaty są wstrzymane, gdy saldo dostępnych środków jest niższe niż %s. {{learnMoreLink}}Dowiedz się więcej{{/learnMoreLink}}"

#: includes/woopay/class-woopay-session.php:676
#: includes/woopay/class-woopay-session.php:745
msgid "Could not determine the blog ID."
msgstr "Nie udało się określić identyfikatora bloga."

#: includes/payment-methods/class-afterpay-payment-method.php:50
msgid "Clearpay"
msgstr "Clearpay"

#. translators: %s: metadata. We do not need to translate WooPayMeta
#: includes/class-wc-payment-gateway-wcpay.php:1522
msgid "We're not able to process this payment. Please try again later. WooPayMeta: intent_meta_order_id: %1$s, order_id: %2$s"
msgstr "Nie możemy przetworzyć tej płatności. Spróbuj ponownie później. WooPayMeta: intent_meta_order_id: %1$s, order_id: %2$s"

#: client/settings/general-settings/test-mode-confirm-modal.tsx:17
msgid "Test mode lets you try out payments, refunds, disputes and other such processes as you're working on your store without handling live payment information. All incoming orders will be simulated, and test mode will have to be disabled before you can accept real orders."
msgstr "Tryb testowy umożliwia wypróbowanie płatności, zwrotów, sporów i innych podobnych procesów podczas pracy nad sklepem bez konieczności używania informacji o bieżących płatnościach. Wszystkie zamówienia przychodzące są symulowane, a tryb testowy należy wyłączyć przed rozpoczęciem przyjmowania rzeczywistych zamówień."

#: client/settings/general-settings/test-mode-confirm-modal.tsx:16
msgid "Are you sure you want to enable test mode?"
msgstr "Czy na pewno chcesz włączyć tryb testowy?"

#: client/payment-details/summary/missing-order-notice/index.tsx:21
msgid "Investigate this purchase and refund the transaction as needed."
msgstr "Zbadaj ten zakup i w razie potrzeby zwróć środki za transakcję."

#: client/payment-details/summary/missing-order-notice/index.tsx:20
msgid "It has been refunded and is not a subject for disputes."
msgstr "Środki zostały zwrócone i transakcja nie podlega sporom."

#: client/payment-details/summary/missing-order-notice/index.tsx:18
msgid "This transaction is not connected to order. "
msgstr "Ta transakcja nie jest powiązana z zamówieniem. "

#: client/order/test-mode-notice/index.tsx:16
#: client/settings/general-settings/test-mode-confirm-modal.tsx:24
msgid "Learn more about test mode"
msgstr "Dowiedz się więcej o trybie testowym"

#: client/data/payment-intents/actions.ts:47
msgid "There has been an error refunding the payment #%s. Please try again later."
msgstr "Podczas zwracania płatności nr %s wystąpił błąd. Spróbuj ponownie później."

#: client/data/payment-intents/actions.ts:42
msgid "Refunded payment #%s."
msgstr "Zwrócono płatność nr %s."

#: client/components/account-status/account-tools/strings.tsx:14
msgid "Payments and payouts are disabled until account setup is completed. If you are experiencing problems completing account setup, or need to change the email/country associated with your account, you can reset your account and start from the beginning."
msgstr "Płatności i wypłaty są wyłączone do momentu ukończenia konfiguracji konta. Jeśli masz problemy z konfiguracją konta lub musisz zmienić powiązany z nim adres e-mail albo kraj, możesz zresetować konto i zacząć jeszcze raz."

#: client/components/account-status/account-tools/strings.tsx:11
msgid "Account Tools"
msgstr "Narzędzia konta"

#: client/components/account-status/account-tools/strings.tsx:15
#: client/connect-account-page/strings.tsx:19
#: client/overview/modal/reset-account/strings.tsx:13
msgid "Reset account"
msgstr "Zresetuj konto"

#: includes/class-wc-payments-status.php:143
msgid "Enabled APMs"
msgstr "Włączono APM"

#: client/components/deposits-overview/deposit-notices.tsx:74
msgid "You have no funds available. {{whyLink}}Why?{{/whyLink}}"
msgstr "Brak dostępnych środków. {{whyLink}}Dlaczego?{{/whyLink}}"

#: client/settings/fraud-protection/advanced-settings/cards/international-ip-address.tsx:21
msgid "This filter is disabled because you're currently selling to all countries."
msgstr "Ten filtr jest wyłączony, ponieważ obecnie prowadzisz sprzedaż we wszystkich krajach."

#: includes/express-checkout/class-wc-payments-express-checkout-ajax-handler.php:85
msgid "Invalid product id"
msgstr "Nieprawidłowy identyfikator produktu"

#: includes/class-wc-payment-gateway-wcpay.php:538
msgid "Small"
msgstr "Mała"

#: client/settings/payment-methods-list/use-payment-method-availability.tsx:98
msgid "Contact Support"
msgstr "skontaktuj się z pomocą techniczną"

#: includes/class-wc-payment-gateway-wcpay.php:563
msgid "By placing this order, you agree to our [terms] and understand our [privacy_policy]."
msgstr "Składając to zamówienie, wyrażasz zgodę na nasze [terms] i potwierdzasz, że [privacy_policy] jest dla Ciebie zrozumiała."

#. translators: This is an error API response.
#: includes/wc-payment-api/class-wc-payments-api-client.php:2667
msgid "Error: The payment could not be captured because the requested capture amount is greater than the amount you can capture for this charge."
msgstr "Błąd: nie można zarejestrować płatności, ponieważ żądana kwota jest większa niż kwota, którą można zarejestrować w przypadku tego obciążenia."

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:70
msgid "Accepting…"
msgstr "Akceptowanie…"

#: includes/class-wc-payment-gateway-wcpay.php:4598
msgid "A valid shipping address is required for Afterpay payments."
msgstr "W przypadku płatności Afterpay wymagany jest prawidłowy adres dostawy."

#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:181
msgid "The date when the payment intent was created."
msgstr "Data utworzenia zamiaru płatności."

#: includes/class-wc-payment-gateway-wcpay.php:544
msgid "WooPay button locations"
msgstr "Lokalizacje przycisku WooPay"

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:114
msgid "Unable to view order. Order not found."
msgstr "Nie można wyświetlić zamówienia. Nie znaleziono zamówienia."

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:66
msgid "This action is final and cannot be undone."
msgstr "To działanie jest ostateczne i nie można go cofnąć."

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:60
msgid "Accepting the dispute marks it as <em>Lost</em>. The disputed amount and the %s dispute fee will not be returned to you."
msgstr "Zaakceptowanie sporu spowoduje oznaczenie go jako <em>przegranego</em>. Sporna kwota i opłata za spór w wysokości %s nie zostaną Ci zwrócone."

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:47
msgid "View order to issue refund"
msgstr "Wyświetl zamówienie, aby przekazać zwrot"

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:44
msgid "You will be taken to the order, where you must complete the refund process manually."
msgstr "Nastąpi przekierowanie do strony zamówienia, na której należy ręcznie dokończyć proces zwrotu środków."

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:40
msgid "Issuing a refund will close the inquiry, returning the amount in question back to the cardholder. No additional fees apply."
msgstr "Przekazanie zwrotu spowoduje zamknięcie zgłoszenia i zwrócenie spornej kwoty posiadaczowi karty. Nie ma żadnych dodatkowych opłat."

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:36
msgid "Issue a refund?"
msgstr "Przekazać zwrot środków?"

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:34
msgid "Issue refund"
msgstr "Przekaż zwrot środków"

#. Translators: %d is the ID of an order.
#: src/Internal/Service/OrderService.php:270
msgid "The requested order (ID %d) was not found."
msgstr "Nie znaleziono żądanego zamówienia (identyfikator: %d)."

#. Translators: placeholders are opening and closing strong HTML tags. %6$s:
#. WooPayments, %7$s: Woo Subscriptions.
#: includes/subscriptions/templates/html-woo-payments-deactivate-warning.php:24
msgid "Your store has subscriptions using %6$s Stripe Billing functionality for payment processing. Due to the %1$soff-site billing engine%3$s these subscriptions use,%4$s they will continue to renew even after you deactivate %6$s%5$s."
msgstr "W Twoim sklepie są aktywne subskrypcje używające funkcji %6$s Stripe Billing do przetwarzania płatności. Z uwagi na %1$szewnętrzny mechanizm fakturowania%3$s, z którego korzystają te subskrypcje, %4$sbędą się one odnawiały nawet po wyłączeniu usługi %6$s%5$s."

#. translators: $1 $2 placeholders are opening and closing HTML link tags,
#. linking to documentation. $3 is WooPayments.
#: includes/subscriptions/templates/html-subscriptions-plugin-notice.php:38
#: includes/subscriptions/templates/html-woo-payments-deactivate-warning.php:38
msgid "If you do not want these subscriptions to continue to be billed, you should %1$scancel these subscriptions%2$s prior to deactivating %3$s."
msgstr "Jeśli nie chcesz, aby te subskrypcje były nadal fakturowane, %1$sanuluj je%2$s przed wyłączeniem usługi %3$s."

#. Translators: %1-%4 placeholders are opening and closing a or strong HTML
#. tags. %5$s: WooPayments, %6$s: Woo Subscriptions.
#: includes/subscriptions/templates/html-subscriptions-plugin-notice.php:24
msgid "Your store has subscriptions using %5$s Stripe Billing functionality for payment processing. Due to the %1$soff-site billing engine%2$s these subscriptions use,%3$s they will continue to renew even after you deactivate %6$s%4$s."
msgstr "W Twoim sklepie są aktywne subskrypcje używające funkcji %5$s Stripe Billing do przetwarzania płatności. Z uwagi na %1$szewnętrzny mechanizm fakturowania%2$s, z którego korzystają te subskrypcje, %3$sbędą się one odnawiały nawet po wyłączeniu usługi %6$s%4$s."

#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:196
msgid "Charge object associated with this payment intention."
msgstr "Obiekt obciążenia powiązany z tym zamiarem płatności."

#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:191
msgid "The status of the payment intent."
msgstr "Status zamiaru płatności."

#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:186
msgid "The customer id of the intent"
msgstr "Identyfikator klienta zamiaru"

#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:166
msgid "ID for the payment intent."
msgstr "Identyfikator zamiaru płatności."

#: includes/admin/class-wc-rest-payments-customer-controller.php:270
msgid "Email associated with the link."
msgstr "Adres e-mail powiązany z linkiem."

#: includes/admin/class-wc-rest-payments-customer-controller.php:265
msgid "Link details for the payment method."
msgstr "Szczegóły linku do metody płatności."

#: includes/admin/class-wc-rest-payments-customer-controller.php:258
msgid "Last 4 digits of the SEPA Debit."
msgstr "Ostatnie 4 cyfry numeru karty SEPA Debit."

#: includes/admin/class-wc-rest-payments-customer-controller.php:253
msgid "SEPA Debit details for the payment method."
msgstr "Szczegóły karty SEPA Debit do metody płatności."

#: includes/admin/class-wc-rest-payments-customer-controller.php:246
msgid "Expiration year of the card."
msgstr "Rok daty ważności karty."

#: includes/admin/class-wc-rest-payments-customer-controller.php:241
msgid "Expiration month of the card."
msgstr "Miesiąc daty ważności karty."

#: includes/admin/class-wc-rest-payments-customer-controller.php:236
msgid "Last 4 digits of the card."
msgstr "Ostatnie 4 cyfry numeru karty."

#: includes/admin/class-wc-rest-payments-customer-controller.php:231
msgid "Brand of the card."
msgstr "Marka karty."

#: includes/admin/class-wc-rest-payments-customer-controller.php:226
msgid "Card details for the payment method."
msgstr "Szczegóły karty do metody płatności."

#: includes/admin/class-wc-rest-payments-customer-controller.php:219
#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:308
msgid "Phone number associated with the billing details."
msgstr "Numer telefonu powiązany z danymi rozliczeniowymi."

#: includes/admin/class-wc-rest-payments-customer-controller.php:214
#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:303
msgid "Name associated with the billing details."
msgstr "Nazwa powiązana z danymi rozliczeniowymi."

#: includes/admin/class-wc-rest-payments-customer-controller.php:208
#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:297
msgid "Email associated with the billing details."
msgstr "Adres e-mail powiązany z danymi rozliczeniowymi."

#: includes/admin/class-wc-rest-payments-customer-controller.php:201
#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:290
msgid "State of the billing address."
msgstr "Stan adresu rozliczeniowego."

#: includes/admin/class-wc-rest-payments-customer-controller.php:196
#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:285
msgid "Postal code of the billing address."
msgstr "Kod pocztowy adresu rozliczeniowego."

#: includes/admin/class-wc-rest-payments-customer-controller.php:191
#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:280
msgid "Line 2 of the billing address."
msgstr "Wiersz 2 adresu rozliczeniowego."

#: includes/admin/class-wc-rest-payments-customer-controller.php:186
#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:275
msgid "Line 1 of the billing address."
msgstr "Wiersz 1 adresu rozliczeniowego."

#: includes/admin/class-wc-rest-payments-customer-controller.php:181
#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:270
msgid "Country of the billing address."
msgstr "Kraj adresu rozliczeniowego."

#: includes/admin/class-wc-rest-payments-customer-controller.php:176
#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:265
msgid "City of the billing address."
msgstr "Miejscowość adresu rozliczeniowego."

#: includes/admin/class-wc-rest-payments-customer-controller.php:171
#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:260
msgid "Address associated with the billing details."
msgstr "Adres powiązany z danymi rozliczeniowymi."

#: includes/admin/class-wc-rest-payments-customer-controller.php:166
#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:255
msgid "Billing details for the payment method."
msgstr "Szczegóły rozliczenia metody płatności."

#: includes/admin/class-wc-rest-payments-customer-controller.php:160
msgid "Type of the payment method."
msgstr "Rodzaj metody płatności."

#: includes/admin/class-wc-rest-payments-customer-controller.php:155
msgid "ID for the payment method."
msgstr "Identyfikator metody płatności."

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:176
msgid "This inquiry was closed on %s. <a>Learn more about preventing disputes</a>."
msgstr "To zgłoszenie zostało zamknięte %s. <a>Dowiedz się więcej o zapobieganiu sporom</a>."

#: client/payment-details/dispute-details/dispute-due-by-date.tsx:26
msgid " (Past due)"
msgstr "(po terminie)"

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:54
msgid "Accept the dispute?"
msgstr "Zaakceptować spór?"

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:95
msgid "This dispute was lost on %1$s."
msgstr "Ten spór zakończył się przegraną dnia %1$s."

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:86
msgid "This dispute was lost on %1$s due to non-response."
msgstr "Ten spór zakończył się przegraną dnia %1$s ze względu na brak odpowiedzi."

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:148
msgid "You submitted evidence for this inquiry on %s. The <strong>cardholder’s bank</strong> is reviewing the case, which can take 120 days or more. You will be alerted when they make their final decision. <a>Learn more</a>."
msgstr "Dowody dotyczące tego zapytania zostały przesłane przez Ciebie w dniu %s. <strong>Bank posiadacza karty</strong> sprawdza zgłoszenie, co może potrwać 120 dni lub dłużej. Po podjęciu ostatecznej decyzji otrzymasz powiadomienie. <a>Dowiedz się więcej</a>."

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:134
msgid "View dispute details"
msgstr "Wyświetl szczegóły sporu"

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:118
msgid "The %1$s fee has been deducted from your account, and the disputed amount has been returned to your customer. <a>Learn more about preventing disputes</a>."
msgstr "Opłata w kwocie %1$s została pobrana z Twojego konta, a sporna kwota została zwrócona klientowi. <a>Dowiedz się więcej o zapobieganiu sporom</a>."

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:176
msgid "Before proceeding, please take note of the following:"
msgstr "Zanim przejdziesz dalej, weź pod uwagę poniższe kwestie:"

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:165
msgid "Continue with challenge"
msgstr "Kontynuuj kwestionowanie sporu"

#: includes/class-wc-payments-status.php:262
msgid "Whether debug logging is enabled and working or not."
msgstr "Czy rejestrowanie na poziomie debugowania jest włączone i działa?"

#: includes/class-wc-payments-status.php:261
msgid "Logging"
msgstr "Rejestrowanie"

#: includes/class-wc-payments-status.php:255
msgid "Whether the tax documents section is enabled or not."
msgstr "Czy sekcja dokumentów podatkowych jest włączona?"

#: includes/class-wc-payments-status.php:245
msgid "Whether the store has the Auth & Capture feature enabled or not."
msgstr "Czy w sklepie włączono funkcję autoryzacji i rejestracji?"

#: includes/class-wc-payments-status.php:244
msgid "Auth and Capture"
msgstr "Autoryzacja i rejestracja"

#: includes/class-wc-payments-status.php:240
msgid "Whether the store has the Multi-currency feature enabled or not."
msgstr "Czy w sklepie włączono funkcję obsługi wielu walut?"

#: includes/class-wc-payments-status.php:198
msgid "The advanced fraud protection filters currently enabled."
msgstr "Aktualnie włączone filtry zaawansowanej ochrony przed oszustwami."

#: includes/class-wc-payments-status.php:197
msgid "Enabled Fraud Filters"
msgstr "Włączone filtry oszustw"

#: includes/class-wc-payments-status.php:192
msgid "The current fraud protection level the payment gateway is using."
msgstr "Aktualny poziom ochrony przed oszustwami używanej przez bramkę płatności."

#: includes/class-wc-payments-status.php:191
msgid "Fraud Protection Level"
msgstr "Poziom ochrony przed oszustwami"

#: includes/class-wc-payments-status.php:180
msgid "Whether the store has Payment Request enabled or not."
msgstr "Czy w sklepie włączono przyciski żądania zapłaty?"

#: includes/class-wc-payments-status.php:179
msgid "Apple Pay / Google Pay Express Checkout"
msgstr "Apple Pay / Google Pay Express Checkout"

#: includes/class-wc-payments-status.php:173
msgid "Whether there are extensions active that are have known incompatibilities with the functioning of the new WooPay Express Checkout."
msgstr "Czy aktywne są rozszerzenia, które są niezgodne z działaniem nowej funkcji WooPay Express Checkout?"

#: includes/class-wc-payments-status.php:172
msgid "WooPay Incompatible Extensions"
msgstr "Rozszerzenia niezgodne z WooPay"

#: includes/class-wc-payments-status.php:162
msgid "Whether the new WooPay Express Checkout is enabled or not."
msgstr "Czy nowa funkcja WooPay Express Checkout jest włączona?"

#: includes/class-wc-payments-status.php:157
msgid "Not active"
msgstr "Nieaktywny"

#: includes/class-wc-payments-status.php:157
msgid "Not eligible"
msgstr "Nie spełnia kryteriów"

#. translators: %s: WooPayments
#: includes/class-wc-payments-status.php:154
msgid "WooPay is not available, as a %s feature, or the store is not yet eligible."
msgstr "Usługa WooPay jest niedostępna jako funkcja %s lub sklep nie spełnia jeszcze kryteriów."

#: includes/class-wc-payments-status.php:150
#: includes/class-wc-payments-status.php:161
msgid "WooPay Express Checkout"
msgstr "WooPay Express Checkout"

#: includes/class-wc-payments-status.php:144
msgid "What payment methods are enabled for the store."
msgstr "Metody płatności włączone w sklepie."

#: includes/class-wc-payments-status.php:139
msgid "Whether the payment gateway has test payments enabled or not."
msgstr "Czy bramka płatności obsługuje płatności testowe?"

#: includes/class-wc-payments-status.php:135
msgid "Needs setup"
msgstr "Wymaga konfiguracji"

#: includes/class-wc-payments-status.php:134
msgid "Is the payment gateway ready and enabled for use on your store?"
msgstr "Czy bramka płatności jest włączona i gotowa do użycia w sklepie?"

#: includes/class-wc-payments-status.php:133
msgid "Payment Gateway"
msgstr "Bramka płatności"

#: includes/class-wc-payments-status.php:125
msgid "The merchant account ID you are currently using to process payments with."
msgstr "Identyfikator konta sprzedawcy, którego obecnie używasz do przetwarzania płatności."

#: includes/class-wc-payments-status.php:120
msgid "The corresponding wordpress.com blog ID for this store."
msgstr "Odpowiednie identyfikator bloga wordpress.com dla tego sklepu."

#: includes/class-wc-payments-status.php:119
msgid "WPCOM Blog ID"
msgstr "Identyfikator bloga WPCOM"

#. translators: %s: WooPayments
#: includes/class-wc-payments-status.php:112
msgid "Can your store connect securely to wordpress.com? Without a proper WPCOM connection %s can't function!"
msgstr "Czy Twój sklep może bezpiecznie łączyć się z wordpress.com? Usługa %s nie może działać bez odpowiedniego połączenia z WPCOM."

#. translators: %s: WooPayments
#: includes/class-wc-payments-status.php:102
msgid "The current version of the %s extension."
msgstr "Używana obecnie wersja rozszerzenia %s."

#: includes/admin/class-wc-rest-payments-orders-controller.php:170
msgid "The payment cannot be captured for completed or processed orders."
msgstr "Płatności nie można rejestrować w przypadku zrealizowanych lub przetworzonych zamówień."

#: client/data/files/resolvers.ts:26
msgid "Error retrieving file."
msgstr "Błąd podczas pobierania pliku."

#: includes/wc-payment-api/class-wc-payments-api-client.php:1190
msgid "Onboarding field data could not be retrieved"
msgstr "Nie można pobrać danych pola wdrażania"

#: client/settings/advanced-settings/stripe-billing-toggle.tsx:26
msgid "By enabling this setting, future %s subscription purchases will utilize Stripe Billing for payment processing. Note: This feature supports card payments only and may lack support for key subscription features. {{learnMoreLink}}Learn more{{/learnMoreLink}}"
msgstr "Po włączeniu tego ustawienia zakupione w przyszłości subskrypcje %s będą używać usługi Stripe Billing do przetwarzania płatności. Uwaga: ta funkcja obsługuje tylko płatności kartami i może nie obsługiwać kluczowych funkcji subskrypcji. {{learnMoreLink}}Dowiedz się więcej{{/learnMoreLink}}"

#: client/settings/advanced-settings/stripe-billing-toggle.tsx:22
msgid "Alternatively, you can enable this setting and future %s subscription purchases will also utilize Stripe Billing for payment processing. Note: This feature supports card payments only and may lack support for key subscription features. {{learnMoreLink}}Learn more{{/learnMoreLink}}"
msgstr "Możesz też włączyć to ustawienie, aby zakupione w przyszłości subskrypcje %s używały usługi Stripe Billing do przetwarzania płatności. Uwaga: ta funkcja obsługuje tylko płatności kartami i może nie obsługiwać kluczowych funkcji subskrypcji. {{learnMoreLink}}Dowiedz się więcej{{/learnMoreLink}}"

#: client/settings/advanced-settings/stripe-billing-toggle.tsx:19
msgid "Enable Stripe Billing for future subscriptions"
msgstr "Włącz usługę Stripe Billing dla przyszłych subskrypcji"

#: client/settings/advanced-settings/stripe-billing-section.tsx:79
msgid "Subscriptions"
msgstr "Subskrypcje"

#: client/settings/advanced-settings/stripe-billing-notices/migration-progress-notice.tsx:52
msgid "%d customer subscription is being migrated from Stripe off-site billing to billing powered by %s and %s."
msgid_plural "%d customer subscriptions are being migrated from Stripe off-site billing to billing powered by %s and %s."
msgstr[0] "Trwa przenoszenie %d subskrypcji klienta z zewnętrznego systemu fakturowania Stripe do systemu obsługiwanego przez %s i %s."
msgstr[1] "Trwa przenoszenie %d subskrypcji klientów z zewnętrznego systemu fakturowania Stripe do systemu obsługiwanego przez %s i %s."
msgstr[2] "Trwa przenoszenie %d subskrypcji klientów z zewnętrznego systemu fakturowania Stripe do systemu obsługiwanego przez %s i %s."

#: client/settings/advanced-settings/stripe-billing-notices/migrate-option-notice.tsx:69
msgid "There is %d customer subscription using Stripe Billing for subscription renewals. We suggest migrating it to on-site billing powered by the %s plugin. {{learnMoreLink}}Learn more{{/learnMoreLink}}"
msgid_plural "There are %d customer subscriptions using Stripe Billing for payment processing. We suggest migrating them to on-site billing powered by the %s plugin. {{learnMoreLink}}Learn more{{/learnMoreLink}}"
msgstr[0] "%d subskrypcja klienta używa systemu Stripe Billing do odnawiania subskrypcji. Zalecamy przeniesienie subskrypcji do wewnętrznego systemu obliczeń obsługiwanego przez wtyczkę %s. {{learnMoreLink}}Dowiedz się więcej{{/learnMoreLink}}"
msgstr[1] "Istnieją aktywne subskrypcje klientów (%d) używające systemu fakturowania Stripe do przetwarzania płatności. Zalecamy przeniesienie subskrypcji do wewnętrznego systemu obliczeń obsługiwanego przez wtyczkę %s. {{learnMoreLink}}Dowiedz się więcej{{/learnMoreLink}}"
msgstr[2] "Istnieją aktywne subskrypcje klientów (%d) używające systemu fakturowania Stripe do przetwarzania płatności. Zalecamy przeniesienie subskrypcji do wewnętrznego systemu obliczeń obsługiwanego przez wtyczkę %s. {{learnMoreLink}}Dowiedz się więcej{{/learnMoreLink}}"

#: client/settings/advanced-settings/stripe-billing-notices/migrate-option-notice.tsx:65
msgid "Begin migration"
msgstr "Rozpocznij migrację"

#: client/settings/advanced-settings/stripe-billing-notices/migrate-completed-notice.tsx:24
msgid "%d customer subscription was successfully migrated from Stripe off-site billing to on-site billing powered by %s and %s."
msgid_plural "%d customer subscriptions were successfully migrated from Stripe off-site billing to on-site billing powered by %s and %s."
msgstr[0] "%d subskrypcja klienta została przeniesiona z zewnętrznego systemu fakturowania Stripe do systemu obsługiwanego przez %s i %s."
msgstr[1] "%d subskrypcje klientów zostały przeniesione z zewnętrznego systemu fakturowania Stripe do systemu obsługiwanego przez %s i %s."
msgstr[2] "%d subskrypcji klientów zostało przeniesionych z zewnętrznego systemu fakturowania Stripe do systemu obsługiwanego przez %s i %s."

#: client/settings/advanced-settings/stripe-billing-notices/migrate-automatically-notice.tsx:43
msgid "There is currently %d customer subscription using Stripe Billing for payment processing. This subscription will be automatically migrated to use the on-site billing engine built into %s once Stripe Billing is disabled. {{learnMoreLink}}Learn more{{/learnMoreLink}}"
msgid_plural "There are currently %d customer subscriptions using Stripe Billing for payment processing. These subscriptions will be automatically migrated to use the on-site billing engine built into %s once Stripe Billing is disabled. {{learnMoreLink}}Learn more{{/learnMoreLink}}"
msgstr[0] "Obecnie istnieje %d subskrypcja klienta używająca funkcji Stripe Billing do przetwarzania płatności. Po wyłączeniu funkcji Stripe Billing ta subskrypcja zostanie automatycznie przeniesiona i będzie używała wewnętrznego mechanizmu fakturowania wbudowanego w %s. {{learnMoreLink}}Dowiedz się więcej{{/learnMoreLink}}"
msgstr[1] "Obecnie istnieją %d subskrypcje klientów używające funkcji Stripe Billing do przetwarzania płatności. Po wyłączeniu funkcji Stripe Billing te subskrypcje zostaną automatycznie przeniesione i będą używały wewnętrznego mechanizmu fakturowania wbudowanego w %s. {{learnMoreLink}}Dowiedz się więcej{{/learnMoreLink}}"
msgstr[2] "Obecnie istnieje %d subskrypcji klientów używających funkcji Stripe Billing do przetwarzania płatności. Po wyłączeniu funkcji Stripe Billing te subskrypcje zostaną automatycznie przeniesione i będą używały wewnętrznego mechanizmu fakturowania wbudowanego w %s. {{learnMoreLink}}Dowiedz się więcej{{/learnMoreLink}}"

#: client/payment-details/dispute-details/dispute-due-by-date.tsx:22
msgid " (%d day left to respond)"
msgid_plural " (%d days left to respond)"
msgstr[0] "(pozostał %d dzień na odpowiedź)"
msgstr[1] "(pozostały %d dni na odpowiedź)"
msgstr[2] "(pozostało %d dni na odpowiedź)"

#: client/payment-details/dispute-details/dispute-summary-row.tsx:45
msgid "Respond By"
msgstr "Odpowiedz do"

#: client/payment-details/dispute-details/dispute-summary-row.tsx:26
msgid "Disputed On"
msgstr "Spór rozpoczęty"

#: client/payment-details/dispute-details/dispute-summary-row.tsx:22
msgid "Dispute Amount"
msgstr "Sporna kwota"

#: includes/admin/class-wc-rest-payments-settings-controller.php:297
msgid "The number of subscriptions migrated from Stripe Billing to on-site billing."
msgstr "Liczba subskrypcji przeniesionych z systemu Stripe Billing do wewnętrznego systemu fakturowania."

#: includes/admin/class-wc-rest-payments-settings-controller.php:292
msgid "The number of subscriptions using Stripe Billing"
msgstr "Liczba subskrypcji używających systemu Stripe Billing"

#: includes/admin/class-wc-rest-payments-settings-controller.php:287
msgid "Whether there is a Stripe Billing off-site to on-site billing migration in progress."
msgstr "Czy trwa migracja z zewnętrznego systemu Stripe Billing do wewnętrznego systemu fakturowania."

#: includes/admin/class-wc-rest-payments-settings-controller.php:282
msgid "If Stripe Billing is enabled."
msgstr "Czy funkcja Stripe Billing jest włączona."

#: client/overview/task-list/tasks/update-business-details-task.tsx:111
msgid "Finish setting up %s"
msgstr "Dokończ konfigurowanie %s"

#. translators: %1$s is a new line character and %2$d is the number of
#. subscriptions.
#: includes/subscriptions/class-wc-payments-subscriptions-migrator.php:551
msgid "This tool will migrate all Stripe Billing subscriptions to tokenized subscriptions with WooPayments.%1$sNumber of Stripe Billing subscriptions found: %2$d"
msgstr "Narzędzie przeniesie wszystkie subskrypcje z systemu Stripe Billing do subskrypcji z tokenami WooPayments.%1$sLiczba znalezionych subskrypcji używających systemu Stripe Billing: %2$d"

#: includes/subscriptions/class-wc-payments-subscriptions-migrator.php:548
msgid "Migrate Subscriptions"
msgstr "Migracja subskrypcji"

#: includes/subscriptions/class-wc-payments-subscriptions-migrator.php:548
msgid "Migration in progress"
msgstr "Migracja w toku"

#: includes/subscriptions/class-wc-payments-subscriptions-migrator.php:547
msgid "Migrate Stripe Billing subscriptions"
msgstr "Migracja subskrypcji używających systemu Stripe Billing"

#: client/overview/task-list/tasks/update-business-details-task.tsx:48
msgid "Payments and payouts are disabled for this account until setup is completed."
msgstr "Płatności i wypłaty są wyłączone do momentu ukończenia konfiguracji tego konta."

#: includes/wc-payment-api/class-wc-payments-api-client.php:1234
msgid "Link type is required"
msgstr "Wymagany jest typ linku"

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:385
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:378
msgid "The status of the deposit"
msgstr "Status wpłaty"

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:380
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:373
msgid "A unique identifier for the deposit."
msgstr "Unikatowy identyfikator wpłaty."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:375
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:368
msgid "Deposit date of transaction"
msgstr "Data transakcji wpłaty"

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:370
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:363
msgid "Fraud risk level."
msgstr "Poziom ryzyka dotyczącego oszustwa."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:365
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:358
msgid "The identifier of the WooCommerce order associated with this transaction."
msgstr "Identyfikator zamówienia WooCommerce powiązanego z tą transakcją."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:360
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:353
msgid "Net amount."
msgstr "Kwota netto."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:353
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:346
msgid "Customer country."
msgstr "Kraj klienta."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:348
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:341
msgid "Customer email."
msgstr "Adres e-mail klienta."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:343
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:336
msgid "Customer name."
msgstr "imię i nazwisko klienta."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:338
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:331
msgid "Customer details."
msgstr "Dane klienta."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:333
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:326
msgid "Transaction fees."
msgstr "Opłaty za transakcję."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:328
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:321
msgid "The currency of the store."
msgstr "Waluta sklepu."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:323
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:316
msgid "The exchange rate of the transaction."
msgstr "Kurs wymiany podczas transakcji."

#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:171
#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:318
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:311
msgid "The amount of the transaction."
msgstr "Kwota transakcji."

#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:176
#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:313
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:306
msgid "The currency of the transaction."
msgstr "Waluta transakcji."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:308
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:301
msgid "The type of the transaction."
msgstr "Typ transakcji."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:301
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:294
msgid "The payment method ID used to create the transaction type."
msgstr "Identyfikator metody płatności użyty do utworzenia typu transakcji."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:291
#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:296
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:284
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:289
msgid "Specifies whether the payment method used was a card (Visa, Mastercard, etc.) or an Alternative Payment Method (APM) or Local Payment Method (LPM) (iDEAL, Apple Pay, Google Pay, etc.)."
msgstr "Określa, czy użytą metodą płatności była karta (Visa, Mastercard itd.), alternatywna metoda płatności, czy lokalna metoda płatności (iDEAL, Apple Pay, Google Pay itd.)."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:286
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:279
msgid "Indicates whether the transaction was made online or offline."
msgstr "Wskazuje, czy transakcja została zrealizowana w trybie online czy offline."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:281
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:274
msgid "A unique source id for each transaction."
msgstr "Unikalny identyfikator źródłowy każdej transakcji."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:276
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:269
msgid "A unique identifier for each transaction based on its transaction type."
msgstr "Unikalny identyfikator każdej transakcji oparty na jej typie."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:270
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:263
msgid "The date and time when the transaction was created."
msgstr "Data i godzina utworzenia transakcji."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:249
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:242
msgid "Direction on which to sort."
msgstr "Kierunek sortowania."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:243
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:236
msgid "Field on which to sort."
msgstr "Pole do sortowania."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:235
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:228
msgid "Page size."
msgstr "Rozmiar strony."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:228
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:221
msgid "Page number."
msgstr "Numer strony."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:223
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:216
msgid "Include timezone into date filtering."
msgstr "Uwzględnij strefę czasową w filtrze daty."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:218
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:211
msgid "Match filter for the transactions."
msgstr "Filtr dopasowywania transakcji."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:212
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:205
msgid "Filter transactions where type is a specific value."
msgstr "Filtrowanie transakcji, których typ ma określoną wartość."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:206
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:199
msgid "Filter transactions based on the payment method used."
msgstr "Filtrowanie transakcji na podstawie użytej metody płatności."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:200
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:193
msgid "Filter transactions based on the customer email."
msgstr "Filtrowanie transakcji na podstawie adresu e-mail klienta."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:194
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:187
msgid "Filter transactions based on the associated deposit ID."
msgstr "Filtrowanie transakcji na podstawie identyfikatora powiązanej wpłaty."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:187
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:180
msgid "Filter transactions based on the associated order ID."
msgstr "Filtrowanie transakcji na podstawie identyfikatora powiązanego zamówienia."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:183
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:176
msgid "Filter transactions between these dates."
msgstr "Filtrowanie transakcji między podanymi datami."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:177
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:170
msgid "Filter transactions after this date."
msgstr "Filtrowanie transakcji po podanej dacie."

#: includes/reports/class-wc-rest-payments-reports-authorizations-controller.php:171
#: includes/reports/class-wc-rest-payments-reports-transactions-controller.php:164
msgid "Filter transactions before this date."
msgstr "Filtrowanie transakcji przed podaną datą."

#. translators: customer email
#: includes/class-wc-payment-token-wcpay-link.php:53
msgid "Stripe Link email ending in %s"
msgstr "Adres e-mail w usłudze Stripe Link kończący się na %s"

#: client/payment-methods-map.tsx:22
msgid "Let your customers pay with JCB, the only international payment brand based in Japan."
msgstr "Pozwól swoim klientom płacić przy użyciu JCB, jedynej marki płatności międzynarodowych z siedzibą w Japonii."

#: client/settings/payment-methods-list/use-payment-method-availability.tsx:50
msgid "More information needed"
msgstr "Potrzeba więcej informacji"

#: client/settings/payment-methods-list/use-payment-method-availability.tsx:58
msgid "Learn more about enabling payment methods"
msgstr "Dowiedz się więcej o włączaniu metod płatności"

#: client/settings/payment-methods-list/use-payment-method-availability.tsx:53
msgid "We need more information from you to enable this method. {{learnMoreLink}}Learn more{{/learnMoreLink}}"
msgstr "Aby włączyć tę metodę, potrzebujemy więcej informacji. {{learnMoreLink}}Dowiedz się więcej.{{/learnMoreLink}}"

#: includes/class-wc-payments-utils.php:265
msgid "Japan"
msgstr "Japonia"

#: includes/class-wc-payments-utils.php:249
msgid "United Arab Emirates"
msgstr "Zjednoczone Emiraty Arabskie"

#: includes/admin/class-wc-rest-payments-settings-controller.php:393
msgid "Error: Invalid Japanese phone number: "
msgstr "Błąd: nieprawidłowy japoński numer telefonu: "

#: includes/subscriptions/class-wc-payments-subscriptions-migrator.php:140
msgid "This subscription has been successfully migrated to a WooPayments tokenized subscription."
msgstr "Ta subskrypcja została pomyślnie przeniesiona do subskrypcji tokenizowanej WooPayments."

#: includes/class-wc-payment-gateway-wcpay.php:694
msgid "We're not able to process this payment due to the order ID mismatch. Please try again later."
msgstr "Nie możemy przetworzyć tej płatności z powodu niezgodności identyfikatora zamówienia. Spróbuj ponownie później."

#: client/overview/task-list/tasks/update-business-details-task.tsx:114
msgid "Update %s business details"
msgstr "Zaktualizuj dane firmy %s"

#. translators: %1$s: The current site domain name. %2$s: The original site
#. domain name. Please keep hostname tags in your translation so that they can
#. be formatted properly. %3$s: WooPayments.
#: woocommerce-payments.php:366
msgid "Transfer your %3$s connection from <hostname>%2$s</hostname> to this site <hostname>%1$s</hostname>. <hostname>%2$s</hostname> will be disconnected from %3$s."
msgstr "Przenieś połączenie %3$s z <hostname>%2$s</hostname> do tej witryny <hostname>%1$s</hostname>. Nastąpi odłączenie witryny <hostname>%2$s</hostname> od usługi %3$s."

#. translators: %s: WooPayments.
#: woocommerce-payments.php:347
msgid "%s Safe Mode"
msgstr "Tryb bezpieczny usługi %s"

#. translators: %s: WooPayments.
#: woocommerce-payments.php:331
msgid "Safe Mode has been deactivated and %s is fully functional."
msgstr "Wyłączono tryb bezpieczny i przywrócono pełną funkcjonalność usługi %s."

#. translators: %s: WooPayments.
#: woocommerce-payments.php:326
msgid "%s connection successfully transferred"
msgstr "Pomyślnie przeniesiono połączenie usługi %s"

#. translators: %1 WooPayments.
#: woocommerce-payments.php:301
msgid "The version of Jetpack installed is too old to be used with %1$s. %1$s has been disabled. Please deactivate or update Jetpack."
msgstr "Zainstalowana wersja Jetpack jest za stara, aby można jej było używać z usługą %1$s. Usługa %1$s została wyłączona. Wyłącz lub zaktualizuj Jetpack."

#. translators: Placeholder is "Woo Subscriptions"".
#. translators: %s: WooPayments
#. translators: Placeholder is "Woo Subscriptions"".
#: includes/subscriptions/templates/html-subscriptions-plugin-notice.php:62
#: includes/subscriptions/templates/html-wcpay-deactivate-warning.php:61
#: includes/subscriptions/templates/html-woo-payments-deactivate-warning.php:62
msgid "Yes, deactivate %s"
msgstr "Tak, wyłącz usługę %s"

#. translators: Placeholder is "Woo Subscriptions"".
#. translators: %s: WooPayments.
#. translators: Placeholder is "Woo Subscriptions"".
#: includes/subscriptions/templates/html-subscriptions-plugin-notice.php:49
#: includes/subscriptions/templates/html-wcpay-deactivate-warning.php:49
#: includes/subscriptions/templates/html-woo-payments-deactivate-warning.php:49
msgid "Are you sure you want to deactivate %s?"
msgstr "Czy na pewno chcesz wyłączyć usługę %s?"

#. translators: %s: WooPayments
#: includes/subscriptions/class-wc-payments-subscription-service.php:730
msgid "%s Subscription ID"
msgstr "ID subskrypcji usługi %s"

#. translators: %s: WooPayments
#: includes/notes/class-wc-payments-notes-qualitative-feedback.php:70
msgid "Help us make improvements to %s"
msgstr "Pomóż nam ulepszyć usługę %s"

#. translators: %s: WooPayments
#: includes/notes/class-wc-payments-notes-instant-deposits-eligible.php:33
msgid "You’re now eligible to receive Instant Payouts with %s"
msgstr "Możesz teraz otrzymywać natychmiastowe wypłaty przy użyciu usługi %s"

#. translators: %s: WooPayments
#: includes/multi-currency/SettingsOnboardCta.php:74
msgid "To add new currencies to your store, please finish setting up %s."
msgstr "Aby dodać nowe waluty do sklepu, zakończ konfigurację usługi %s."

#. translators: %s: WooPayments
#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:144
msgid "Risk filtering is only available for orders processed using credit cards with %s."
msgstr "Filtrowanie ryzyka jest dostępne tylko w przypadku zamówień przetwarzanych za pomocą kart kredytowych przy użyciu usługi %s."

#. translators: %1$s: WooCommerce, %2$s: WooPayments, a1: documentation URL
#: includes/class-wc-payments.php:1590
msgid "The %1$s version you have installed is not compatible with %2$s for a Norwegian business. Please update %1$s to version 7.5 or above. You can do that via the <a1>the plugins page.</a1>"
msgstr "Zainstalowana wersja usługi %1$s nie jest zgodna z usługą %2$s dla norweskich przedsiębiorstw. Zaktualizuj usługę %1$s do wersji 7.5 lub nowszej. Możesz to zrobić za pośrednictwem <a1>strony wtyczek</a1>."

#. translators: %1$s: the refund amount, %2$s: status (cancelled/unsuccessful),
#. %3$s: WooPayments, %4$s: ID of the refund, %5$s: failure message or period
#: includes/class-wc-payments-order-service.php:1524
msgid "A refund of %1$s was <strong>%2$s</strong> using %3$s (<code>%4$s</code>)%5$s"
msgstr "Zwrot kosztów w kwocie %1$s został <strong>%2$s</strong> przy użyciu %3$s (<code>%4$s</code>)%5$s"

#. translators: %s: WooPayments
#: includes/class-wc-payments-status.php:75
msgid "This tool will clear the account cached values used in %s."
msgstr "To narzędzie umożliwia czyszczenie wartości z pamięci podręcznej konta używanego w usłudze %s."

#. translators: %s: WooPayments
#: includes/class-wc-payments-status.php:69
msgid "Clear %s account cache"
msgstr "Wyczyść pamięć podręczną konta usługi %s"

#. translators: %1: the authorized amount, %2: WooPayments, %3: transaction ID
#. of the payment
#: includes/class-wc-payments-order-service.php:1743
msgid "A capture of %1$s <strong>failed</strong> to complete using %2$s (<a>%3$s</a>)."
msgstr " <strong>Nie udało się</strong> ukończyć przechwycenia %1$s za pomocą %2$s (<a>%3$s</a>)."

#. translators: %1: the successfully charged amount, %2: WooPayments, %3:
#. transaction ID of the payment
#: includes/class-wc-payments-order-service.php:1715
msgid "A payment of %1$s was <strong>successfully captured</strong> using %2$s (<a>%3$s</a>)."
msgstr "Płatność w wysokości %1$s została <strong>przechwycona</strong> za pomocą usługi %2$s (<a>%3$s</a>)."

#. translators: %1: the authorized amount, %2: WooPayments, %3: intent ID of
#. the payment
#: includes/class-wc-payments-order-service.php:1687
msgid "A payment of %1$s was <strong>started</strong> using %2$s (<code>%3$s</code>)."
msgstr "Płatność %1$s została <strong>rozpoczęta</strong> za pomocą usługi %2$s (<code>%3$s</code>)."

#. translators: %1: the authorized amount, %2: WooPayments, %3: transaction ID
#. of the payment
#: includes/class-wc-payments-order-service.php:1661
msgid "A payment of %1$s was <strong>authorized</strong> using %2$s (<a>%3$s</a>)."
msgstr "Płatność %1$s została <strong>autoryzowana</strong> za pomocą usługi %2$s (<a>%3$s</a>)."

#. translators: %1: the authorized amount, %2: WooPayments, %3: transaction ID
#. of the payment
#: includes/class-wc-payments-order-service.php:1595
msgid "A payment of %1$s <strong>failed</strong> using %2$s (<a>%3$s</a>)."
msgstr "Płatność %1$s za pomocą usługi %2$s <strong>nie powiodła się</strong> (<a>%3$s</a>)."

#. translators: %1: the successfully charged amount, %2: WooPayments, %3:
#. transaction ID of the payment
#: includes/class-wc-payments-order-service.php:1568
msgid "A payment of %1$s was <strong>successfully charged</strong> using %2$s (<a>%3$s</a>)."
msgstr "Płatność %1$s za pomocą usługi %2$s <strong>została zrealizowana</strong> (<a>%3$s</a>)."

#. translators: %1: WooPayments, %2: required WP version number, %3: currently
#. installed WP version number
#: includes/class-wc-payments-dependency-service.php:297
msgid "%1$s requires <strong>WordPress %2$s</strong> or greater (you are using %3$s)."
msgstr "Usługa %1$s wymaga zainstalowania <strong>WordPress%2$s </strong> lub nowszej wersji (używasz wersji %3$s)."

#. translators: %1: WooPayments, %2: WooCommerce Admin, %3: required WC-Admin
#. version number, %4: currently installed WC-Admin version number
#: includes/class-wc-payments-dependency-service.php:275
msgid "%1$s requires <strong>%2$s %3$s</strong> or greater to be installed (you are using %4$s)."
msgstr "Usługa %1$s wymaga zainstalowania <strong>%2$s %3$s</strong> lub nowszej wersji (używasz wersji %4$s). "

#. translators: %1$s: WooPayments, %2$s: WooCommerce Admin
#: includes/class-wc-payments-dependency-service.php:263
msgid "%1$s requires %2$s to be enabled. Please remove the <code>woocommerce_admin_disabled</code> filter to use %1$s."
msgstr "Usługa %1$s wymaga włączenia %2$s. Usuń filtr <code>woocommerce_admin_disabled</code>, aby korzystać z usługi %1$s."

#. translators: %1$s: WooCommerce, %2$s: WooPayments, a1: link to the Plugins
#. page, a2: link to the page having all previous versions
#: includes/class-wc-payments-dependency-service.php:245
msgid "<a1>Update %1$s</a1> <strong>(recommended)</strong> or manually re-install <a2>a previous version</a2> of %2$s."
msgstr "<a1>Zaktualizuj %1$s</a1> <strong>(zalecane)</strong> lub ręcznie zainstaluj ponownie <a2>poprzednią wersję</a2> %2$s."

#. translators: %1: WooPayments, %2: current WooCommerce Payment version, %3:
#. WooCommerce, %4: required WC version number, %5: currently installed WC
#. version number
#: includes/class-wc-payments-dependency-service.php:229
msgid "%1$s %2$s requires <strong>%3$s %4$s</strong> or greater to be installed (you are using %5$s). "
msgstr "Usługa %1$s %2$s wymaga zainstalowania <strong>%3$s %4$s</strong> albo nowszej wersji (aktualnie używasz wersji %5$s). "

#. translators: %1$s: WooPayments, %2$s: WooCommerce
#: includes/class-wc-payments-dependency-service.php:204
msgid "%1$s requires <a>%2$s</a> to be installed and active."
msgstr "Usługa %1$s wymaga zainstalowania i aktywacji <a>%2$s</a>."

#. translators: %s: WooPayments
#: includes/class-wc-payment-gateway-wcpay.php:3668
msgid "A %s payment method was not provided"
msgstr "Nie podano usługi %s jako metody płatności"

#: client/overview/task-list/tasks/dispute-task.tsx:113
msgid "Respond to %d active disputes"
msgstr "Odpowiedz na aktywne spory (%d)"

#. translators: %s: WooPayments
#: includes/admin/class-wc-rest-payments-settings-controller.php:208
msgid "If %s express checkouts should be enabled."
msgstr "Jeśli ekspresowa finalizacji płatności w usłudze %s ma być włączona."

#. translators: %s: WooPayments
#: includes/admin/class-wc-rest-payments-settings-controller.php:148
msgid "%s bank account descriptor to be displayed in customers' bank accounts."
msgstr "Deskryptor konta bankowego usługi %s wyświetlany na rachunkach bankowych klientów."

#. translators: %s: WooPayments
#: includes/admin/class-wc-rest-payments-settings-controller.php:139
msgid "%s Subscriptions feature flag setting."
msgstr "Ustawienie flagi funkcji subskrypcji usługi %s."

#. translators: %s: WooPayments
#: includes/admin/class-wc-rest-payments-settings-controller.php:130
msgid "%s Multi-Currency feature flag setting."
msgstr "Ustawienie flagi funkcji wielu walut usługi %s."

#. translators: %s: WooPayments
#: includes/admin/class-wc-rest-payments-settings-controller.php:121
msgid "%s test mode setting."
msgstr "Ustawienie trybu testowego usługi %s."

#. translators: %s: WooPayments
#: includes/admin/class-wc-rest-payments-settings-controller.php:112
msgid "If %s \"Saved cards\" should be enabled."
msgstr "Jeśli funkcja „Zapisane karty” usługi %s powinna być włączona."

#. translators: %s: WooPayments
#: includes/admin/class-wc-rest-payments-settings-controller.php:103
msgid "If %s manual capture of charges should be enabled."
msgstr "Jeśli ręczne przechwytywanie opłat za pomocą usługi %s powinno być włączone."

#. translators: %s: WooPayments
#: includes/admin/class-wc-rest-payments-settings-controller.php:85
msgid "If %s should be enabled."
msgstr "Jeśli usługa %s powinna być włączona."

#. translators: %s: WooPayments
#: includes/admin/class-wc-payments-admin.php:1250
msgid "This represents the fee %s collects for the transaction."
msgstr "Przedstawia opłatę za transakcję pobieraną przez usługę %s."

#. translators: %s: WooPayments
#: includes/admin/class-wc-payments-admin.php:250
msgid "The selected currency is not available for the country set in your %s account."
msgstr "Wybrana waluta nie jest dostępna dla kraju ustawionego na koncie usługi %s."

#: includes/class-wc-payments-order-success-page.php:84
#: includes/class-wc-payments-order-success-page.php:546
#: includes/payment-methods/class-multibanco-payment-method.php:46
msgid "Multibanco"
msgstr "Multibanco"

#: includes/payment-methods/class-klarna-payment-method.php:48
msgid "Klarna"
msgstr "Klarna"

#: includes/payment-methods/Configs/Definitions/AlipayDefinition.php:56
msgid "Alipay"
msgstr "Alipay"

#: client/constants/payment-method.ts:57 client/payment-methods-icons.tsx:49
msgid "Visa"
msgstr "Visa"

#: client/payment-methods-icons.tsx:48
msgid "UnionPay"
msgstr "UnionPay"

#: client/constants/payment-method.ts:54 client/payment-methods-icons.tsx:47
msgid "Mastercard"
msgstr "MasterCard"

#: client/constants/payment-method.ts:53 client/payment-methods-icons.tsx:45
#: client/payment-methods-map.tsx:21
msgid "JCB"
msgstr "JCB"

#: client/constants/payment-method.ts:52 client/payment-methods-icons.tsx:43
msgid "Discover"
msgstr "Odkryj"

#: client/constants/payment-method.ts:51 client/payment-methods-icons.tsx:42
msgid "Diners Club"
msgstr "Diners Club"

#: client/constants/payment-method.ts:47 client/payment-methods-icons.tsx:39
msgid "American Express"
msgstr "American Express"

#: client/transactions/filters/config.ts:205
msgid "<title>Type</title> <rule /> <filter />"
msgstr "<title>Typ</title> <rule /> <filter />"

#: client/disputes/filters/config.ts:88
msgid "Disputes match <select /> filters"
msgstr "Spory pasują do {{select /}} filtrów"

#: client/documents/filters/config.ts:53
#: client/transactions/filters/config.ts:123
msgid "<title>Date</title> <rule /> <filter />"
msgstr "<title>Data</title> <rule /> <filter />"

#. translators: %1$s: WooCommerce , %2$s: WooPayments, %3$s: The current
#. WooCommerce version used by the store
#: includes/class-wc-payments.php:2068
msgid "The %1$s version you have installed is not compatible with %2$s for a Czech Republic business. Please update %1$s to version 7.8 or above (you are using %3$s). You can do that via the <a1>the plugins page.</a1>"
msgstr "Zainstalowana wersja usługi %1$s nie jest zgodna z usługą %2$s dla czeskich przedsiębiorstw. Zaktualizuj usługę %1$s do wersji 7.8 lub nowszej (aktualnie używana wersja: %3$s). Możesz to zrobić za pośrednictwem <a1>strony wtyczek</a1>."

#. translators: %1$s: WooCommerce , %2$s: WooPayments, %3$s: The current
#. WooCommerce version used by the store
#: includes/class-wc-payments.php:2064
msgid "The %1$s version you have installed is not compatible with %2$s for a Swedish business. Please update %1$s to version 7.8 or above (you are using %3$s). You can do that via the <a1>the plugins page.</a1>"
msgstr "Zainstalowana wersja %1$s nie jest zgodna z usługą %2$s dla szwedzkich przedsiębiorstw. Zaktualizuj usługę %1$s do wersji 7.8 lub nowszej (aktualnie używana wersja: %3$s). Możesz to zrobić za pośrednictwem <a1>strony wtyczek</a1>."

#. translators: %1$s: WooCommerce , %2$s: WooPayments, %3$s: The current
#. WooCommerce version used by the store
#: includes/class-wc-payments.php:2060
msgid "The %1$s version you have installed is not compatible with %2$s for a Hungarian business. Please update %1$s to version 7.8 or above (you are using %3$s). You can do that via the <a1>the plugins page.</a1>"
msgstr "Zainstalowana wersja %1$s nie jest zgodna z usługą %2$s dla węgierskich przedsiębiorstw. Zaktualizuj usługę %1$s do wersji 7.8 lub nowszej (aktualnie używana wersja: %3$s). Możesz to zrobić za pośrednictwem <a1>strony wtyczek</a1>."

#: includes/class-wc-payments-utils.php:282
msgid "Sweden"
msgstr "Szwecja"

#: includes/class-wc-payments-utils.php:270
msgid "Hungary"
msgstr "Węgry"

#: includes/class-wc-payments-utils.php:257
msgid "Czech Republic"
msgstr "Czechy"

#. translators: %s is the number of days left, e.g. 1 day.
#. translators: %d: number of days
#: includes/admin/tasks/class-wc-payments-task-disputes.php:196
#: includes/class-wc-payments-order-success-page.php:111
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d dzień"
msgstr[1] "%d dni"
msgstr[2] "%d dni"

#. translators: %1$s is a date, eg: Jan 1, 2021. %2$s is the number of days
#. left, eg: 2 days.
#: includes/admin/tasks/class-wc-payments-task-disputes.php:193
msgid "By %1$s – %2$s left to respond"
msgstr "Do %1$s — liczba pozostałych dni na odpowiedź: %2$s"

#. translators: %d is a number greater than 1. %s is a formatted amount, eg:
#. $10.00
#: includes/admin/tasks/class-wc-payments-task-disputes.php:145
msgid "Respond to %1$d active disputes for a total of %2$s"
msgstr "Odpowiedz na aktywne spory (%1$d) na łączną kwotę %2$s"

#: client/overview/task-list/tasks/dispute-task.tsx:125
msgid "Last week to respond to %d of the disputes"
msgstr "Ostatni tydzień na udzielenie odpowiedzi na %d ze sporów"

#: client/overview/task-list/tasks/dispute-task.tsx:124
msgid "Final day to respond to %d of the disputes"
msgstr "Ostatni dzień na udzielenie odpowiedzi na %d ze sporów"

#: client/overview/task-list/tasks/dispute-task.tsx:93
msgid "Respond today by %s"
msgstr "Odpowiedz dzisiaj do %s"

#: client/overview/task-list/tasks/dispute-task.tsx:90
msgid "Respond to a dispute for %s"
msgstr "Odpowiedź na spór na kwotę %s"

#: client/overview/task-list/tasks/dispute-task.tsx:89
msgid "Respond to a dispute for %s – Last day"
msgstr "Odpowiedź na spór na kwotę %s — ostatni dzień"

#: client/overview/task-list/tasks/dispute-task.tsx:74
msgid "Respond now"
msgstr "Odpowiedz teraz"

#. translators: %1: the disputed amount and currency; %2: the dispute reason;
#. %3 the deadline date for responding to dispute
#: includes/class-wc-payments-order-service.php:1915
msgid "Payment has been disputed for %1$s with reason \"%2$s\". <a>Response due by %3$s</a>."
msgstr "Zakwestionowano płatność na kwotę %1$s, powód: „%2$s”. <a>Termin odpowiedzi: %3$s</a>."

#: includes/payment-methods/class-afterpay-payment-method.php:57
msgid "Afterpay"
msgstr "Afterpay"

#: includes/payment-methods/class-affirm-payment-method.php:49
msgid "Affirm"
msgstr "Affirm"

#: includes/class-wc-payments-status.php:126
msgid "Not connected"
msgstr "Nie połączono"

#. Translators: %1$s: Opening anchor tag. %2$s: Closing anchor tag.
#: includes/admin/class-wc-payments-admin.php:291
msgid "Icelandic Króna does not accept decimals. Please update your currency number of decimals to 0 or select a different currency. %1$sVisit settings%2$s"
msgstr "W przypadku korony islandzkiej nie są akceptowane miejsca dziesiętne. Zaktualizuj liczbę miejsc dziesiętnych w walucie do 0 lub wybierz inną walutę. %1$sOtwórz ustawienia%2$s"

#: client/settings/fraud-protection/advanced-settings/rule-toggle.tsx:56
msgid "Filter action"
msgstr "Działanie filtra"

#: client/settings/fraud-protection/advanced-settings/rule-toggle.tsx:18
msgid "Authorize and hold for review"
msgstr "Autoryzuj i wstrzymaj do weryfikacji"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:168
msgid "The payment for this order was held for review by your risk filtering and manually approved."
msgstr "Płatność za to zamówienie została wstrzymana do weryfikacji przez filtr ryzyka i zatwierdzona ręcznie."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:103
msgid "Approved"
msgstr "Zatwierdzono"

#: includes/class-wc-payments-utils.php:281
msgid "Romania"
msgstr "Rumunia"

#: includes/class-wc-payments-utils.php:264
msgid "Croatia"
msgstr "Chorwacja"

#: includes/class-wc-payments-utils.php:253
msgid "Bulgaria"
msgstr "Bułgaria"

#: client/components/deposits-status/index.tsx:64
#: client/components/payments-status/index.tsx:26
#: client/settings/payment-methods-list/use-payment-method-availability.tsx:79
msgid "Pending verification"
msgstr "Oczekujące na weryfikację"

#: includes/woopay/class-woopay-session.php:97
#: includes/woopay/class-woopay-session.php:98
msgid "WooPay request is not signed correctly."
msgstr "Żądanie WooPay nie jest poprawnie podpisane."

#: client/components/deposits-overview/deposit-notices.tsx:46
msgid "Payouts may be interrupted while your %s balance remains negative. {{whyLink}}Why?{{/whyLink}}"
msgstr "Płatności częściowe mogą zostać przerwane, gdy saldo %s pozostaje ujemne. {{whyLink}}Dlaczego?{{/whyLink}}"

#: client/components/deposits-overview/deposit-notices.tsx:32
msgid "Your first payout is held for 7-14 days. {{whyLink}}Why?{{/whyLink}}"
msgstr "Twoja pierwsza wypłata będzie wstrzymana przez 7–14 dni. {{whyLink}}Dlaczego?{{/whyLink}}"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:194
msgid "The payment for this order was done in person and has bypassed your risk filtering."
msgstr "Płatność za to zamówienie została dokonana osobiście i ominęła filtr ryzyka."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:187
msgid "The payment for this order was held for review by your risk filtering. The authorization for the charge appears to have failed."
msgstr "Płatność za to zamówienie została wstrzymana do weryfikacji przez filtr ryzyka. Wygląda na to, że autoryzacja opłaty nie powiodła się."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:180
msgid "The payment for this order was held for review by your risk filtering. The authorization for the charge appears to have expired."
msgstr "Płatność za to zamówienie została wstrzymana do weryfikacji przez filtr ryzyka. Wygląda na to, że autoryzacja opłaty wygasła."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:156
msgid "The payment for this order has not yet been passed to the fraud and risk filters to determine its outcome status."
msgstr "Płatność za to zamówienie nie została jeszcze przekazana do filtrów zapobiegania oszustwom i filtrów ryzyka w celu określenia statusu wyniku."

#: includes/core/server/request/class-update-account.php:61
msgid "No account settings provided"
msgstr "Nie podano ustawień konta"

#. Translators: %s is a provided username.
#: includes/core/server/class-request.php:817
msgid "%s is not a valid username."
msgstr "%s nie jest prawidłową nazwą użytkownika."

#. Translators: %s is a currency code.
#: includes/core/server/class-request.php:793
msgid "%1$s is not a valid redirect URL. Use a URL in the allowed_redirect_hosts filter."
msgstr "%1$s nie jest prawidłowym adresem URL przekierowania. Użyj adresu URL w filtrze allowed_redirect_hosts."

#: client/components/deposits-overview/deposit-notices.tsx:21
msgid "Your payouts are {{strong}}temporarily suspended{{/strong}}. {{suspendLink}}Learn more{{/suspendLink}}"
msgstr "Twoje płatności częściowe są {{strong}}tymczasowo zawieszone{{/strong}}. {{suspendLink}}Dowiedz się więcej{{/suspendLink}}"

#: client/components/deposits-overview/recent-deposits-list.tsx:37
msgid "Dispatch date"
msgstr "Data wysyłki"

#: includes/class-wc-payment-gateway-wcpay.php:1102
msgid "Invalid phone number."
msgstr "Nieprawidłowy numer telefonu."

#: client/settings/fraud-protection/protection-levels/protection-level-modal-notice/index.tsx:14
msgid "Provides basic anti-fraud protection only."
msgstr "Zapewnia tylko podstawową ochronę przed oszustwami."

#: client/settings/fraud-protection/protection-levels/fp-modal/index.tsx:20
msgid "Basic filter level"
msgstr "Podstawowy poziom filtrowania"

#: client/settings/fraud-protection/advanced-settings/cards/ip-address-mismatch.tsx:24
msgid "Fraudulent transactions often use fake addresses to place orders. If the IP address seems to be in one country, but the billing address is in another, that could signal potential fraud."
msgstr "W oszukańczych transakcjach często wykorzystuje się fałszywe adresy do składania zamówień. Jeśli wygląda na to, że adres IP jest w jednym kraju, a adres rozliczeniowy w innym, może to sygnalizować potencjalne oszustwo."

#: client/settings/fraud-protection/advanced-settings/cards/ip-address-mismatch.tsx:14
msgid "IP Address Mismatch"
msgstr "Niezgodność adresu IP"

#. translators: %s: WooPayments
#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:201
msgid "Risk filtering through %s was not found on this order, it may have been created while filtering was not enabled."
msgstr "W tym zamówieniu nie znaleziono filtrowania ryzyka przy użyciu %s — mogło ono zostać utworzone, gdy filtrowanie nie było włączone."

#. translators: %1: WooPayments, %2: Payment method title
#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:137
msgid "Risk filtering is only available for orders processed using credit cards with %1$s. This order was processed with %2$s."
msgstr "Filtrowanie ryzyka jest dostępne tylko w przypadku zamówień przetwarzanych za pomocą kart kredytowych przy użyciu usługi %1$s. To zamówienie zostało przetworzone przy użyciu %2$s."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:124
msgid "View more details"
msgstr "Wyświetl więcej szczegółów"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:123
msgid "The payment for this order was blocked by your risk filtering. There is no pending authorization, and the order can be cancelled to reduce any held stock."
msgstr "Płatność za to zamówienie została zablokowana przez filtr ryzyka. Nie ma oczekującej autoryzacji, a zamówienie może zostać anulowane w celu zmniejszenia utrzymywanych zapasów."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:173
msgid "This transaction was held for review by your risk filters, and the charge was manually blocked after review."
msgstr "Ta transakcja została wstrzymana do weryfikacji przez filtry ryzyka, a opłata została ręcznie zablokowana po weryfikacji."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:97
msgid "Red shield outline"
msgstr "Czerwony kontur tarczy"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:162
#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:174
#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:181
#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:188
msgid "Review payment"
msgstr "Zweryfikuj płatność"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:161
msgid "The payment for this order was held for review by your risk filtering. You can review the details and determine whether to approve or block the payment."
msgstr "Płatność za to zamówienie została wstrzymana do weryfikacji przez filtr ryzyka. Możesz przejrzeć szczegóły i zdecydować o zatwierdzeniu lub zablokowaniu płatności."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:104
msgid "Held for review"
msgstr "Wstrzymano do weryfikacji"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:93
msgid "Orange shield outline"
msgstr "Pomarańczowy kontur tarczy"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:118
msgid "The payment for this order passed your risk filtering."
msgstr "Płatność za to zamówienie została zaakceptowana przez filtr ryzyka."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:105
msgid "No action taken"
msgstr "Nie podjęto żadnych działań"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:89
msgid "Green check mark"
msgstr "Zielony znacznik zatwierdzenia"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:57
msgid "Fraud &amp; Risk"
msgstr "Oszustwo i ryzyko"

#: includes/class-wc-payments-order-service.php:2263
msgid "The requested order was not found."
msgstr "Nie znaleziono żądanego zamówienia."

#. translators: %1: the blocked amount, %2: transaction ID of the payment
#: includes/class-wc-payments-order-service.php:1865
msgid "&#x1F6AB; A payment of %1$s was <strong>blocked</strong> by one or more risk filters.<br><br><a>View more details</a>."
msgstr "&#x1F6AB; Płatność %1$s została <strong>zablokowana</strong> przez co najmniej jeden filtr ryzyka.<br><br><a>Wyświetl więcej szczegółów</a>."

#. translators: %1: the authorized amount, %2: transaction ID of the payment
#: includes/class-wc-payments-order-service.php:1831
msgid "&#x26D4; A payment of %1$s was <strong>held for review</strong> by one or more risk filters.<br><br><a>View more details</a>."
msgstr "⛔ Płatność %1$s została <strong>wstrzymana do weryfikacji</strong> przez co najmniej jeden filtr ryzyka.<br><br><a>Wyświetl więcej szczegółów</a>."

#: includes/admin/class-wc-payments-admin.php:1255
msgid "Transaction Fee:"
msgstr "Opłata za transakcję: "

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:102
msgid "Blocked"
msgstr "Zablokowane"

#: client/settings/fraud-protection/protection-levels/index.tsx:101
msgid "Basic"
msgstr "Podstawowe"

#: client/settings/fraud-protection/protection-levels/fp-help-text/index.tsx:16
msgid "Provides the base level of platform protection."
msgstr "Zapewnia podstawowy poziom ochrony platformy."

#: client/settings/fraud-protection/advanced-settings/index.tsx:194
#: client/settings/fraud-protection/protection-levels/index.tsx:94
msgid "There was an error retrieving your fraud protection settings. Please refresh the page to try again."
msgstr "Podczas pobierania ustawień ochrony przed oszustwami wystąpił błąd. Odśwież stronę, aby spróbować ponownie."

#: client/settings/fraud-protection/advanced-settings/cards/cvc-verification.tsx:20
msgid "For security, this filter is enabled and cannot be modified. Payments failing CVC verification will be blocked. {{learnMoreLink}}Learn more{{/learnMoreLink}}"
msgstr "Ze względów bezpieczeństwa ten filtr jest włączony i nie można go modyfikować. Płatności, które nie przeszły weryfikacji kodu CVC, będą blokowane. {{learnMoreLink}}Dowiedz się więcej{{/learnMoreLink}}"

#: client/settings/fraud-protection/advanced-settings/cards/cvc-verification.tsx:32
msgid "This filter is disabled, and cannot be modified."
msgstr "Ten filtr jest wyłączony i nie można go modyfikować."

#: client/settings/fraud-protection/advanced-settings/allow-countries-notice.tsx:22
msgid "Orders from outside of the following countries will be screened by the filter: "
msgstr "Filtr będzie monitorować zamówienia spoza następujących krajów: "

#: client/settings/fraud-protection/advanced-settings/allow-countries-notice.tsx:21
msgid "Orders from outside of the following countries will be blocked by the filter: "
msgstr "Filtr będzie blokować zamówienia spoza następujących krajów: "

#: client/settings/fraud-protection/advanced-settings/allow-countries-notice.tsx:17
msgid "Orders from the following countries will be screened by the filter: "
msgstr "Filtr będzie monitorować zamówienia z następujących krajów: "

#: client/settings/fraud-protection/advanced-settings/allow-countries-notice.tsx:16
msgid "Orders from the following countries will be blocked by the filter: "
msgstr "Filtr będzie blokować zamówienia z następujących krajów: "

#: client/transactions/list/index.tsx:76
msgid "Tap to Pay on Android"
msgstr "Tap to Pay na Androida"

#: client/transactions/list/index.tsx:73
msgid "Tap to Pay on iPhone"
msgstr "Tap to Pay on iPhone"

#: client/data/authorizations/actions.ts:148
msgid "Payment for order #%s canceled successfully."
msgstr "Płatność za zamówienie nr %s została anulowana."

#: client/components/transaction-status-pill/mappings.ts:13
msgid "Needs review"
msgstr "Wymagana weryfikacja"

#. translators: %s: the error message.
#: includes/admin/class-wc-rest-payments-orders-controller.php:555
msgid "Payment cancel failed to complete with the following message: %s"
msgstr "Anulowanie płatności zakończone niepowodzeniem z następującym komunikatem: %s"

#: includes/admin/class-wc-rest-payments-orders-controller.php:540
#: includes/admin/class-wc-rest-payments-orders-controller.php:543
msgid "The payment cannot be canceled"
msgstr "Nie można anulować płatności"

#: includes/admin/class-wc-rest-payments-orders-controller.php:525
msgid "Payment cannot be canceled for partially or fully refunded orders."
msgstr "Nie można anulować płatności za zamówienia częściowo lub w całości zwrócone."

#: client/settings/fraud-protection/protection-levels/index.tsx:122
msgid "Configure"
msgstr "Konfiguruj"

#: client/settings/fraud-protection/protection-levels/fp-help-text/index.tsx:12
msgid "Allows you to fine-tune the level of filtering according to your business needs."
msgstr "Pozwala dostosować poziom filtrowania do potrzeb biznesowych."

#: client/settings/fraud-protection/advanced-settings/rule-toggle.tsx:22
msgid "Block Payment"
msgstr "Zablokuj płatność"

#: client/settings/fraud-protection/protection-levels/index.tsx:110
msgid "Advanced"
msgstr "Zaawansowane"

#: client/settings/fraud-protection/advanced-settings/rule-description.tsx:12
msgid "How does this filter protect me?"
msgstr "W jaki sposób ten filtr mnie chroni?"

#: client/settings/fraud-protection/advanced-settings/index.tsx:57
msgid "Advanced fraud protection"
msgstr "Zaawansowana ochrona przed oszustwami"

#: client/settings/fraud-protection/advanced-settings/index.tsx:193
msgid "Settings were not saved."
msgstr "Nie zapisano ustawień."

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:50
msgid "Maximum purchase price"
msgstr "Maksymalna cena zakupu"

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:48
msgid "Minimum purchase price"
msgstr "Minimalna cena zakupu"

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:72
msgid "Maximum purchase price must be greater than the minimum purchase price."
msgstr "Maksymalna cena zakupu musi być większa niż minimalna cena zakupu."

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:68
msgid "A price range must be set for the \"Purchase Price threshold\" filter."
msgstr "Filtr „Limit ceny zakupu” musi mieć ustawiony zakres cen."

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:60
msgid "An unusually high purchase amount, compared to the average for your business, can indicate potential fraudulent activity."
msgstr "Nadzwyczajnie wysoka cena zakupu, biorąc pod uwagę średnią dla Twojej firmy, może oznaczać potencjalne oszustwo."

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:56
msgid "Purchase Price Threshold"
msgstr "Limit ceny zakupu"

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:52
msgid "A price range must be set for this filter to take effect."
msgstr "Aby ten filtr zaczął obowiązywać, należy ustawić zakres cen."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:31
#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:45
msgid "Limits"
msgstr "Limity"

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:36
msgid "Maximum items per order"
msgstr "Maksymalna liczba pozycji na zamówienie"

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:34
msgid "Minimum items per order"
msgstr "Minimalna liczba pozycji na zamówienie"

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:56
msgid "Maximum item count must be greater than the minimum item count on the \"Order Item Threshold\" rule."
msgstr "W regule „Limit liczby pozycji na zamówieniu” maksymalna liczba pozycji musi być większa niż minimalna liczba pozycji."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:52
msgid "An item range must be set for the \"Order Item Threshold\" filter."
msgstr "W przypadku filtra „Limit liczby pozycji na zamówieniu” należy ustawić zakres pozycji."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:46
msgid "An unusually high item count, compared to the average for your business, can indicate potential fraudulent activity."
msgstr "Nadzwyczajnie duża liczba pozycji, biorąc pod uwagę średnią dla Twojej firmy, może oznaczać potencjalne oszustwo."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:42
msgid "Order Items Threshold"
msgstr "Limit liczby pozycji na zamówieniu"

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:40
msgid "Maximum item count must be greater than the minimum item count."
msgstr "Maksymalna liczba pozycji musi być większa niż minimalna liczba pozycji."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:38
msgid "An item range must be set for this filter to take effect."
msgstr "Aby ten filtr zaczął obowiązywać, należy ustawić zakres pozycji."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:36
#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:50
msgid "Leave blank for no limit"
msgstr "Puste pole oznacza brak limitu."

#: client/settings/fraud-protection/advanced-settings/cards/international-ip-address.tsx:37
msgid "You should be especially wary when a customer has an international IP address but uses domestic billing and shipping information. Fraudsters often pretend to live in one location, but live and shop from another."
msgstr "Należy zachować szczególną ostrożność, gdy klient ma międzynarodowy adres IP, ale używa krajowych danych rozliczeniowych i wysyłkowych. Oszuści często ukrywają swoją rzeczywistą lokalizację."

#: client/settings/fraud-protection/advanced-settings/cards/international-ip-address.tsx:20
msgid "International IP Address"
msgstr "Międzynarodowy adres IP"

#: client/settings/fraud-protection/advanced-settings/cards/cvc-verification.tsx:33
msgid "Because the card security code appears only on the card and not on receipts or statements, the card security code provides some assurance that the physical card is in the possession of the buyer."
msgstr "Ponieważ kod bezpieczeństwa występuje tylko na karcie, a nie na pokwitowaniach czy wyciągach, stanowi pewną gwarancję, że fizyczna karta jest w posiadaniu nabywcy."

#: client/settings/fraud-protection/advanced-settings/cards/cvc-verification.tsx:17
msgid "CVC Verification"
msgstr "Weryfikacja kodu CVC"

#: client/settings/fraud-protection/advanced-settings/cards/avs-mismatch.tsx:16
msgid "Buyers who can provide the street number and post code on file with the issuing bank are more likely to be the actual account holder. AVS matches, however, are not a guarantee."
msgstr "Nabywca podający adres i kod pocztowy zgodne z danymi zarejestrowanymi przez bank wydający kartę jest najprawdopodobniej autentycznym właścicielem konta. Zgodność danych w usłudze AVS nie stanowi natomiast takiej gwarancji."

#: client/settings/fraud-protection/advanced-settings/cards/avs-mismatch.tsx:13
msgid "AVS Mismatch"
msgstr "Brak zgodności danych w usłudze AVS"

#: client/settings/fraud-protection/advanced-settings/cards/address-mismatch.tsx:15
msgid "There are legitimate reasons for a billing/shipping mismatch with a customer purchase, but a mismatch could also indicate that someone is using a stolen identity to complete a purchase."
msgstr "Istnieją autentyczne powody, dlaczego adres rozliczeniowy i adres wysyłkowy klienta mogą się różnić. Niezgodność może jednak wskazywać, że ktoś chce zrealizować transakcję z wykorzystaniem skradzionej tożsamości."

#: client/settings/fraud-protection/advanced-settings/cards/address-mismatch.tsx:12
msgid "Address Mismatch"
msgstr "Brak zgodności adresów"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:232
msgid "High"
msgstr "Wysoki"

#: client/settings/fraud-protection/protection-levels/protection-level-modal-notice/index.tsx:15
msgid "Provides a standard level of filtering that's suitable for most business."
msgstr "Zapewnia standardowy poziom filtrowania odpowiedni dla większości przedsiębiorstw."

#: client/settings/fraud-protection/protection-levels/fp-modal/index.tsx:32
msgid "The card's issuing bank cannot verify the CVV."
msgstr "Bank wydający kartę nie może zweryfikować jej kodu CVV."

#: client/settings/fraud-protection/protection-levels/fp-modal/index.tsx:31
msgid "The billing address does not match what is on file with the card issuer."
msgstr "Adres rozliczeniowy jest niezgodny z danymi zarejestrowanymi przez wydawcę karty."

#: client/settings/fraud-protection/protection-levels/fp-modal/index.tsx:25
msgid "Payments will be {{blocked}}blocked{{/blocked}} if:"
msgstr "Płatności będą {{blocked}}blokowane{{/blocked}} w następujących sytuacjach:"

#: client/settings/fraud-protection/protection-levels/fp-help-text/index.tsx:14
msgid "Provides a standard level of filtering that's suitable for most businesses."
msgstr "Zapewnia standardowy poziom filtrowania odpowiedni dla większości przedsiębiorstw."

#: client/settings/fraud-protection/protection-levels/fp-help-text/index.tsx:10
#: client/settings/fraud-protection/protection-levels/protection-level-modal-notice/index.tsx:16
msgid "Offers the highest level of filtering for stores, but may catch some legitimate transactions."
msgstr "Zapewnia najwyższy poziom filtrowania dla sklepów, ale może też błędnie blokować niektóre autentyczne transakcje."

#: includes/core/server/request/class-list-disputes.php:137
msgid "The search parameter must be a string, or an array of strings."
msgstr "Parametr wyszukiwania powinien być ciągiem lub tablicą ciągów."

#: includes/class-wc-payments-utils.php:284
msgid "Slovakia"
msgstr "Słowacja"

#: includes/class-wc-payments-utils.php:283
msgid "Slovenia"
msgstr "Słowenia"

#: includes/class-wc-payments-utils.php:277
msgid "Norway"
msgstr "Norwegia"

#: includes/class-wc-payments-utils.php:275
msgid "Malta"
msgstr "Malta"

#: includes/class-wc-payments-utils.php:274
msgid "Latvia"
msgstr "Łotwa"

#: includes/class-wc-payments-utils.php:273
msgid "Lithuania"
msgstr "Litwa"

#: includes/class-wc-payments-utils.php:268
msgid "Greece"
msgstr "Grecja"

#: includes/class-wc-payments-utils.php:266
msgid "Luxembourg"
msgstr "Luksemburg"

#: includes/class-wc-payments-utils.php:261
msgid "Finland"
msgstr "Finlandia"

#: includes/class-wc-payments-utils.php:260
msgid "Estonia"
msgstr "Estonia"

#: includes/class-wc-payments-utils.php:259
msgid "Denmark"
msgstr "Dania"

#: includes/class-wc-payments-utils.php:256
msgid "Cyprus"
msgstr "Cypr"

#: includes/core/server/request/class-create-and-confirm-setup-intention.php:105
msgid "Intentions require at least one payment method"
msgstr "Funkcja obsługująca zamiary płatności wymaga co najmniej jednej metody płatności"

#. Translators: %1$s is a provided date string, %2$s is a date format.
#: includes/core/server/class-request.php:768
msgid "%1$s is not a valid date for format %2$s."
msgstr "%1$s nie jest prawidłową datą dla formatu %2$s."

#. Translators: %s is a currency code.
#: includes/core/server/class-request.php:718
msgid "%s is not a supported currency for payments."
msgstr "%s nie jest obsługiwaną walutą płatności."

#. Translators: %s is a Stripe ID.
#: includes/core/server/class-request.php:669
msgid "%s is not a valid Stripe identifier"
msgstr "%s nie jest prawidłowym identyfikatorem Stripe"

#: includes/core/server/class-request.php:638
msgid "Empty parameter is not allowed"
msgstr "Pusty parametr jest niedozwolony"

#: client/settings/express-checkout/apple-google-pay-item.tsx:66
msgid "Offer customers a fast, secure checkout experience with Google Pay. By enabling this feature, you agree to {{stripeLink}}Stripe{{/stripeLink}}, and {{googleLink}}Google{{/googleLink}}'s terms of use."
msgstr "Oferuj klientom możliwość szybkiego i bezpiecznego finalizowania zamówień za pośrednictwem usługi Google Pay. {{stripeLink}}Włączając tę funkcję, wyrażasz zgodę na zasady użytkowania firm {{/stripeLink}}Stripe{{googleLink}} i {{/googleLink}}Google."

#: client/settings/express-checkout/apple-google-pay-item.tsx:64
msgid "Offer customers a fast, secure checkout experience with Google Pay."
msgstr "Oferuj klientom możliwość szybkiego i bezpiecznego finalizowania zamówień za pośrednictwem usługi Google Pay. "

#: client/settings/express-checkout/apple-google-pay-item.tsx:39
msgid "Apple Pay is an easy and secure way for customers to pay on your store. By enabling this feature, you agree to {{stripeLink}}Stripe{{/stripeLink}} and{{appleLink}} Apple{{/appleLink}}'s terms of use."
msgstr "Apple Pay to łatwy i bezpieczny sposób płacenia za zamówienia składane w Twoim sklepie. {{stripeLink}}Włączając tę funkcję, wyrażasz zgodę na zasady użytkowania firm {{/stripeLink}}Stripe{{appleLink}} i {{/appleLink}}Apple."

#: client/settings/express-checkout/apple-google-pay-item.tsx:37
msgid "Apple Pay is an easy and secure way for customers to pay on your store."
msgstr "Apple Pay to łatwy i bezpieczny sposób płacenia za zamówienia składane w Twoim sklepie. "

#: includes/payment-methods/class-sepa-payment-method.php:47
msgid "<strong>Test mode:</strong> use the test account number <number>********************</number>. Other payment methods may redirect to a Stripe test page to authorize payment. More test card numbers are listed <a>here</a>."
msgstr "<strong>Tryb testowy:</strong> użyj testowego numeru konta <number>********************</number>. Inne metody płatności mogą przekierować do strony testowej Stripe w celu autoryzacji płatności. Więcej testowych numerów kart można znaleźć <a>tutaj</a>."

#: includes/payment-methods/class-becs-payment-method.php:43
msgid "<strong>Test mode:</strong> use the test account number <number>*********</number>. Other payment methods may redirect to a Stripe test page to authorize payment. More test card numbers are listed <a>here</a>."
msgstr "<strong>Tryb testowy:</strong> użyj testowego numeru konta <number>*********</number>. Inne metody płatności mogą przekierować do strony testowej Stripe w celu autoryzacji płatności. Więcej testowych numerów kart można znaleźć <a>tutaj</a>."

#: client/overview/task-list/tasks/update-business-details-task.tsx:97
msgid "More details"
msgstr "Szczegóły"

#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:91
msgid "Suspended WooPayments Subscription in invoice.upcoming webhook handler because subscription next_payment date is 0."
msgstr "Zawieszono subskrypcję WooPayments w procedurze obsługi elementu webhook invoice.upcoming, ponieważ data next_payment za subskrypcję ma wartość 0."

#: includes/subscriptions/class-wc-payments-subscription-service.php:515
msgid "Suspended WooPayments Subscription because subscription status changed to on-hold."
msgstr "Zawieszono subskrypcję WooPayments, ponieważ jej status uległ zmianie na „wstrzymano”."

#: client/settings/payment-methods-list/payment-method.tsx:37
msgid "Required"
msgstr "Wymagany"

#: includes/class-wc-payments-order-success-page.php:333
msgid "We prevented multiple payments for the same order. If this was a mistake and you wish to try again, please create a new order."
msgstr "Zapobiegliśmy wielu płatnościom za to samo zamówienie. Jeśli to był błąd i chcesz spróbować ponownie, utwórz nowe zamówienie."

#: includes/class-wc-payments-order-success-page.php:316
msgid "We detected and prevented an attempt to pay for a duplicate order. If this was a mistake and you wish to try again, please create a new order."
msgstr "Wykryliśmy i zablokowaliśmy próbę opłacenia powielonego zamówienia. Jeśli to był błąd i chcesz spróbować ponownie, utwórz nowe zamówienie."

#. translators: order ID integer number
#: includes/class-duplicate-payment-prevention-service.php:168
msgid "WooCommerce Payments: detected and deleted order ID %d, which has duplicate cart content with this order."
msgstr "Płatności WooCommerce: wykryto i usunięto zamówienie o identyfikatorze %d, którego zawartość koszyka jest taka sama jak w tym zamówieniu."

#: includes/wc-payment-api/class-wc-payments-api-client.php:962
msgid "Currency From parameter is required"
msgstr "Wymagany jest parametr „Waluta wyjściowa”"

#. translators: 1) date in date_format or 'F j, Y'; 2) time in time_format or
#. 'g:i a'
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:1015
msgid "The customer must authorize this payment via a notification sent to them by the bank which issued their card. The authorization must be completed before %1$s at %2$s, when the charge will be attempted."
msgstr "Klient musi autoryzować tę płatność za pośrednictwem powiadomienia otrzymanego od banku wydającego kartę. Autoryzację należy ukończyć do %1$s o godz. %2$s, gdy nastąpi próba wprowadzenia zmiany."

#. translators: %s Stripe error message.
#: includes/class-wc-payments-webhook-processing-service.php:820
msgid "With the following message: <code>%s</code>"
msgstr "Z następującym komunikatem: <code>%s</code>"

#: includes/class-wc-payments-webhook-processing-service.php:815
msgid "For recurring payment greater than mandate amount or INR 15000, payment was not approved by the card holder."
msgstr "Płatność cykliczna o wartości przekraczającej upoważnioną kwotę lub 15 000 INR nie została zatwierdzona przez właściciela karty."

#: includes/class-wc-payments-webhook-processing-service.php:813
msgid "The customer's bank could not send pre-debit notification for the payment."
msgstr "Bank klienta nie był w stanie wysłać powiadomienia o wstępnej blokadzie środków w celu realizacji płatności."

#. translators: $1 $2 placeholders are opening and closing HTML link tags,
#. linking to documentation. $3 is WooPayments.
#: includes/subscriptions/templates/html-wcpay-deactivate-warning.php:38
msgid "If you do not want these subscriptions to continue to be billed, you should %1$scancel all subscriptions%2$s prior to deactivating %3$s. "
msgstr "Jeśli nie chcesz, aby te subskrypcje były nadal fakturowane, %1$sanuluj wszystkie subskrypcje%2$s przed wyłączeniem usługi %3$s. "

#. translators: $1 $2 $3 placeholders are opening and closing HTML link tags,
#. linking to documentation. $4 $5 placeholders are opening and closing strong
#. HTML tags. $6 is WooPayments.
#: includes/subscriptions/templates/html-wcpay-deactivate-warning.php:24
msgid "Your store has active subscriptions using the built-in %6$s functionality. Due to the %1$soff-site billing engine%3$s these subscriptions use, %4$sthey will continue to renew even after you deactivate %6$s%5$s. %2$sLearn more%3$s."
msgstr "Twój sklep ma aktywne subskrypcje korzystające z wbudowanej funkcji %6$s. Z uwagi na %1$szewnętrzny mechanizm fakturowania%3$s, z którego korzystają te subskrypcje, %4$sbędą się one odnawiały nawet po wyłączeniu usługi %6$s%5$s. %2$sDowiedz się więcej%3$s."

#: client/transactions/uncaptured/index.tsx:80
msgid "Action"
msgstr "Akcja"

#: client/data/authorizations/resolvers.ts:70
msgid "Error retrieving uncaptured transactions."
msgstr "Błąd podczas pobierania nieprzechwyconych transakcji."

#: client/data/authorizations/resolvers.ts:54
msgid "Error retrieving authorization."
msgstr "Błąd podczas pobierania autoryzacji."

#: client/data/authorizations/actions.ts:106
msgid "Payment for order #%s captured successfully."
msgstr "Płatność za zamówienie nr %s została przechwycona."

#: includes/notes/class-wc-payments-notes-set-up-stripelink.php:73
msgid "Reduce cart abandonment and create a frictionless checkout experience with Link by Stripe. Link autofills your customer’s payment and shipping details, so they can check out in just six seconds with the Link optimized experience."
msgstr "Ogranicz rezygnowanie z zakupów i spraw, by płatności stały się bezproblemowe dzięki metodzie Link by Stripe. Link automatycznie wypełnia dane klienta dotyczące płatności i wysyłki, umożliwiając mu finalizację zamówienia w ciągu zaledwie sześciu sekund dzięki optymalizacji procesu."

#: client/settings/express-checkout/link-item.tsx:51
msgid "Link by Stripe"
msgstr "Link by Stripe"

#: includes/notes/class-wc-payments-notes-set-up-stripelink.php:72
msgid "Increase conversion at checkout"
msgstr "Zwiększ konwersję podczas finalizacji zamówienia"

#: client/connect-account-page/info-notice-modal.tsx:69
#: client/settings/fraud-protection/protection-levels/fp-modal/index.tsx:33
#: client/settings/fraud-protection/tour/steps.tsx:61
msgid "Got it"
msgstr "Rozumiem"

#: includes/class-wc-payments-token-service.php:363
msgid "Stripe Link email"
msgstr "Adres e-mail w usłudze Stripe Link: "

#: includes/notes/class-wc-payments-notes-set-https-for-checkout.php:52
msgid "Enable HTTPS on your checkout pages to display all available payment methods and protect your customers data."
msgstr "Włącz protokół HTTPS na stronach finalizacji zamówień, aby wyświetlić wszystkie dostępne metody płatności i chronić dane swoich klientów."

#: includes/notes/class-wc-payments-notes-set-https-for-checkout.php:51
msgid "Enable secure checkout"
msgstr "Włącz bezpieczną finalizację zamówienia."

#. Translators: %d is the numeric ID of the product without a price.
#: includes/express-checkout/class-wc-payments-express-checkout-button-helper.php:762
msgid "Express checkout does not support products without prices! Please add a price to product #%d"
msgstr "Ekspresowa finalizacja zamówienia nie obsługuje produktów bez podanej ceny. Dodaj cenę do produktu nr %d"

#: includes/admin/class-wc-rest-payments-settings-controller.php:202
msgid "Monthly anchor for deposit scheduling when interval is set to monthly"
msgstr "Miesięczny punkt odniesienia do tworzenia harmonogramów płatności częściowych po ustawieniu interwału na miesięczny"

#: includes/admin/class-wc-rest-payments-settings-controller.php:198
msgid "Weekly anchor for deposit scheduling when interval is set to weekly"
msgstr "Tygodniowy punkt odniesienia do tworzenia harmonogramów płatności częściowych po ustawieniu interwału na tygodniowy"

#: includes/admin/class-wc-rest-payments-settings-controller.php:194
msgid "An interval for deposit scheduling."
msgstr "Interwał przy tworzeniu harmonogramów płatności częściowych"

#: client/utils/charge/index.ts:147
msgid "In-Person"
msgstr "Osobiście"

#: includes/class-wc-payments-order-service.php:562
msgid "<strong>Fee details:</strong>"
msgstr "<strong>Szczegóły opłaty:</strong>"

#: includes/class-wc-payments-apple-pay-registration.php:266
msgid "<a>Learn more</a>."
msgstr "<a>Dowiedz się więcej</a>"

#. translators: a: Link to the logs page
#: includes/class-wc-payments-apple-pay-registration.php:259
msgid "Please check the <a>logs</a> for more details on this issue. Debug log must be enabled under <strong>Advanced settings</strong> to see recorded logs."
msgstr "Sprawdź <a>dzienniki</a>, aby dowiedzieć się więcej o problemie. Aby można było zobaczyć zapisane dzienniki, w obszarze <strong>Ustawienia zaawansowane</strong> musi być włączony dziennik debugowania."

#: includes/class-wc-payments-apple-pay-registration.php:254
msgid "Express checkouts:"
msgstr "Ekspresowe realizacje zamówień:"

#: client/settings/transactions/manual-capture-control.tsx:56
msgid "Enable manual capture"
msgstr "Włącz ręczne pobieranie płatności"

#: client/settings/payment-methods-list/use-payment-method-availability.tsx:109
msgid "%s is not available to your customers when the \"manual capture\" setting is enabled."
msgstr "Metoda płatności %s jest niedostępna dla klientów, gdy jest włączone ustawienie „ręczne pobieranie”."

#: client/settings/express-checkout-settings/file-upload.tsx:100
msgid "Replace"
msgstr "Zastąp"

#: client/settings/express-checkout-settings/file-upload.tsx:95
msgid "Upload custom logo"
msgstr "Prześlij niestandardowe logo"

#. translators: %1$s: <h3> tag, %2$s: </h3> tag, %3$s: <p> tag, %4$s:
#. WooPayments, %5$s: <em> tag, %6$s: </em> tag, %7$s: <em> tag, %8$s: </em>
#. tag, %9$s: </p> tag.
#: includes/subscriptions/class-wc-payments-subscriptions-onboarding-handler.php:262
msgctxt "used in admin pointer script params in javascript as type pointer content"
msgid "%1$sChoose Subscription%2$s%3$s%4$s adds two new subscription product types - %5$sSimple subscription%6$s and %7$sVariable subscription%8$s.%9$s"
msgstr "%1$sWybierz subskrypcję%2$s%3$s%4$s zawiera dwie nowe usługi subskrypcji — %5$sSubskrypcja prosta%6$s i %7$sSubskrypcja zmienna%8$s.%9$s"

#: includes/admin/class-wc-rest-payments-settings-controller.php:267
msgid "Store logo to display to WooPay customers."
msgstr "Logo sklepu wyświetlane klientom WooPay."

#: includes/admin/class-wc-payments-admin.php:1223
#: includes/class-wc-payments-order-success-page.php:259
msgid "Card ending in"
msgstr "Karta o numerze kończącym się cyframi"

#: includes/admin/class-wc-payments-admin.php:1219
msgid "Paid with"
msgstr "Sposób płatności:"

#: client/vat/form/tasks/vat-number-task.tsx:93
msgid "8 to 12 digits with your country code prefix, for example DE *********."
msgstr "Jest to od 8 do 12 cyfr z kodem Twojego kraju na początku, np. DE *********."

#: client/vat/form/tasks/vat-number-task.tsx:58
msgid "VAT Number"
msgstr "Numer VAT"

#: client/vat/form/tasks/vat-number-task.tsx:78
msgid "If your sales exceed the VAT threshold for your country, you're required to register for a VAT Number."
msgstr "Jeśli sprzedaż przekroczy próg VAT dla Twojego kraju, musisz się zarejestrować, aby otrzymać numer VAT."

#: client/vat/form/tasks/company-data-task.tsx:64
msgid "Confirm"
msgstr "Potwierdź"

#: client/vat/form/tasks/company-data-task.tsx:60
msgid "Confirm your business details"
msgstr "Potwierdź dane firmy"

#: includes/class-wc-payments-account.php:2467
msgid "Failed to update Account locale. "
msgstr "Nie udało się zaktualizować ustawień regionalnych konta. "

#: includes/class-wc-payment-gateway-wcpay.php:2328
msgid "You shall refund this payment in the same application where the payment was made."
msgstr "Należy zwrócić płatność w tej samej aplikacji, w której została przekazana."

#: includes/class-wc-payments-woopay-button-handler.php:345
#: client/payment-methods-icons.tsx:51
#: client/settings/express-checkout/woopay-item.tsx:38
msgid "WooPay"
msgstr "WooPay"

#: client/components/inline-label-select/index.tsx:76
msgid "Currently selected: %s"
msgstr "Aktualnie wybrane: %s"

#: client/components/inline-label-select/index.tsx:73
msgid "No selection"
msgstr "Brak wyboru"

#. translators: %s: WooPayments.
#: woocommerce-payments.php:353
msgid "<strong>Notice:</strong> It appears that your 'wp-config.php' file might be using dynamic site URL values. Dynamic site URLs could cause %s to enter Safe Mode. <dynamicSiteUrlSupportLink>Learn how to set a static site URL.</dynamicSiteUrlSupportLink>"
msgstr "<strong>Uwaga:</strong> wygląda na to, że plik „wp-config.php” może używać dynamicznych wartości URL witryny. Dynamiczne adresy URL witryny mogą spowodować przejście usługi %s do trybu awaryjnego. <dynamicSiteUrlSupportLink> Dowiedz się, jak ustawić statyczny adres URL witryny.</dynamicSiteUrlSupportLink>"

#: includes/admin/class-wc-rest-payments-settings-controller.php:262
msgid "Custom message to display to WooPay customers."
msgstr "Indywidualna wiadomość wyświetlana klientom WooPay."

#: includes/admin/class-wc-rest-payments-settings-controller.php:257
msgid "If WooPay should be enabled."
msgstr "Jeśli usługa WooPay powinna być włączona."

#: client/data/payment-intents/resolvers.ts:21
msgid "Error retrieving transaction."
msgstr "Podczas pobierania transakcji wystąpił błąd."

#: templates/emails/email-ipp-receipt-compliance-details.php:34
#: templates/emails/plain/email-ipp-receipt-compliance-details.php:21
msgid "Application Name"
msgstr "Nazwa Aplikacji"

#: templates/emails/email-ipp-receipt-compliance-details.php:26
#: templates/emails/plain/email-ipp-receipt-compliance-details.php:19
msgid "Payment Method"
msgstr "Metoda płatności"

#. translators: %s: Order number
#: templates/emails/customer-ipp-receipt.php:31
#: templates/emails/plain/customer-ipp-receipt.php:26
msgid "This is the receipt for your order #%s:"
msgstr "Oto paragon za zamówienie nr %s:"

#. translators: %s: Customer first name
#: templates/emails/customer-ipp-receipt.php:29
#: templates/emails/plain/customer-ipp-receipt.php:24
msgid "Hi %s,"
msgstr "Cześć %s,"

#: includes/emails/class-wc-payments-email-ipp-receipt.php:364
msgid "Thanks for using {site_url}!"
msgstr "Dziękujemy za korzystanie z {site_url}!"

#: includes/emails/class-wc-payments-email-ipp-receipt.php:156
msgid "Your receipt for order: #{order_number}"
msgstr "Paragon za zamówienie: {order_number}"

#: includes/emails/class-wc-payments-email-ipp-receipt.php:146
msgid "Your {site_title} Receipt"
msgstr "Twój paragon: {site_title}"

#: includes/emails/class-wc-payments-email-ipp-receipt.php:62
msgid "New receipt emails are sent to customers when a new order is paid for with a card reader."
msgstr "Wiadomości e-mail z nowymi paragonami są wysyłane do klientów, gdy nowe zamówienie zostaje opłacone za pomocą czytnika kart."

#: includes/emails/class-wc-payments-email-ipp-receipt.php:61
msgid "New receipt"
msgstr "Nowy paragon"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:176
msgid "Shipping:"
msgstr "Wysyłka"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:168
msgid "Fees:"
msgstr "Opłaty: "

#. translators: %1: The document ID. %2: The error message.
#: includes/admin/class-wc-rest-payments-documents-controller.php:94
msgid "There was an error accessing document %1$s. %2$s"
msgstr "Podczas uzyskiwania dostępu do dokumentu %1$s wystąpił błąd. %2$s"

#: includes/admin/class-wc-payments-admin.php:459
msgid "Documents"
msgstr "Dokumenty"

#: includes/class-wc-payments-order-service.php:2190
msgid "Intent id was not included for payment complete status change."
msgstr "Nie uwzględniono identyfikatora zamiaru, co uniemożliwia zmianę statusu na „płatność ukończona”."

#: includes/class-woopay-tracker.php:98
msgid "No valid event name or type."
msgstr "Brak prawidłowej nazwy lub poprawnego typu zdarzenia."

#: includes/class-wc-payments-woopay-button-handler.php:210
#: includes/class-wc-payments.php:1801 includes/class-woopay-tracker.php:91
#: includes/woopay/class-woopay-session.php:616
#: includes/woopay/class-woopay-session.php:668
#: includes/woopay/class-woopay-session.php:694
#: includes/woopay/class-woopay-session.php:737
msgid "You aren’t authorized to do that."
msgstr "Brak uprawnień."

#: includes/notes/class-wc-payments-notes-loan-approved.php:76
msgid "View loan details"
msgstr "Wyświetl szczegóły pożyczki"

#. Translators: %1: total amount lent to the merchant formatted in the account
#. currency, %2: WooPayments
#: includes/notes/class-wc-payments-notes-loan-approved.php:50
msgid "Congratulations! Your capital loan has been approved and %1$s was deposited into the bank account linked to %2$s. You'll automatically repay the loan, plus a flat fee, through a fixed percentage of each %2$s transaction."
msgstr "Gratulacje! Twoja pożyczka kapitałowa została zatwierdzona — na rachunek bankowy powiązany z %2$s została przekazana kwota %1$s. Będziesz automatycznie spłacać pożyczkę oraz stałą opłatę procentową od każdej transakcji %2$s."

#: includes/notes/class-wc-payments-notes-loan-approved.php:46
msgid "Your capital loan has been approved!"
msgstr "Twoja pożyczka kapitałowa została zatwierdzona."

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:217
msgid "Powered by WooCommerce"
msgstr "Obsługiwane przez WooCommerce"

#: client/settings/express-checkout/apple-google-pay-item.tsx:25
msgid "Apple Pay / Google Pay"
msgstr "Apple Pay / Google Pay"

#: client/settings/express-checkout/apple-google-pay-item.tsx:81
#: client/settings/express-checkout/woopay-item.tsx:66
msgid "Customize"
msgstr "Dostosuj"

#: includes/admin/class-wc-rest-payments-files-controller.php:103
msgid "Sorry, you are not allowed to do that."
msgstr "Przepraszamy, nie posiadasz uprawnienia żeby to zrobić."

#: client/transactions/strings.ts:18
msgid "Loan repayment"
msgstr "Spłata pożyczki"

#: client/data/capital/resolvers.ts:37
msgid "Error retrieving the active loan summary."
msgstr "Podczas pobierania podsumowania aktywnej pożyczki wystąpił błąd."

#: client/components/active-loan-summary/index.tsx:86
msgid "First paydown"
msgstr "Pierwsza spłata"

#: client/components/active-loan-summary/index.tsx:83
msgid "Withhold rate"
msgstr "Kwota potrącenia"

#: client/components/active-loan-summary/index.tsx:82
msgid "Fixed fee"
msgstr "Opłata stała"

#: client/connect-account-page/strings.tsx:114
#: client/settings/phone-input/index.tsx:140
msgid "Mobile phone number"
msgstr "Numer telefonu komórkowego"

#: includes/woopay/class-woopay-session.php:881
#: includes/woopay/class-woopay-session.php:882
msgid "Privacy Policy"
msgstr "Polityka prywatności"

#: includes/woopay/class-woopay-session.php:878
msgid "Terms of Service"
msgstr "Warunki świadczenia usług"

#: includes/payment-methods/class-eps-payment-method.php:54
msgid "Accept your payment with EPS — a common payment method in Austria."
msgstr "Zaakceptuj płatność za pomocą EPS, popularnej metody płatności w Austrii."

#: client/connect-account-page/strings.tsx:113
msgid "Email address"
msgstr "Adres e-mail"

#: includes/class-wc-payments.php:1868
msgid "Contact information"
msgstr "Informacje kontaktowe"

#: includes/admin/class-wc-rest-payments-settings-controller.php:420
msgid "Error: Invalid address format!"
msgstr "Błąd: nieprawidłowy format adresu."

#: includes/admin/class-wc-rest-payments-settings-controller.php:384
msgid "Error: Invalid phone number: "
msgstr "Błąd: nieprawidłowy numer telefonu: "

#: includes/admin/class-wc-rest-payments-settings-controller.php:360
msgid "Error: Invalid email address: "
msgstr "Błąd: nieprawidłowy adres e-mail: "

#: client/disputes/index.tsx:79
msgid "Currency"
msgstr "Waluta"

#. translators: %1$s: The current site domain name. %2$s: The original site
#. domain name. Please keep hostname tags in your translation so that they can
#. be formatted properly. %3$s: WooPayments.
#: woocommerce-payments.php:378
msgid "Create a new connection to %3$s for <hostname>%1$s</hostname>. You’ll have to re-verify your business details to begin accepting payments. Your <hostname>%2$s</hostname> connection will remain as is."
msgstr "Utwórz nowe połączenie z usługą %1$s w przypadku domeny <hostname>%3$s</hostname>. Aby rozpocząć przyjmowanie płatności, musisz ponownie zweryfikować dane swojej firmy. Twoje połączenie w przypadku domeny <hostname>%2$s</hostname> pozostanie bez zmian."

#. translators: %s: WooPayments.
#: woocommerce-payments.php:341
msgid "We’ve detected that you have duplicate sites connected to %s. When Safe Mode is active, payments will not be interrupted. However, some features may not be available until you’ve resolved this issue below. Safe Mode is most frequently activated when you’re transferring your site from one domain to another, or creating a staging site for testing. A site administrator can resolve this issue. <safeModeLink>Learn more</safeModeLink>"
msgstr "Wykryliśmy, że masz zduplikowane domeny połączone z usługą %s. Gdy tryb bezpieczny jest aktywny, płatności nie będą przerywane. Jednak do czasu rozwiązania poniższego problemu niektóre funkcje mogą być niedostępne. Tryb bezpieczny najczęściej jest aktywowany podczas przenoszenia witryny między domenami lub tworzenia witryny tymczasowej na potrzeby testowania. Problem ten może rozwiązać administrator witryny. <safeModeLink>Dowiedz się więcej</safeModeLink>"

#: woocommerce-payments.php:336 woocommerce-payments.php:337
msgid "Create a new connection"
msgstr "Utwórz nowe połączenie"

#: woocommerce-payments.php:335
msgid "Transfer your connection"
msgstr "Przenieś swoje połączenie"

#: woocommerce-payments.php:334
msgid "Transfer connection"
msgstr "Przenieś połączenie"

#. translators: %s: WooPayments.
#: woocommerce-payments.php:321
msgid "We’ve detected that you have duplicate sites connected to %s. When Safe Mode is active, payments will not be interrupted. However, some features may not be available until you’ve resolved this issue below. Safe Mode is most frequently activated when you’re transferring your site from one domain to another, or creating a staging site for testing. <safeModeLink>Learn more</safeModeLink>"
msgstr "Wykryliśmy, że masz zduplikowane domeny połączone z usługą %s. Gdy tryb bezpieczny jest aktywny, płatności nie będą przerywane. Jednak do czasu rozwiązania poniższego problemu niektóre funkcje mogą być niedostępne. Tryb bezpieczny jest najczęściej aktywowany podczas przenoszenia witryny z jednej domeny do drugiej lub tworzenia tymczasowej witryny na potrzeby testowania. <safeModeLink>Dowiedz się więcej</safeModeLink>."

#: woocommerce-payments.php:318 woocommerce-payments.php:338
msgid "Safe Mode activated"
msgstr "Włączono tryb bezpieczny"

#: woocommerce-payments.php:317
msgid "Safe Mode"
msgstr "Tryb bezpieczny"

#: client/disputes/filters/config.ts:99
msgid "Select a dispute date"
msgstr "Wybierz datę sporu"

#: client/disputes/filters/config.ts:94
msgid "Select a dispute date filter match"
msgstr "Wybierz dopasowanie filtra daty sporu"

#: client/disputes/filters/config.ts:93
msgid "Remove dispute date filter"
msgstr "Usuń filtr daty sporu"

#: client/disputes/filters/config.ts:92
msgid "Disputed on date"
msgstr "Data sporu"

#: client/disputes/filters/config.ts:87
msgid "Disputes match {{select /}} filters"
msgstr "Spory pasują do {{select /}} filtrów"

#: client/disputes/filters/config.ts:70
msgid "All disputes"
msgstr "Wszystkie spory"

#: client/disputes/filters/config.ts:27
msgid "Dispute currency"
msgstr "Waluta sporu"

#: client/disputes/filters/config.ts:128
msgid "Select a dispute status"
msgstr "Wybierz status sporu"

#: client/disputes/filters/config.ts:123
msgid "Select a dispute status filter match"
msgstr "Wybierz dopasowanie filtra statusu sporu"

#: client/disputes/filters/config.ts:122
msgid "Remove dispute status filter"
msgstr "Usuń filtr statusu sporu"

#: includes/payment-methods/class-becs-payment-method.php:54
msgid "Bulk Electronic Clearing System — Accept secure bank transfer from Australia."
msgstr "Elektroniczny system rozliczeń zbiorczych — zaakceptuj bezpieczny przelew bankowy z Australii."

#. Translators: The %1 placeholder is a currency formatted price string
#. ($0.50). The %2 and %3 placeholders are opening and closing strong HTML
#. tags.
#: includes/subscriptions/class-wc-payments-subscription-service.php:430
msgid "There was a problem creating your subscription. %1$s doesn't meet the %2$sminimum recurring amount%3$s this payment method can process."
msgstr "Podczas tworzenia subskrypcji wystąpił błąd. Kwota %1$s jest niezgodna z %2$sminimalną kwotą płatności cyklicznej%3$s, jaką może przetworzyć ta metoda płatności"

#: includes/class-wc-payments-webhook-processing-service.php:807
msgid "You have exceeded the number of allowed verification attempts."
msgstr "Przekroczono liczbę dozwolonych prób weryfikacji."

#: includes/class-wc-payments-webhook-processing-service.php:805
msgid "Microdeposit transfers failed. Please check the account, institution and transit numbers."
msgstr "Niepowodzenie mikropłatności częściowych. Sprawdź numer konta, numer instytucji oraz numer rozliczeniowy."

#: includes/class-wc-payments-webhook-processing-service.php:803
msgid "The customer's bank account could not be located."
msgstr "Nie udało się zlokalizować rachunku bankowego klienta."

#: includes/class-wc-payments-webhook-processing-service.php:801
msgid "The customer's account has insufficient funds to cover this payment."
msgstr "Na rachunku klienta nie ma wystarczających środków, aby zrealizować tę płatność."

#: includes/class-wc-payments-webhook-processing-service.php:799
msgid "The customer has notified their bank that this payment was unauthorized."
msgstr "Klient powiadomił swój bank, że ta płatność nie została autoryzowana."

#: includes/class-wc-payments-webhook-processing-service.php:797
msgid "The customer's bank account has been closed."
msgstr "Rachunek bankowy klienta został zamknięty."

#: client/transactions/blocked/index.tsx:113
msgid "There was a problem generating your export."
msgstr "Podczas generowania eksportu wystąpił błąd."

#: client/components/error-boundary/index.tsx:30
msgid "There was an error rendering this view. Please contact support for assistance if the problem persists."
msgstr "Podczas renderowania tego widoku wystąpił błąd. Jeśli problem będzie się powtarzać, skontaktuj się z działem pomocy."

#. translators: %s: the error message.
#: includes/class-wc-payment-gateway-wcpay.php:3800
msgid "Intent creation failed with the following message: %s"
msgstr "Utworzenie zamiaru nie powiodło się i został wyświetlony  następujący komunikat: %s"

#: client/vat/form/tasks/company-data-task.tsx:62
msgid "Business name"
msgstr "Nazwa firmy"

#: client/settings/fraud-protection/protection-levels/index.tsx:121
msgid "Edit"
msgstr "Edytuj"

#: client/settings/express-checkout-settings/file-upload.tsx:34
msgid "The file you have attached is exceeding the maximum limit."
msgstr "Wielkość załączonego pliku przekracza maksymalny limit."

#: client/card-readers/list/list-item.tsx:12
msgid "Active"
msgstr "Aktywny"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:216
#: templates/emails/email-ipp-receipt-compliance-details.php:50
#: templates/emails/plain/email-ipp-receipt-compliance-details.php:25
msgid "Account Type"
msgstr "Typ konta"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:215
#: templates/emails/email-ipp-receipt-compliance-details.php:42
#: templates/emails/plain/email-ipp-receipt-compliance-details.php:23
msgid "AID"
msgstr "POMOC"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:214
msgid "Application name"
msgstr "Nazwa Aplikacji"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:204
msgid "AMOUNT PAID"
msgstr "ZAPŁACONA KWOTA"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:195
msgid "TOTAL"
msgstr "SUMA"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:154
msgid "SUBTOTAL"
msgstr "SUMA CZĘŚCIOWA"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:143
msgid "SKU"
msgstr "SKU"

#: includes/class-wc-payment-gateway-wcpay.php:793
msgid "Next we’ll ask you to share a few details about your business to create your account."
msgstr "Następnie poprosimy o podanie kilku informacji na temat Twojej firmy w celu utworzenia konta."

#: includes/class-wc-payment-gateway-wcpay.php:3210
msgid "Failed to update Stripe account. "
msgstr "Aktualizacja konta Stripe nie powiodła się. "

#: includes/admin/class-wc-rest-payments-settings-controller.php:190
msgid "A CSS hex color value representing the secondary branding color for this account."
msgstr "Wartość szesnastkowa koloru CSS reprezentująca dodatkowy kolor marki w przypadku tego konta."

#: includes/admin/class-wc-rest-payments-settings-controller.php:186
msgid "A CSS hex color value representing the primary branding color for this account."
msgstr "Wartość szesnastkowa koloru CSS reprezentująca podstawowy kolor marki w przypadku tego konta."

#: includes/admin/class-wc-rest-payments-settings-controller.php:182
msgid "An icon for the account."
msgstr "Ikona konta."

#: includes/admin/class-wc-rest-payments-settings-controller.php:178
msgid "A logo id for the account that will be used in Checkout"
msgstr "Identyfikator logo konta, które będzie używane podczas realizacji zamówienia"

#: includes/admin/class-wc-rest-payments-settings-controller.php:173
msgid "A publicly available phone number to call with support issues."
msgstr "Dostępny publicznie numer telefonu do zgłaszania problemów."

#: includes/admin/class-wc-rest-payments-settings-controller.php:168
msgid "A publicly available email address for sending support issues to."
msgstr "Dostępny publicznie adres e-mail do zgłaszania problemów."

#: includes/admin/class-wc-rest-payments-settings-controller.php:163
msgid "A publicly available mailing address for sending support issues to."
msgstr "Dostępny publicznie adres pocztowy do zgłaszania problemów."

#: includes/admin/class-wc-rest-payments-settings-controller.php:159
msgid "The business’s publicly available website."
msgstr "Dostępna publicznie witryna internetowa firmy."

#: includes/admin/class-wc-rest-payments-settings-controller.php:155
msgid "The customer-facing business name."
msgstr "Znana klientom nazwa firmy."

#: includes/admin/class-wc-rest-payments-reader-controller.php:284
msgid "Invalid payment intent"
msgstr "Nieprawidłowy zamiar płatności"

#: includes/admin/class-wc-payments-admin.php:433
msgid "Card Readers"
msgstr "Czytniki kart"

#: includes/admin/class-wc-payments-admin.php:547
msgid "Add new payment methods"
msgstr "Dodaj nowe metody płatności"

#: includes/class-wc-payments-utils.php:693
msgid "We couldn’t verify the postal code in your billing address. Make sure the information is current with your card issuing bank and try again."
msgstr "Nie można zweryfikować kodu pocztowego w adresie rozliczeniowym. Sprawdź aktualność informacji w banku, który wystawił kartę, i spróbuj ponownie."

#. translators: %s a formatted price.
#: includes/class-wc-payments-utils.php:680
msgid "The selected payment method requires a total amount of at least %s."
msgstr "Wybrana metoda płatności wymaga sumy w wysokości co najmniej %s."

#. translators: %s: WooPayments
#: includes/class-wc-payments-dependency-service.php:312
msgid "You have installed a development version of %s which requires files to be built. From the plugin directory, run <code>npm run build:client</code> to build and minify assets. Alternatively, you can download a pre-built version of the plugin from the <a1>WordPress.org repository</a1> or by visiting the <a2>releases page in the GitHub repository</a2>."
msgstr "Zainstalowano wersję deweloperską %s, która wymaga utworzenia plików. Aby skompilować i zminifikować zasoby, uruchom <code>npm run build:client</code> w katalogu wtyczek. Możesz też pobrać gotową wersję wtyczki z repozytorium <a1>WordPress.org</a1> lub odwiedzając <a2>stronę wersji w repozytorium GitHub</a2>."

#: includes/class-wc-payment-gateway-wcpay.php:1200
msgid "We couldn’t verify the postal code in the billing address. If the issue persists, suggest the customer to reach out to the card issuing bank."
msgstr "Nie można zweryfikować kodu pocztowego w adresie rozliczeniowym. Jeśli problem będzie się powtarzać, zasugeruj klientowi, aby skontaktował się z bankiem, który wystawił kartę."

#. translators: %1: the failed payment amount, %2: error message
#: includes/class-wc-payment-gateway-wcpay.php:1195
msgid "A payment of %1$s <strong>failed</strong>. %2$s"
msgstr "Płatność %1$s <strong>zakończona niepowodzeniem</strong>. %2$s"

#. translators: %1: the dispute message, %2: the dispute details URL
#: includes/class-wc-payments-webhook-processing-service.php:637
msgid "%1$s. See <a href=\"%2$s\">dispute overview</a> for more details."
msgstr "%1$s. Aby uzyskać więcej informacji, zapoznaj się z <a href=\"%2$s\">przeglądem sporów</a>."

#: includes/class-wc-payments-webhook-processing-service.php:632
msgid "Payment dispute has been updated"
msgstr "Zaktualizowano spór dotyczący płatności"

#: includes/class-wc-payments-webhook-processing-service.php:629
msgid "Payment dispute funds have been reinstated"
msgstr "Przywrócono środki związane ze sporem dotyczącym płatności"

#: includes/class-wc-payments-order-service.php:427
msgid "Dispute lost."
msgstr "Spór przegrany."

#. translators: %1: the dispute status
#: includes/class-wc-payments-order-service.php:1956
msgid "Dispute has been closed with status %1$s. See <a>dispute overview</a> for more details."
msgstr "Spór został zamknięty ze statusem %1$s. Więcej szczegółów znajdziesz w <a>przeglądzie sporów</a>."

#: includes/admin/class-wc-rest-payments-orders-controller.php:191
#: includes/emails/class-wc-payments-email-ipp-receipt.php:103
msgid "WooCommerce In-Person Payments"
msgstr "Płatności osobiste WooCommerce"

#: includes/class-wc-payments-status.php:124
msgid "Account ID"
msgstr "Identyfikator konta"

#: includes/class-wc-payments-status.php:115
#: client/merchant-feedback-prompt/index.tsx:87
msgid "No"
msgstr "Nie"

#: includes/class-wc-payments-status.php:115
#: client/merchant-feedback-prompt/index.tsx:79
msgid "Yes"
msgstr "Tak"

#: includes/class-wc-payments-status.php:108
msgid "Connected to WPCOM"
msgstr "Połączono z WPCOM"

#: includes/class-wc-payments-status.php:98
msgid "Version"
msgstr "Wersja"

#: client/transactions/list/index.tsx:338
msgid "N/A"
msgstr "brak"

#. translators: %1$s Opening strong tag, %2$s Closing strong tag
#: includes/subscriptions/class-wc-payments-product-service.php:663
msgid "%1$sThere was an issue saving your variations!%2$s A subscription product's billing period cannot be longer than one year. We have updated one or more of this product's variations to renew every %3$s."
msgstr "%1$sPodczas zapisywania wersji wystąpił problem.%2$s Okres rozliczeniowy subskrypcji nie może być dłuższy niż rok. Zaktualizowaliśmy wersję lub wersje produktu tak, by odnawiała(-y) się co %3$s."

#. translators: %1$s Opening strong tag, %2$s Closing strong tag, %3$s The
#. subscription renewal interval (every x time)
#: includes/subscriptions/class-wc-payments-product-service.php:618
msgid "%1$sThere was an issue saving your product!%2$s A subscription product's billing period cannot be longer than one year. We have updated this product to renew every %3$s."
msgstr "%1$sPodczas zapisywania produktu wystąpił błąd.%2$s Okres rozliczeniowy subskrypcji nie może być dłuższy niż rok. Zaktualizowaliśmy ten produkt, aby odnawiał się co %3$s."

#: client/settings/payment-methods-list/activation-modal.tsx:36
msgid "You need to provide more information to enable %s on your checkout."
msgstr "Musisz dostarczyć więcej informacji, aby umożliwić %s podczas realizacji zamówienia."

#: client/settings/payment-methods-list/activation-modal.tsx:30
msgid "You need to provide more information to enable %s on your checkout:"
msgstr "Musisz dostarczyć więcej informacji, aby umożliwić %s podczas realizacji zamówienia:"

#: client/settings/payment-methods-list/activation-modal.tsx:25
msgid "One more step to enable %s"
msgstr "Jeszcze jeden krok, aby umożliwić %s"

#: client/settings/payment-methods-list/activation-modal.tsx:40
msgid "If you choose to continue, our payment partner Stripe will send an e-mail to {{merchantEmail /}} to collect the required information"
msgstr "Jeśli postanowisz kontynuować, nasz partner płatności Stripe wyśle wiadomość e-mail do {{merchantEmail /}}, aby zebrać wymagane informacje."

#: client/settings/payment-methods-list/use-payment-method-availability.tsx:81
msgid "%s won't be visible to your customers until you provide the required information. Follow the instructions sent by our partner Stripe to %s."
msgstr "Klienci nie zobaczą %s, dopóki nie dostarczysz wymaganych informacji. Postępuj zgodnie z instrukcjami przesłanymi przez naszego partnera, firmę Stripe, do %s."

#: client/settings/payment-methods-list/use-payment-method-availability.tsx:73
msgid "This payment method is pending approval. Once approved, you will be able to use it."
msgstr "Ta metoda płatności oczekuje na zatwierdzenie. Po zatwierdzeniu będzie można z niej korzystać."

#: includes/subscriptions/class-wc-payments-invoice-service.php:302
msgid "The payment info couldn't be added to the order."
msgstr "Nie można dodać informacji o płatności do zamówienia."

#: client/settings/transactions/manual-capture-control.tsx:43
msgid " The setting is not applied to {{a}}In-Person Payments{{/a}} (please note that In-Person Payments should be captured within 2 days of authorization)."
msgstr " To ustawienie nie ma zastosowania do {{a}}płatności osobistych{{/a}} (takie płatności należy zarejestrować w ciągu 2 dni od daty autoryzacji)."

#: includes/subscriptions/templates/html-subscriptions-plugin-notice.php:14
#: includes/subscriptions/templates/html-wcpay-deactivate-warning.php:14
#: includes/subscriptions/templates/html-woo-payments-deactivate-warning.php:14
msgid "Are you sure?"
msgstr "Czy jesteś pewien?"

#: includes/class-wc-payment-gateway-wcpay.php:1484
#: includes/payment-methods/class-cc-payment-method.php:104
msgid "Credit / Debit Cards"
msgstr "Karty kredytowe/debetowe"

#: includes/admin/class-wc-rest-payments-orders-controller.php:153
#: includes/admin/class-wc-rest-payments-orders-controller.php:293
msgid "Payment cannot be captured for partially or fully refunded orders."
msgstr "Nie można przechwycić płatności za zamówienia zwrócone w części albo w całości."

#: includes/wc-payment-api/class-wc-payments-api-client.php:1378
msgid "Price ID is required"
msgstr "Wymagany jest identyfikator ceny"

#: includes/wc-payment-api/class-wc-payments-api-client.php:1346
msgid "Product ID is required"
msgstr "Wymagany jest identyfikator produktu"

#. Translators: %s Property name not found in event data array.
#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:330
msgid "%s not found in array"
msgstr "Nie znaleziono %s w tablicy"

#. Translators: %d Number of failed renewal attempts.
#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:295
msgid "WooPayments subscription renewal attempt %d failed."
msgid_plural "WooPayments subscription renewal attempt %d failed."
msgstr[0] "Próba %d odnowienia subskrypcji WooPayments nie powiodła się."
msgstr[1] "Próba %d odnowienia subskrypcji WooPayments nie powiodła się."
msgstr[2] "Próba %d odnowienia subskrypcji WooPayments nie powiodła się."

#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:260
msgid "Unable to generate renewal order for subscription to record the incoming \"invoice.payment_failed\" event."
msgstr "Nie można wygenerować zlecenia odnowienia subskrypcji w celu zapisania nadchodzącego zdarzenia „invoice.payment_failed”."

#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:233
msgid "Cannot find subscription for the incoming \"invoice.payment_failed\" event."
msgstr "Nie można znaleźć subskrypcji dla nadchodzącego zdarzenia „invoice.payment_failed”."

#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:152
msgid "Unable to generate renewal order for subscription on the \"invoice.paid\" event."
msgstr "Nie można wygenerować zlecenia odnowienia subskrypcji dla zdarzenia „invoice.paid”."

#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:138
msgid "Cannot find subscription for the incoming \"invoice.paid\" event."
msgstr "Nie można znaleźć subskrypcji dla nadchodzącego zdarzenia „invoice.paid”."

#. Translators: %s Scheduled/upcoming payment date in Y-m-d H:i:s format.
#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:102
msgid "Next automatic payment scheduled for %s."
msgstr "Następną płatność automatyczną zaplanowano na %s."

#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:79
msgid "Cannot find subscription to handle the \"invoice.upcoming\" event."
msgstr "Nie można znaleźć subskrypcji na przetworzenie zdarzenia „invoice.upcoming”."

#. Translators: %s Stripe subscription item ID.
#: includes/subscriptions/class-wc-payments-subscription-service.php:1023
msgid "Unable to set subscription item ID meta for WooPayments subscription item %s."
msgstr "Nie można ustawić meta identyfikatora pozycji subskrypcji dla pozycji %s subskrypcji WooPayments."

#: includes/subscriptions/class-wc-payments-subscription-service.php:753
msgid "The subscription's next payment date has been updated to match WooPayments server."
msgstr "Data następnej płatności subskrypcji została zaktualizowana w celu zachowania zgodności z serwerem WooPayments."

#: includes/subscriptions/class-wc-payments-subscription-service.php:656
msgid "We've successfully collected payment for your subscription using your new payment method."
msgstr "Płatność za subskrypcję została pomyślnie pobrana za pomocą nowej metody płatności."

#: includes/subscriptions/class-wc-payments-subscription-service.php:397
msgid "There was a problem creating your subscription. Please try again or contact us for assistance."
msgstr "Wystąpił problem z utworzeniem subskrypcji. Spróbuj ponownie albo skontaktuj się z nami, aby uzyskać pomoc."

#. Translators: %s Coupon code.
#: includes/subscriptions/class-wc-payments-subscription-service.php:347
msgid "Coupon - %s"
msgstr "Kupon — %s"

#: includes/subscriptions/class-wc-payments-subscription-change-payment-method-handler.php:226
msgid "Update and retry payment"
msgstr "Zaktualizuj i spróbuj ponownie dokonać płatności"

#: includes/subscriptions/class-wc-payments-subscription-change-payment-method-handler.php:175
msgid "Your subscription's last renewal failed payment. Please update your payment details so we can reattempt payment."
msgstr "Nieudana płatność za ostatnie odnowienie subskrypcji. Zaktualizuj szczegóły płatności, abyśmy mogli ponowić próbę jej zrealizowania."

#: includes/subscriptions/class-wc-payments-subscription-change-payment-method-handler.php:159
msgid "Update payment details"
msgstr "Zaktualizuj szczegóły płatności"

#: includes/subscriptions/class-wc-payments-subscription-change-payment-method-handler.php:53
msgid "Update payment method"
msgstr "Zaktualizuj metodę płatności"

#: includes/subscriptions/class-wc-payments-invoice-service.php:449
msgid "The WooPayments invoice items do not match WC subscription items."
msgstr "Pozycje faktury WooPayments nie odpowiadają pozycjom subskrypcji WC."

#: includes/multi-currency/CurrencySwitcherWidget.php:50
msgid "Currency Switcher Widget"
msgstr "Widżet przełącznika walut"

#: client/overview/task-list/tasks/update-business-details-task.tsx:103
msgid "Update"
msgstr "Zaktualizuj"

#: includes/wc-payment-api/class-wc-payments-api-client.php:1899
msgid "Address country and line 1 are required."
msgstr "Wymagane jest podanie kraju i wypełnienie pierwszego wiersza adresu."

#: includes/class-wc-payments-utils.php:689
#: includes/class-wc-payments-utils.php:691
msgid "We're not able to process this request. Please refresh the page and try again."
msgstr "Nie możemy przetworzyć tego żądania. Odśwież stronę i spróbuj ponownie."

#: includes/class-wc-payments-account.php:1603
msgid "There was a duplicate attempt to initiate account setup. Please wait a few seconds and try again."
msgstr "Wystąpiła zduplikowana próba rozpoczęcia konfiguracji konta. Odczekaj kilka sekund i spróbuj ponownie."

#. translators: %1: the failed payment amount
#: includes/class-wc-payment-gateway-wcpay.php:1229
msgid "A payment of %1$s <strong>failed</strong> to complete because of too many failed transactions. A rate limiter was enabled for the user to prevent more attempts temporarily."
msgstr "Płatność %1$s <strong>nie powiodła się</strong> z uwagi na zbyt wiele nieudanych transakcji. Czasowo włączono ograniczenie kursu dla użytkownika, aby zapobiec kolejnym próbom."

#: includes/class-wc-payment-gateway-wcpay.php:1120
msgid "Your payment was not processed."
msgstr "Płatność nie została przetworzona."

#: includes/payment-methods/class-bancontact-payment-method.php:54
msgid "Bancontact is a bank redirect payment method offered by more than 80% of online businesses in Belgium."
msgstr "Bancontact to metoda płatności oparta na przekierowaniu do banku, oferowana przez ponad 80% sklepów internetowych w Belgii."

#: includes/admin/class-wc-rest-payments-orders-controller.php:379
msgid "Invalid order status"
msgstr "Nieprawidłowy status zamówienia"

#: includes/payment-methods/class-ideal-payment-method.php:54
msgid "Expand your business with iDEAL — Netherlands’s most popular payment method."
msgstr "Rozwijaj firmę, korzystając z iDEAL, najpopularniejszej w Holandii metody płatności."

#: includes/payment-methods/class-p24-payment-method.php:54
msgid "Accept payments with Przelewy24 (P24), the most popular payment method in Poland."
msgstr "Akceptuj płatności, korzystając z Przelewy24 (P24), najpopularniejszej w Polsce metody płatności."

#: includes/class-wc-payments-order-success-page.php:136
#: includes/class-wc-payments-order-success-page.php:530
msgid "Reference"
msgstr "Opis"

#: includes/multi-currency/SettingsOnboardCta.php:80
msgid "Get started"
msgstr "Rozpocznij"

#: client/payment-details/transaction-breakdown/utils.ts:11
msgid "Total"
msgstr "Łącznie"

#: includes/class-wc-payment-gateway-wcpay.php:2300
msgid "The refund amount is not valid."
msgstr "Nieprawidłowa kwota zwrotu."

#: includes/admin/class-wc-payments-admin.php:244
#: includes/admin/class-wc-payments-admin.php:284
msgid "Unsupported currency:"
msgstr "Nieobsługiwana waluta:"

#: client/settings/payment-methods-list/payment-method.tsx:89
msgid "Base transaction fees: %s"
msgstr "Podstawowe opłaty za transakcje: %s"

#. translators: %1$s: name of the blog, %2$s: link to payment re-authentication
#. URL, note: no full stop due to url at the end.
#. translators: %1$s: name of the blog, %2$s: link to checkout payment url,
#. note: no full stop due to url at the end.
#: includes/compat/subscriptions/emails/failed-renewal-authentication.php:18
#: includes/compat/subscriptions/emails/plain/failed-renewal-authentication.php:17
msgctxt "In failed renewal authentication email"
msgid "The automatic payment to renew your subscription with %1$s has failed. To reactivate the subscription, please login and authorize the renewal from your account page: %2$s"
msgstr "Automatyczna płatność z tytułu odnowienia subskrypcji %1$s nie powiodła się. Aby ponownie aktywować subskrypcję, zaloguj się i zatwierdź odnowienie z poziomu strony konta: %2$s"

#. translators: %1$s: an order number, %2$s: the customer's full name, %3$s:
#. lowercase human time diff in the form returned by wcs_get_human_time_diff(),
#. e.g. 'in 12 hours'.
#: includes/compat/subscriptions/emails/failed-renewal-authentication-requested.php:22
#: includes/compat/subscriptions/emails/plain/failed-renewal-authentication-requested.php:18
msgctxt "In admin renewal failed email"
msgid "The automatic recurring payment for order %1$s from %2$s has failed. The customer was sent an email requesting authentication of payment. If the customer does not authenticate the payment, they will be requested by email again %3$s."
msgstr "Automatyczna płatność cykliczna za zamówienie %1$s z firmy %2$s nie powiodła się. Wiadomość e-mail z prośbą o autoryzację płatności została wysłana do klienta. Jeśli klient nie autoryzuje płatności, wiadomość e-mail zostanie ponownie wysłana w ciągu %3$s."

#: includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php:116
msgctxt "an email notification"
msgid "Enable/disable"
msgstr "Włącz/wyłącz"

#: includes/class-wc-payment-gateway-wcpay.php:4154
#: includes/class-wc-payment-gateway-wcpay.php:4164
msgid "Unable to update UPE appearance values at this time."
msgstr "Nie można teraz zaktualizować wartości wyglądu funkcji UPE."

#: client/merchant-feedback-prompt/index.tsx:89
#: client/overview/modal/connection-success/strings.tsx:7
msgid "Dismiss"
msgstr "Zignoruj"

#. translators: %1 User's country, %2 Selected currency name, %3 Default store
#. currency name, %4 Link to switch currency
#: includes/multi-currency/MultiCurrency.php:982
msgid "We noticed you're visiting from %1$s. We've updated our prices to %2$s for your shopping convenience. <a href=\"%4$s\">Use %3$s instead.</a>"
msgstr "Zauważyliśmy, że Twój kraj to %1$s. Z myślą o Twojej wygodzie zmieniliśmy walutę cen na %2$s. <a href=\"%4$s\">Zamiast tego użyj %3$s.</a>"

#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:323
msgid ""
"Almost there!\n"
"\n"
"Your order has already been created, the only thing that still needs to be done is for you to authorize the payment with your bank."
msgstr ""
"To już prawie koniec!\n"
"\n"
"Zamówienie zostało utworzone, musisz tylko autoryzować płatność w banku."

#: includes/compat/subscriptions/emails/failed-renewal-authentication.php:18
msgid "Authorize the payment &raquo;"
msgstr "Autoryzuj płatność »"

#: includes/compat/subscriptions/emails/failed-renewal-authentication-requested.php:34
#: includes/compat/subscriptions/emails/plain/failed-renewal-authentication-requested.php:27
msgid "The renewal order is as follows:"
msgstr "Zamówienie odnowienia:"

#: includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php:143
msgid "Payment authorization needed for renewal of order {order_number}"
msgstr "W celu odnowienia zamówienia o numerze {order_number} konieczna jest autoryzacja płatności"

#: includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php:134
msgid "Payment authorization needed for renewal of {site_title} order {order_number}"
msgstr "W celu odnowienia zamówienia z {site_title} o numerze {order_number} konieczna jest autoryzacja płatności"

#: includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php:118
msgid "Enable this email notification"
msgstr "Włącz to powiadomienie mailowe"

#: includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php:33
msgid "Sent to a customer when a renewal fails because the transaction requires an SCA verification. The email contains renewal order information and payment links."
msgstr "Wysyłane do klienta w przypadku niepowodzenia odnowienia ze względu na konieczność weryfikacji SCA transakcji. Wiadomość e-mail zawiera informacje o zamówieniu odnowienia i łącza do płatności."

#: includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php:32
msgid "Failed subscription renewal SCA authentication"
msgstr "Niepowodzenie autoryzacji SCA odnowienia subskrypcji"

#: includes/compat/subscriptions/class-wc-payments-email-failed-authentication-retry.php:58
msgid "[{site_title}] Automatic payment failed for {order_number}. Customer asked to authenticate payment and will be notified again {retry_time}"
msgstr "[{site_title}] Niepowodzenie płatności automatycznej za zamówienie nr {order_number}. Klient został poproszony o autoryzację płatności i zostanie powiadomiony ponownie {retry_time}"

#: includes/compat/subscriptions/class-wc-payments-email-failed-authentication-retry.php:57
msgid "Automatic renewal payment failed due to authentication required"
msgstr "Niepowodzenie automatycznego odnowienia płatności ze względu na wymóg autoryzacji"

#: includes/compat/subscriptions/class-wc-payments-email-failed-authentication-retry.php:55
msgid "Payment authentication requested emails are sent to chosen recipient(s) when an attempt to automatically process a subscription renewal payment fails because the transaction requires an SCA verification, the customer is requested to authenticate the payment, and a retry rule has been applied to notify the customer again within a certain time period."
msgstr "Wiadomości e-mail z prośbą o autoryzację płatności są wysyłane do wybranych odbiorców w przypadku niepowodzenia automatycznego przetwarzania płatności za odnowienie subskrypcji ze względu na wymóg weryfikacji SCA transakcji. Klient jest proszony o autoryzację płatności i zostaje zastosowana zasada ponownej próby w celu ponownego powiadomienia klienta w określonym przedziale czasowym."

#: includes/compat/subscriptions/class-wc-payments-email-failed-authentication-retry.php:54
msgid "Payment authentication requested email"
msgstr "Wiadomość e-mail z prośbą o autoryzację płatności"

#: includes/class-wc-payments-utils.php:285
msgid "Singapore"
msgstr "Singapur"

#: includes/class-wc-payments-utils.php:280
msgid "Portugal"
msgstr "Portugalia"

#: includes/class-wc-payments-utils.php:279
msgid "Poland"
msgstr "Polska"

#: includes/class-wc-payments-utils.php:276
msgid "Netherlands"
msgstr "Holandia"

#: includes/class-wc-payments-utils.php:269
msgid "Hong Kong"
msgstr "Hongkong"

#: includes/class-wc-payments-utils.php:255
msgid "Switzerland"
msgstr "Szwajcaria"

#: includes/class-wc-payments-utils.php:252
msgid "Belgium"
msgstr "Belgia"

#: includes/class-wc-payments-utils.php:250
msgid "Austria"
msgstr "Austria"

#. translators: %s List of currencies that are already translated in
#. WooCommerce core.
#: includes/multi-currency/AdminNotices.php:112
msgid "The store currency was recently changed. The following currencies are set to manual rates and may need updates: %s"
msgstr "Waluta sklepu została ostatnio zmieniona. Następujące waluty mają ręczne kursy wymiany i mogą wymagać aktualizacji: %s"

#: includes/multi-currency/AdminNotices.php:76
msgid "Cheatin&#8217; huh?"
msgstr "Chyba oszukujesz?"

#: includes/multi-currency/AdminNotices.php:72
msgid "Action failed. Please refresh the page and retry."
msgstr "Akcja zakończona niepowodzeniem. Odśwież stronę i spróbuj ponownie."

#: includes/multi-currency/CurrencySwitcherWidget.php:66
msgid "Display flags in supported devices"
msgstr "Wyświetl flagi na obsługiwanych urządzeniach"

#: client/settings/general-settings/test-mode-confirm-modal.tsx:15
msgid "Enable"
msgstr "Włącz"

#: includes/admin/class-wc-payments-admin.php:797
msgid "Refunding manually requires reimbursing your customer offline via cash, check, etc. The refund amounts entered here will only be used to balance your analytics."
msgstr "Aby dokonać zwrotu ręcznego, należy zwrócić koszty klientowi offline: gotówką, czekiem itp. Wprowadzone tutaj kwoty zwrotów będą używane tylko do zbilansowania analiz."

#: includes/class-wc-payment-gateway-wcpay.php:1849
msgid "Payment method successfully added."
msgstr "Metoda płatności została dodana."

#: includes/class-wc-payments-utils.php:286
msgid "United States (US)"
msgstr "Stany Zjednoczone"

#: includes/class-wc-payments-utils.php:278
msgid "New Zealand"
msgstr "Nowa Zelandia"

#: includes/class-wc-payments-utils.php:272
msgid "Italy"
msgstr "Włochy"

#: includes/class-wc-payments-utils.php:271
msgid "Ireland"
msgstr "Irlandia"

#: includes/class-wc-payments-utils.php:267
msgid "United Kingdom (UK)"
msgstr "Wielka Brytania"

#: includes/class-wc-payments-utils.php:263
msgid "France"
msgstr "Francja"

#: includes/class-wc-payments-utils.php:262
msgid "Spain"
msgstr "Hiszpania"

#: includes/class-wc-payments-utils.php:258
msgid "Germany"
msgstr "Niemcy"

#: includes/class-wc-payments-utils.php:254
msgid "Canada"
msgstr "Kanada"

#: includes/class-wc-payments-utils.php:251
msgid "Australia"
msgstr "Australia"

#. translators: The text encapsulated in `**` can be replaced with "Apple Pay"
#. or "Google Pay". Please translate this text, but don't remove the `**`.
#: includes/express-checkout/class-wc-payments-express-checkout-button-handler.php:133
msgid "To complete your transaction with **the selected payment method**, you must log in or create an account with our site."
msgstr "Aby realizować transakcje za pomocą **wybranej metody płatności**, musisz się zalogować lub utworzyć konto w naszej witrynie."

#: includes/notes/class-wc-payments-notes-instant-deposits-eligible.php:53
msgid "Request an instant payout"
msgstr "Poproś o wypłatę natychmiastową"

#. translators: %s: WooPayments
#: includes/notes/class-wc-payments-notes-instant-deposits-eligible.php:41
msgid "Get immediate access to your funds when you need them – including nights, weekends, and holidays. With %s' <a>Instant Payouts feature</a>, you're able to transfer your earnings to a debit card within minutes."
msgstr "Uzyskaj natychmiastowy dostęp do środków, gdy ich potrzebujesz, także w nocy oraz w weekendy i święta. Dzięki dostępnej w usłudze %s funkcji <a>natychmiastowej wypłaty</a> możesz przelać swoje przychody na kartę debetową w ciągu kilku minut."

#: client/settings/transactions/manual-capture-control.tsx:39
msgid "Charge must be captured on the order details screen within 7 days of authorization, otherwise the authorization and order will be canceled."
msgstr "Opłata musi zostać przechwycona na ekranie ze szczegółami zamówienia w ciągu 7 dni od autoryzacji. W przeciwnym razie autoryzacja i zamówienie zostaną anulowane."

#: includes/multi-currency/Notes/NoteMultiCurrencyAvailable.php:54
#: includes/notes/class-wc-payments-notes-set-up-stripelink.php:80
msgid "Set up now"
msgstr "Skonfiguruj teraz"

#: includes/multi-currency/Notes/NoteMultiCurrencyAvailable.php:47
msgid "Boost your international sales by allowing your customers to shop and pay in their local currency."
msgstr "Zwiększ swoją sprzedaż międzynarodową, umożliwiając klientom robienie zakupów w ich lokalnej walucie."

#: includes/multi-currency/Notes/NoteMultiCurrencyAvailable.php:46
msgid "Sell worldwide in multiple currencies"
msgstr "Sprzedawaj na całym świecie w wielu walutach"

#: includes/class-wc-payment-gateway-wcpay.php:540
msgid "Large"
msgstr "Duży"

#: includes/class-wc-payment-gateway-wcpay.php:539
msgid "Medium"
msgstr "Średni"

#: includes/class-wc-payment-gateway-wcpay.php:534
msgid "Select the size of the button."
msgstr "Wybierz rozmiar przycisku."

#: includes/class-wc-payment-gateway-wcpay.php:532
msgid "Size of the button displayed for Express Checkouts"
msgstr "Wielkość przycisku wyświetlanego dla ekspresowej realizacji zamówienia."

#: includes/admin/class-wc-rest-payments-settings-controller.php:243
msgid "1-click checkout button themes."
msgstr "Motywy przycisków realizacji zamówienia za pomocą jednego kliknięcia."

#: includes/admin/class-wc-rest-payments-settings-controller.php:233
msgid "1-click checkout button sizes."
msgstr "Rozmiary przycisków realizacji transakcji za pomocą jednego kliknięcia."

#: includes/admin/class-wc-rest-payments-settings-controller.php:224
msgid "1-click checkout button types."
msgstr "Typy przycisków realizacji zamówienia za pomocą jednego kliknięcia."

#: includes/multi-currency/Settings.php:46
#: includes/multi-currency/SettingsOnboardCta.php:49
msgctxt "Settings tab label"
msgid "Multi-currency"
msgstr "Wiele walut"

#: includes/multi-currency/UserSettings.php:69
msgid "Select your preferred currency for shopping and payments."
msgstr "Wybierz swoją walutę preferowaną przy zakupach i płatnościach."

#: includes/multi-currency/UserSettings.php:54
msgid "Default currency"
msgstr "Waluta domyślna"

#. translators: %s: url to documentation.
#: includes/multi-currency/SettingsOnboardCta.php:102
msgid "Accept payments in multiple currencies. Prices are converted based on exchange rates and rounding rules. <a href=\"%s\">Learn more</a>"
msgstr "Akceptuj płatności w wielu walutach. Ceny są przeliczane na podstawie kursów wymiany i zasad zaokrąglania. <a href=\"%s\">Dowiedz się więcej</a>"

#: includes/multi-currency/SettingsOnboardCta.php:99
msgid "Enabled currencies"
msgstr "Dostępne waluty"

#: includes/multi-currency/CurrencySwitcherWidget.php:61
msgid "Display currency symbols"
msgstr "Wyświetl symbole walut"

#: includes/multi-currency/CurrencySwitcherWidget.php:56
msgid "Title"
msgstr "Tytuł"

#: includes/multi-currency/CurrencySwitcherWidget.php:51
msgid "Let your customers switch between your enabled currencies"
msgstr "Zapewnij swoim klientom możliwość przełączania między dostępnymi walutami"

#: includes/class-wc-payment-gateway-wcpay.php:476
msgid "Book"
msgstr "Zarezerwuj"

#: client/onboarding/strings.tsx:83
#: client/settings/payment-methods-list/activation-modal.tsx:27
#: client/vat/form/tasks/vat-number-task.tsx:170
msgid "Continue"
msgstr "Kontynuuj"

#. translators: localized exception message
#: includes/class-wc-payment-gateway-wcpay.php:2037
msgid "UPE payment failed: %s"
msgstr "Płatność UPE zakończona niepowodzeniem: %s"

#: includes/admin/class-wc-rest-payments-settings-controller.php:215
#: includes/admin/class-wc-rest-payments-settings-controller.php:272
msgid "Express checkout locations that should be enabled."
msgstr "Należy włączyć lokalizacje ekspresowej realizacji zamówienia."

#: includes/admin/class-wc-rest-payments-settings-controller.php:92
msgid "Payment method IDs that should be enabled. Other methods will be disabled."
msgstr "Identyfikatory metod płatności, które mają być włączone. Inne metody zostaną wyłączone."

#: client/settings/fraud-protection/advanced-settings/index.tsx:215
msgid "Save changes"
msgstr "Zapisz zmiany"

#: client/overview/task-list/tasks/update-business-details-task.tsx:53
msgid "Payments and payouts are disabled for this account until missing business information is updated."
msgstr "Płatności i wypłaty są wyłączone na tym koncie do momentu uzupełnienia brakujących informacji o firmie."

#: client/overview/task-list/tasks/update-business-details-task.tsx:27
msgid "Update by %s to avoid a disruption in payouts."
msgstr "Zaktualizuj do %s, aby uniknąć zakłóceń w wypłatach."

#: client/payment-methods-icons.tsx:44
#: client/settings/express-checkout/apple-google-pay-item.tsx:60
msgid "Google Pay"
msgstr "Google Pay"

#: client/payment-methods-icons.tsx:40
#: client/settings/express-checkout/apple-google-pay-item.tsx:33
msgid "Apple Pay"
msgstr "Apple Pay"

#: client/deposits/list/index.tsx:149 client/transactions/list/index.tsx:481
#: client/transactions/uncaptured/index.tsx:164
msgid "total"
msgstr "suma"

#: includes/admin/class-wc-rest-payments-orders-controller.php:267
#: includes/admin/class-wc-rest-payments-orders-controller.php:346
#: includes/admin/class-wc-rest-payments-orders-controller.php:407
#: includes/admin/class-wc-rest-payments-orders-controller.php:449
#: includes/admin/class-wc-rest-payments-orders-controller.php:572
msgid "Unexpected server error"
msgstr "Nieoczekiwany błąd serwera"

#: includes/admin/class-wc-rest-payments-orders-controller.php:215
#: includes/admin/class-wc-rest-payments-orders-controller.php:326
#: includes/admin/class-wc-rest-payments-orders-controller.php:556
#: includes/class-wc-payment-gateway-wcpay.php:3801
msgid "Unknown error"
msgstr "Nieznany błąd"

#. translators: %s: the error message.
#: includes/admin/class-wc-rest-payments-orders-controller.php:214
#: includes/admin/class-wc-rest-payments-orders-controller.php:325
msgid "Payment capture failed to complete with the following message: %s"
msgstr "Przechwycenie płatności zakończone niepowodzeniem z następującym komunikatem: %s"

#: includes/admin/class-wc-rest-payments-orders-controller.php:183
#: includes/admin/class-wc-rest-payments-orders-controller.php:186
#: includes/admin/class-wc-rest-payments-orders-controller.php:308
#: includes/admin/class-wc-rest-payments-orders-controller.php:311
msgid "The payment cannot be captured"
msgstr "Nie można przechwycić płatności"

#: includes/admin/class-wc-rest-payments-charges-controller.php:79
#: includes/admin/class-wc-rest-payments-orders-controller.php:146
#: includes/admin/class-wc-rest-payments-orders-controller.php:286
#: includes/admin/class-wc-rest-payments-orders-controller.php:366
#: includes/admin/class-wc-rest-payments-orders-controller.php:422
#: includes/admin/class-wc-rest-payments-orders-controller.php:518
#: includes/admin/class-wc-rest-payments-payment-intents-controller.php:121
#: includes/admin/class-wc-rest-payments-reader-controller.php:295
msgid "Order not found"
msgstr "Nie znaleziono zamówienia"

#: includes/subscriptions/templates/html-subscriptions-plugin-notice.php:57
#: includes/subscriptions/templates/html-wcpay-deactivate-warning.php:57
#: includes/subscriptions/templates/html-woo-payments-deactivate-warning.php:57
#: client/components/cancel-authorization-button/index.tsx:20
#: client/deposits/instant-payouts/modal.tsx:41
#: client/disputes/new-evidence/index.tsx:578
#: client/onboarding/steps/embedded-kyc.tsx:71 client/onboarding/strings.tsx:85
#: client/order/order-status-change-strategies/index.tsx:85
#: client/overview/modal/reset-account/strings.tsx:28
#: client/overview/modal/update-business-details/strings.tsx:14
#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:181
#: client/payment-details/summary/refund-modal/index.tsx:42
#: client/settings/general-settings/test-mode-confirm-modal.tsx:14
#: client/settings/payment-methods-list/activation-modal.tsx:26
#: client/settings/payment-methods-list/delete-modal.tsx:19
#: client/settings/transactions/manual-capture-control.tsx:55
msgid "Cancel"
msgstr "Anuluj"

#: includes/payment-methods/class-sepa-payment-method.php:58
msgid "Reach 500 million customers and over 20 million businesses across the European Union."
msgstr "Dotrzyj do 500 milionów klientów i ponad 20 milionów firm w Unii Europejskiej."

#: includes/payment-methods/class-sofort-payment-method.php:55
msgid "Accept secure bank transfers from Austria, Belgium, Germany, Italy, Netherlands, and Spain."
msgstr "Akceptuj bezpieczne przelewy bankowe z Austrii, Belgii, Niemiec, Włoch, Holandii i Hiszpanii."

#: includes/payment-methods/class-giropay-payment-method.php:54
msgid "Expand your business with giropay — Germany’s second most popular payment system."
msgstr "Rozwijaj swój biznes dzięki GiroPay ― drugi pod względem popularności system płatności w Niemczech."

#: includes/payment-methods/class-cc-payment-method.php:91
msgid "Let your customers pay with major credit and debit cards without leaving your store."
msgstr "Zapewnij swoim klientom możliwość płacenia za pomocą głównych kart kredytowych i debetowych bez opuszczania Twojego sklepu."

#: client/components/file-upload/index.tsx:42
#: client/disputes/new-evidence/file-upload-control.tsx:26
#: client/settings/express-checkout-settings/file-upload.tsx:101
msgid "Remove file"
msgstr "Usuń plik"

#: client/overview/modal/update-business-details/strings.tsx:7
#: client/overview/task-list/tasks/update-business-details-task.tsx:100
msgid "Finish setup"
msgstr "Zakończ konfigurowanie"

#: includes/class-wc-payments-token-service.php:347
#: includes/class-wc-payments-token-service.php:376
msgid "SEPA IBAN"
msgstr "SEPA IBAN"

#. translators: last 4 digits of IBAN account
#: includes/class-wc-payment-token-wcpay-sepa.php:53
msgid "SEPA IBAN ending in %s"
msgstr "SEPA IBAN kończący się cyframi %s"

#. translators: %1: intent ID
#: includes/class-wc-payments-webhook-processing-service.php:772
msgid "Could not find order via intent ID: %1$s"
msgstr "Nie udało się znaleźć zamówienia za pomocą identyfikatora zamiaru: %1$s"

#: client/transactions/strings.ts:13
msgid "Refund failure"
msgstr "Zwrot zakończony niepowodzeniem"

#: client/payment-details/summary/missing-order-notice/index.tsx:17
#: client/transactions/strings.ts:12
msgid "Refund"
msgstr "Zwrot"

#: client/transactions/uncaptured/index.tsx:47
msgid "Order number"
msgstr "Numer zamówienia"

#: client/payment-details/summary/index.tsx:215
#: client/transactions/list/index.tsx:145
msgid "Fees"
msgstr "Opłaty"

#: client/transactions/list/index.tsx:91
msgid "Date and time"
msgstr "Data i godzina"

#: client/transactions/list/index.tsx:90
msgid "Date / Time"
msgstr "Data/godzina"

#: client/transactions/uncaptured/index.tsx:52
msgid "Risk level"
msgstr "Poziom ryzyka"

#: client/documents/filters/config.ts:52
#: client/transactions/filters/config.ts:122
msgid "{{title}}Date{{/title}} {{rule /}} {{filter /}}"
msgstr "{{title}}Data{{/title}} {{rule /}} {{filter /}}"

#: client/documents/filters/config.ts:29
#: client/transactions/filters/config.ts:94
msgid "Advanced filters"
msgstr "Filtry zaawansowane"

#: client/documents/filters/config.ts:19
#: client/transactions/filters/config.ts:77
msgid "Show"
msgstr "Pokaż"

#: client/disputes/filters/config.ts:45
#: client/transactions/filters/config.ts:69
msgid "All currencies"
msgstr "Wszystkie waluty"

#: client/transactions/filters/config.ts:204
msgid "{{title}}Type{{/title}} {{rule /}} {{filter /}}"
msgstr "{{title}}Typ{{/title}} {{rule /}} {{filter /}}"

#: client/documents/filters/config.ts:67
#: client/transactions/filters/config.ts:137
msgid "Between"
msgstr "Między"

#: client/documents/filters/config.ts:63
#: client/transactions/filters/config.ts:133
msgid "After"
msgstr "Po"

#: client/documents/filters/config.ts:59
#: client/transactions/filters/config.ts:129
msgid "Before"
msgstr "Przed"

#: client/onboarding/strings.tsx:84 client/tos/modal/index.tsx:67
msgid "Back"
msgstr "Powrót"

#: client/transactions/list/index.tsx:152
msgid "Net"
msgstr "Netto"

#: client/payment-details/transaction-breakdown/utils.ts:35
msgid "Fee"
msgstr "Opłata"

#: client/payment-details/payment-method/base-payment-method-details/index.tsx:45
#: client/vat/form/tasks/company-data-task.tsx:63
msgid "Address"
msgstr "Adres"

#: client/payment-details/payment-method/base-payment-method-details/index.tsx:44
msgid "Owner email"
msgstr "Adres e-mail właściciela"

#: client/payment-details/payment-method/base-payment-method-details/index.tsx:43
msgid "Owner"
msgstr "Właściciel"

#: client/payment-details/payment-method/base-payment-method-details/index.tsx:41
msgid "ID"
msgstr "ID"

#: client/payment-details/summary/index.tsx:133
#: client/transactions/filters/config.ts:174
msgid "Payment method"
msgstr "Metoda płatności"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:132
#: client/payment-details/summary/index.tsx:122
#: client/transactions/uncaptured/index.tsx:46
msgid "Order"
msgstr "Zamówienie"

#: client/transactions/list/index.tsx:190
#: client/transactions/uncaptured/index.tsx:73
msgid "Country"
msgstr "Kraj"

#: client/transactions/list/index.tsx:183
#: client/transactions/uncaptured/index.tsx:66
msgid "Email"
msgstr "E-mail"

#: client/payment-details/summary/index.tsx:118
#: client/transactions/blocked/columns.tsx:39
#: client/transactions/list/index.tsx:177
msgid "Customer"
msgstr "Klient"

#: client/disputes/new-evidence/index.tsx:457
#: client/payment-details/dispute-details/dispute-summary-row.tsx:35
msgid "Reason"
msgstr "Przyczyna"

#: client/settings/fraud-protection/advanced-settings/index.tsx:175
msgid "There are unsaved changes on this page. Are you sure you want to leave and discard the unsaved changes?"
msgstr "Na tej stronie są niezapisane zmiany. Czy na pewno chcesz opuścić stronę i odrzucić niezapisane zmiany?"

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:128
msgid "Submit evidence"
msgstr "Prześlij dowody"

#: client/components/file-upload/index.tsx:40
#: client/disputes/new-evidence/file-upload-control.tsx:41
msgid "Upload file"
msgstr "Prześlij plik"

#: client/disputes/new-evidence/recommended-document-fields.ts:41
msgid "Customer communication"
msgstr "Komunikacja z klientem"

#: client/disputes/new-evidence/recommended-document-fields.ts:240
msgid "Proof of shipping"
msgstr "Dowód wysyłki"

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:71
msgid "Accept dispute"
msgstr "Zaakceptuj spór"

#: client/payment-details/dispute-details/dispute-resolution-footer.tsx:192
msgid "View submitted evidence"
msgstr "Wyświetl przesłane dowody"

#: client/deposits/list/index.tsx:81
msgid "Bank account"
msgstr "Konto bankowe"

#: client/card-readers/list/index.tsx:31
#: client/components/deposits-overview/recent-deposits-list.tsx:38
#: client/disputes/filters/config.ts:121 client/disputes/index.tsx:86
#: client/transactions/blocked/columns.tsx:46
msgid "Status"
msgstr "Status"

#: includes/class-wc-payments-order-success-page.php:140
#: includes/class-wc-payments-order-success-page.php:531
#: client/capital/index.tsx:42
#: client/components/deposits-overview/recent-deposits-list.tsx:39
#: client/deposits/list/index.tsx:65 client/disputes/index.tsx:72
#: client/transactions/blocked/columns.tsx:32
#: client/transactions/list/index.tsx:137
#: client/transactions/uncaptured/index.tsx:59
msgid "Amount"
msgstr "Kwota"

#: client/documents/filters/config.ts:76 client/documents/list/index.tsx:37
#: client/transactions/filters/config.ts:199
#: client/transactions/list/index.tsx:102
msgid "Type"
msgstr "Typ"

#: client/deposits/list/index.tsx:47 client/documents/filters/config.ts:47
#: client/documents/list/index.tsx:25
#: client/payment-details/summary/index.tsx:110
#: client/transactions/filters/config.ts:117
msgid "Date"
msgstr "Data"

#: client/merchant-feedback-prompt/positive-modal.tsx:53
msgid "Close"
msgstr "Zamknij"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:230
#: client/transactions/strings.ts:34
msgid "Normal"
msgstr "Normalne"

#: client/transactions/strings.ts:36
msgid "Highest"
msgstr "Najwyższe"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:231
#: client/transactions/strings.ts:35
msgid "Elevated"
msgstr "Podniesione"

#: client/components/transaction-status-pill/mappings.ts:17
msgid "Payment blocked"
msgstr "Płatność zablokowana"

#: client/payment-details/summary/index.tsx:211
msgid "Refunded"
msgstr "Zwrot dokonany"

#: client/payment-details/summary/index.tsx:283
msgid "Partial refund"
msgstr "Częściowy zwrot"

#: client/components/payment-status-chip/mappings.ts:17
msgid "Disputed: %s"
msgstr "Sporne: %s"

#: client/components/dispute-status-chip/mappings.ts:37
#: client/disputes/strings.ts:179
msgid "Lost"
msgstr "Przegrana"

#: client/components/dispute-status-chip/mappings.ts:33
#: client/disputes/strings.ts:178
msgid "Won"
msgstr "Wygrana"

#: client/components/dispute-status-chip/mappings.ts:29
#: client/disputes/strings.ts:177
msgid "Charge refunded"
msgstr "Opłata zwrócona"

#: client/components/dispute-status-chip/mappings.ts:25
#: client/disputes/strings.ts:176
msgid "Under review"
msgstr "Sprawdzanie"

#: client/disputes/filters/config.ts:66
msgid "Needs response"
msgstr "Wymagana odpowiedź"

#: client/components/dispute-status-chip/mappings.ts:17
#: client/disputes/strings.ts:174
msgid "Inquiry: Closed"
msgstr "Zapytanie: zamknięte"

#: client/components/dispute-status-chip/mappings.ts:13
#: client/disputes/strings.ts:173
msgid "Inquiry: Under review"
msgstr "Zapytanie: sprawdzanie"

#: includes/class-wc-payments-status.php:135
#: includes/class-wc-payments-status.php:140
#: includes/class-wc-payments-status.php:167
#: includes/class-wc-payments-status.php:186
#: includes/class-wc-payments-status.php:241
#: includes/class-wc-payments-status.php:249
#: includes/class-wc-payments-status.php:256
#: client/components/deposits-status/index.tsx:37
#: client/components/payments-status/index.tsx:21
msgid "Disabled"
msgstr "Wyłączono"

#: includes/class-wc-payments-status.php:135
#: includes/class-wc-payments-status.php:140
#: includes/class-wc-payments-status.php:167
#: includes/class-wc-payments-status.php:186
#: includes/class-wc-payments-status.php:241
#: includes/class-wc-payments-status.php:249
#: includes/class-wc-payments-status.php:256
#: client/components/payments-status/index.tsx:16
msgid "Enabled"
msgstr "Włączono"

#: client/settings/payment-methods-list/use-payment-method-availability.tsx:90
msgid "Rejected"
msgstr "Odrzucono"

#: client/components/deposits-status/index.tsx:25
#: client/deposits/details/index.tsx:85
msgid "Unknown"
msgstr "Nieznane"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:149
#: client/disputes/new-evidence/index.tsx:464
#: client/onboarding/steps/embedded-kyc.tsx:65
#: client/payment-details/dispute-details/dispute-steps.tsx:158
#: client/payment-details/dispute-details/dispute-summary-row.tsx:42
#: client/utils/account-fees.tsx:131
msgid "Learn more"
msgstr "Dowiedz się więcej"

#. translators: This is an error API response.
#: includes/wc-payment-api/class-wc-payments-api-client.php:2709
msgctxt "API error message to throw as Exception"
msgid "Error: %1$s"
msgstr "Błąd: %1$s"

#. translators: %1: original error message.
#: includes/wc-payment-api/class-wc-payments-api-client.php:536
#: includes/wc-payment-api/class-wc-payments-http.php:121
msgid "Http request failed. Reason: %1$s"
msgstr "Żądanie http zakończone niepowodzeniem. Przyczyna: %1$s"

#: includes/wc-payment-api/class-wc-payments-http.php:62
msgid "Site is not connected to WordPress.com"
msgstr "Witryna nie jest połączona z WordPress.com"

#: includes/wc-payment-api/class-wc-payments-api-client.php:2704
msgid "Server error. Please try again."
msgstr "Błąd serwera. Spróbuj ponownie."

#: includes/wc-payment-api/class-wc-payments-api-client.php:2636
msgid "Unable to decode response from WooCommerce Payments API"
msgstr "Nie można odkodować odpowiedzi od API Płatności WooCommerce."

#: includes/wc-payment-api/class-wc-payments-api-client.php:2520
msgid "Unable to encode body for request to WooCommerce Payments API."
msgstr "Nie można zakodować treści żądania do API Płatności WooCommerce."

#: includes/wc-payment-api/class-wc-payments-api-client.php:1278
msgid "Customer ID is required"
msgstr "Identyfikator klienta jest wymagany."

#: includes/wc-payment-api/class-wc-payments-api-client.php:793
msgid "Max file size exceeded."
msgstr "Przekroczony maksymalny rozmiar pliku."

#: client/disputes/new-evidence/recommended-document-fields.ts:207
msgid "Store refund policy"
msgstr "Zasady zwrotów sklepu"

#: includes/notes/class-wc-payments-notes-qualitative-feedback.php:81
msgid "Share feedback"
msgstr "Podziel się opinią"

#: includes/notes/class-wc-payments-notes-qualitative-feedback.php:74
msgid "Share your feedback in this 2 minute survey about how we can make the process of accepting payments more useful for your store."
msgstr "Podziel się swoją opinią. Wypełnij tę 2-minutową ankietę i pomóż nam usprawnić proces akceptowania płatności w Twoim sklepie."

#: includes/notes/class-wc-payments-notes-set-https-for-checkout.php:59
msgid "Read more"
msgstr "więcej"

#. translators: 1: payment method likely credit card, 2: last 4 digit.
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:680
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:699
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:740
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:748
msgid "%1$s ending in %2$s"
msgstr "%1$s kończąca się na %2$s"

#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:592
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:646
msgid "Please select a payment method"
msgstr "Wybierz metodę płatności"

#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:523
msgid "The saved payment method selected does not belong to this order's customer."
msgstr "Wybrana zapisana metoda płatności nie należy do klienta z tego zamówienia."

#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:516
msgid "The saved payment method selected is invalid or does not exist."
msgstr "Wybrana zapisana metoda płatności jest nieprawidłowa lub nie istnieje."

#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:507
msgid "A customer saved payment method was not selected for this order."
msgstr "Dla tego zamówienia nie wybrano metody płatności zapisanej przez klienta"

#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:436
msgid "Saved payment method"
msgstr "Zapisana metoda płatności"

#: includes/class-wc-payments-dependency-service.php:305
msgid "Update WordPress"
msgstr "Zaktualizuj WordPress"

#: includes/class-wc-payments-dependency-service.php:289
msgid "Use the bundled version of WooCommerce Admin"
msgstr "Użyj wersji aplikacji Admin z pakietu z WooCommerce"

#: includes/class-wc-payments-dependency-service.php:285
msgid "There is a newer version of WooCommerce Admin bundled with WooCommerce."
msgstr "W pakiecie z WooCommerce jest dostępna nowsza wersja aplikacji WooCommerce Admin."

#: includes/class-wc-payments-dependency-service.php:219
msgid "Activate WooCommerce"
msgstr "Aktywuj WooCommerce"

#: includes/class-wc-payments-dependency-service.php:215
msgid "Install WooCommerce"
msgstr "Zainstaluj WooCommerce"

#: includes/class-wc-payments-utils.php:320
#: includes/wc-payment-api/class-wc-payments-api-client.php:395
#: client/transactions/list/index.tsx:165
msgid "Subscription #"
msgstr "Subskrypcja nr"

#: includes/class-wc-payments-utils.php:310
#: includes/wc-payment-api/class-wc-payments-api-client.php:397
#: client/disputes/index.tsx:107 client/transactions/list/index.tsx:159
msgid "Order #"
msgstr "Zamówienie nr"

#. translators: %1: the authorized amount, %2: transaction ID of the payment
#: includes/class-wc-payments-order-service.php:1775
msgid "Payment authorization has <strong>expired</strong> (<a>%1$s</a>)."
msgstr "Autoryzacja płatności <strong>wygasła</strong> (<a>%1$s</a>)."

#: includes/class-wc-payments-captured-event-note.php:391
#: includes/express-checkout/class-wc-payments-express-checkout-button-helper.php:129
#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:160
msgid "Discount"
msgstr "Zniżka"

#: includes/express-checkout/class-wc-payments-express-checkout-button-display-handler.php:114
msgid "OR"
msgstr "LUB"

#: includes/express-checkout/class-wc-payments-express-checkout-button-helper.php:614
#: client/deposits/strings.ts:18
msgid "Pending"
msgstr "Oczekujące"

#: includes/express-checkout/class-wc-payments-express-checkout-button-helper.php:121
#: includes/express-checkout/class-wc-payments-express-checkout-button-helper.php:607
msgid "Shipping"
msgstr "Wysyłka"

#: includes/class-wc-payments-captured-event-note.php:572
#: includes/express-checkout/class-wc-payments-express-checkout-button-helper.php:112
#: includes/express-checkout/class-wc-payments-express-checkout-button-helper.php:599
#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:185
#: client/payment-details/utils/tax-descriptions.ts:52
msgid "Tax"
msgstr "Podatek"

#. translators: %1$s Name.
#: includes/class-wc-payments-customer-service.php:355
msgid "Name: %1$s, Guest"
msgstr "Nazwa: %1$s, gość"

#. translators: %1$s Name, %2$s Username.
#: includes/class-wc-payments-customer-service.php:351
msgid "Name: %1$s, Username: %2$s"
msgstr "Nazwa: %1$s, nazwa użytkownika: %2$s"

#: includes/class-wc-payments-apple-pay-registration.php:256
msgid "Apple Pay domain verification failed with the following error:"
msgstr "Weryfikacja domeny Apple Pay zakończona niepowodzeniem z następującym błędem:"

#: includes/class-wc-payments-apple-pay-registration.php:255
msgid "Apple Pay domain verification failed."
msgstr "Weryfikacja domeny Apple Pay nie udała się."

#: includes/class-wc-payments-apple-pay-registration.php:157
msgid "Your domain has been verified with Apple Pay!"
msgstr "Twoja domena została zweryfikowana w Apple Pay!"

#: includes/class-wc-payments-account.php:2181
msgid "There was a problem processing your account data. Please try again."
msgstr "Wystąpił problem podczas przetwarzania danych Twojego konta. Spróbuj ponownie."

#. translators: %s: error message.
#: includes/class-wc-payments-account.php:1560
msgid "There was a problem connecting your store to WordPress.com: \"%s\""
msgstr "Podczas łączenia Twojego sklepu z WordPress.com wystąpił problem: „%s”"

#. translators: %s: WooPayments
#: includes/class-wc-payments-account.php:1445
msgid "Connection to WordPress.com failed. Please connect to WordPress.com to start using %s."
msgstr "Nie udało się połączyć z WordPress.com. Połącz się z WordPress.com, aby zacząć korzystać z usługi %s."

#: includes/class-wc-payments-account.php:239
msgid "Failed to detect connection status"
msgstr "Nie udało się wykryć statusu połączenia"

#: includes/class-wc-payments-status.php:72
msgid "Clear"
msgstr "Wyczyść"

#: includes/class-wc-payment-gateway-wcpay.php:3852
msgid "We're not able to add this payment method. Please refresh the page and try again."
msgstr "Nie mogliśmy dodać tej metody płatności. Odśwież stronę i spróbuj ponownie."

#: includes/class-wc-payment-gateway-wcpay.php:3693
msgid "Failed to add the provided payment method. Please try again later"
msgstr "Nie udało się dodać podanej metody płatności. Spróbuj ponownie później"

#: includes/class-wc-payment-gateway-wcpay.php:3682
msgid "We're not able to add this payment method. Please try again later"
msgstr "Nie mogliśmy dodać tej metody płatności. Spróbuj ponownie później"

#. translators: %1: transaction ID of the payment or a translated string
#. indicating an unknown ID.
#: includes/class-wc-payment-gateway-wcpay.php:3622
msgid "A payment with ID <code>%1$s</code> was used in an attempt to pay for this order. This payment intent ID does not match any payments for this order, so it was ignored and the order was not updated."
msgstr "Płatność o identyfikatorze <code>%1$s</code> została użyta podczas próby zapłaty za to zamówienie. Identyfikator zamiaru płatności nie pasuje do żadnej płatności w tym zamówieniu, dlatego został zignorowany, a zamówienie nie zostało zaktualizowane."

#. translators: This will be used to indicate an unknown value for an ID.
#: includes/class-wc-payment-gateway-wcpay.php:3513
msgid "unknown"
msgstr "nieznane"

#: includes/class-payment-information.php:281
#: includes/class-wc-payment-gateway-wcpay.php:1633
#: includes/class-wc-payment-gateway-wcpay.php:1975
#: includes/class-wc-payment-gateway-wcpay.php:3504
#: includes/class-wc-payment-gateway-wcpay.php:3517
#: includes/class-wc-payment-gateway-wcpay.php:3529
msgid "We're not able to process this payment. Please try again later."
msgstr "Nie możemy przetworzyć tej płatności. Spróbuj ponownie później."

#: includes/class-wc-payment-gateway-wcpay.php:3447
#: includes/class-wc-payments-order-service.php:1371
msgid "Canceling authorization <strong>failed</strong> to complete."
msgstr "Anulowanie autoryzacji <strong>zakończone niepowodzeniem</strong>."

#. translators: %1: error message
#: includes/class-wc-payment-gateway-wcpay.php:3432
msgid "Canceling authorization <strong>failed</strong> to complete with the following message: <code>%1$s</code>."
msgstr "Anulowanie autoryzacji <strong>zakończone niepowodzeniem</strong> z następującym komunikatem: <code>%1$s</code>."

#. translators: %1: transaction ID of the payment
#: includes/class-wc-payments-order-service.php:1799
msgid "Payment authorization was successfully <strong>cancelled</strong> (<a>%1$s</a>)."
msgstr "Autoryzacja płatności została pomyślnie <strong>anulowana</strong> (<a>%1$s</a>)."

#: includes/class-wc-payment-gateway-wcpay.php:3273
msgid "Cancel authorization"
msgstr "Anuluj autoryzację"

#: includes/class-wc-payment-gateway-wcpay.php:3272
msgid "Capture charge"
msgstr "Przechwyć opłatę"

#: includes/class-wc-payment-gateway-wcpay.php:3240
msgid "Customer bank statement is invalid. Statement should be between 5 and 22 characters long, contain at least single Latin character and does not contain special characters: ' \" * &lt; &gt;"
msgstr "Wyciąg bankowy klienta jest nieprawidłowy. Deskryptor na wyciągu powinien mieć od 5 do 22 znaków, zawierać co najmniej jedną literę łacińską i nie zawierać znaków specjalnych: ' \" * &lt; &gt;"

#: client/payment-details/transaction-breakdown/utils.ts:12
#: client/utils/account-fees.tsx:102
msgid "Base fee"
msgstr "Opłata podstawowa"

#. translators: %1: the successfully charged amount, %2: error message
#: includes/class-wc-payment-gateway-wcpay.php:2354
msgid "A refund of %1$s failed to complete: %2$s"
msgstr "Zwrot %1$s zakończony niepowodzeniem: %2$s"

#. translators: an error message which will appear if a user tries to refund an
#. order which is has been authorized but not yet charged.
#: includes/class-wc-payment-gateway-wcpay.php:2286
msgid "This payment is not captured yet. To cancel this order, please go to 'Order Actions' > 'Cancel authorization'. To proceed with a refund, please go to 'Order Actions' > 'Capture charge' to charge the payment card, and then trigger a refund via the 'Refund' button."
msgstr "Płatność nie została jeszcze przechwycona. Aby anulować to zamówienie, wybierz kolejno opcje „Działania związane z zamówieniem” > „Anuluj autoryzację”. Aby kontynuować zwrot, wybierz kolejno opcje „Działania związane z zamówieniem” > „Przechwyć opłatę”, aby obciążyć kartę płatniczą, a następnie uruchom zwrot za pomocą przycisku „Zwrot”."

#. translators: %1: the last 4 digit of the credit card
#: includes/class-wc-payment-gateway-wcpay.php:1472
msgid "Payment method is changed to: <strong>Credit card ending in %1$s</strong>."
msgstr "Metoda płatności została zmieniona na: <strong>Karta kredytowa kończąca się na %1$s</strong>."

#. translators: %1: the failed payment amount, %2: error message
#: includes/class-wc-payment-gateway-wcpay.php:1178
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:388
msgid "A payment of %1$s <strong>failed</strong> to complete with the following message: <code>%2$s</code>."
msgstr "Płatność zakończona %1$s <strong>niepowodzeniem</strong> z następującym komunikatem: <code>%2$s</code>."

#: includes/class-wc-payment-gateway-wcpay.php:1112
#: includes/class-wc-payment-gateway-wcpay.php:3495
msgid "We're not able to process this payment. Please refresh the page and try again."
msgstr "Nie możemy przetworzyć tej płatności. Odśwież stronę i spróbuj ponownie."

#: includes/class-wc-payments-utils.php:668
msgid "There was an error while processing this request. If you continue to see this notice, please contact the admin."
msgstr "Podczas przetwarzania tego żądania wystąpił błąd. Jeśli ta wiadomość będzie się cały czas pojawiać, skontaktuj się z administratorem."

#: includes/class-wc-payment-gateway-wcpay.php:1056
msgid "Save payment information to my account for future purchases."
msgstr "Zapisz informacje o płatności na moim koncie, aby je wykorzystać podczas przyszłych zakupów."

#: includes/class-wc-payments-checkout.php:194
msgid "There was a problem processing the payment. Please check your email inbox and refresh the page to try again."
msgstr "Wystąpił problem podczas przetwarzania płatności. Sprawdź swoją skrzynkę odbiorczą e-mail i odśwież stronę, aby spróbować ponownie."

#: includes/class-wc-payment-gateway-wcpay.php:522
#: includes/class-wc-payment-gateway-wcpay.php:560
msgid "Select pages"
msgstr "Wybierz strony"

#: includes/class-wc-payment-gateway-wcpay.php:519
#: includes/class-wc-payment-gateway-wcpay.php:557
msgid "Checkout"
msgstr "Realizacja zamówienia"

#: includes/class-wc-payment-gateway-wcpay.php:518
#: includes/class-wc-payment-gateway-wcpay.php:556
msgid "Cart"
msgstr "Koszyk"

#: includes/class-wc-payment-gateway-wcpay.php:517
#: includes/class-wc-payment-gateway-wcpay.php:555
msgid "Product"
msgstr "Produkt"

#: includes/class-wc-payment-gateway-wcpay.php:508
#: includes/class-wc-payment-gateway-wcpay.php:546
msgid "Select where you would like to display the button."
msgstr "Wybierz, gdzie chcesz wyświetlić przycisk."

#: includes/class-wc-payment-gateway-wcpay.php:506
msgid "Button locations"
msgstr "Lokalizacje przycisku"

#: includes/class-wc-payment-gateway-wcpay.php:502
msgid "Buy now"
msgstr "Kup teraz"

#: includes/class-wc-payment-gateway-wcpay.php:501
msgid "Enter the custom text you would like the button to have."
msgstr "Wprowadź tekst niestandardowy, który chcesz mieć na przycisku."

#: includes/class-wc-payment-gateway-wcpay.php:499
msgid "Custom button label"
msgstr "Niestandardowa etykieta przycisku"

#: includes/class-wc-payment-gateway-wcpay.php:494
msgid "Enter the height you would like the button to be in pixels. Width will always be 100%."
msgstr "Wprowadź wysokość w pikselach, jaką ma mieć przycisk. Szerokość to zawsze 100%."

#: includes/class-wc-payment-gateway-wcpay.php:492
msgid "Button height"
msgstr "Wysokość przycisku"

#: includes/class-wc-payment-gateway-wcpay.php:488
msgid "Light-Outline"
msgstr "Jasny kontur"

#: includes/class-wc-payment-gateway-wcpay.php:482
msgid "Select the button theme you would like to show."
msgstr "Wybierz motyw przycisku, który chcesz wyświetlić."

#: includes/class-wc-payment-gateway-wcpay.php:480
msgid "Button theme"
msgstr "Motyw przycisku"

#: includes/class-wc-payment-gateway-wcpay.php:475
msgid "Donate"
msgstr "Przekaż donację"

#: includes/class-wc-payment-gateway-wcpay.php:474
msgid "Buy"
msgstr "Kup"

#: includes/class-wc-payment-gateway-wcpay.php:469
msgid "Select the button type you would like to show."
msgstr "Wybierz typ przycisku, który chcesz wyświetlić."

#: includes/class-wc-payment-gateway-wcpay.php:467
msgid "Button type"
msgstr "Typ przycisku"

#: includes/class-wc-payment-gateway-wcpay.php:462
msgid "If enabled, users will be able to pay using Apple Pay, Google Pay or the Payment Request API if supported by the browser."
msgstr "Włączenie tej opcji umożliwi użytkownikom płacenie za pomocą Apple Pay, Google Pay lub API żądań zapłaty, jeśli przeglądarka będzie obsługiwać taką możliwość."

#. translators: 1) br tag 2) Stripe anchor tag 3) Apple anchor tag
#: includes/class-wc-payment-gateway-wcpay.php:456
msgid "Enable payment request buttons (Apple Pay, Google Pay, and more). %1$sBy using Apple Pay, you agree to %2$s and %3$s's Terms of Service."
msgstr "Włącz przyciski żądania zapłaty (Apple Pay, Google Pay i inne). %1$sUżywając Apple Pay, wyrażasz zgodę na warunki świadczenia usługi %2$s oraz %3$s."

#: includes/class-wc-payment-gateway-wcpay.php:448
msgid "Payment request buttons"
msgstr "Przyciski żądania zapłaty"

#: includes/class-wc-payment-gateway-wcpay.php:442
msgid "When enabled debug notes will be added to the log."
msgstr "Włączenie tej opcji spowoduje, że do dziennika zostaną dodane notatki z debugowania."

#: includes/class-wc-payment-gateway-wcpay.php:441
msgid "Debug log"
msgstr "Dziennik debugowania"

#: includes/class-wc-payment-gateway-wcpay.php:436
msgid "Simulate transactions using test card numbers."
msgstr "Przeprowadź symulację transakcji za pomocą testowych numerów kart."

#: client/settings/general-settings/test-mode-confirm-modal.tsx:13
msgid "Enable test mode"
msgstr "Włącz tryb testowy"

#: includes/class-wc-payment-gateway-wcpay.php:428
msgid "If enabled, users will be able to pay with a saved card during checkout. Card details are saved on our platform, not on your store."
msgstr "Po włączeniu tej opcji użytkownicy będą mogli płacić za pomocą karty zapisanej podczas realizacji zamówienia. Dane karty są zapisywane na naszej platformie, a nie w Twoim sklepie."

#: includes/class-wc-payment-gateway-wcpay.php:426
msgid "Enable payment via saved cards"
msgstr "Włącz płatność za pomocą zapisanych kart"

#: includes/class-wc-payment-gateway-wcpay.php:425
msgid "Saved cards"
msgstr "Zapisane karty"

#: includes/class-wc-payment-gateway-wcpay.php:421
msgid "Charge must be captured within 7 days of authorization, otherwise the authorization and order will be canceled."
msgstr "Opłata musi zostać przechwycona w ciągu 7 dni od autoryzacji, w przeciwnym razie autoryzacja i zamówienie zostaną anulowane."

#: includes/class-wc-payment-gateway-wcpay.php:419
msgid "Issue an authorization on checkout, and capture later."
msgstr "Dokonaj autoryzacji podczas realizacji zamówienia i przechwyć później."

#: includes/class-wc-payment-gateway-wcpay.php:418
msgid "Manual capture"
msgstr "Przechwytywanie ręczne"

#: includes/class-wc-payment-gateway-wcpay.php:413
msgid "Edit the way your store name appears on your customers’ bank statements (read more about requirements <a>here</a>)."
msgstr "Edytuj sposób wyświetlania nazwy swojego sklepu na wyciągach bankowych Twoich klientów (przeczytaj więcej na temat wymagań <a>tutaj</a>)."

#. translators: %1: ID being fetched
#: includes/class-wc-payments-webhook-processing-service.php:691
msgid "%1$s not found in array"
msgstr "W tablicy nie znaleziono obiektu o ID %1$s"

#. translators: %1: charge ID
#: includes/class-wc-payments-webhook-processing-service.php:276
#: includes/class-wc-payments-webhook-processing-service.php:342
#: includes/class-wc-payments-webhook-processing-service.php:556
#: includes/class-wc-payments-webhook-processing-service.php:587
#: includes/class-wc-payments-webhook-processing-service.php:618
#: includes/class-wc-payments-webhook-processing-service.php:859
msgid "Could not find order via charge ID: %1$s"
msgstr "Nie udało się znaleźć zamówienia za pomocą identyfikatora opłaty: %1$s"

#: includes/admin/class-wc-rest-payments-tos-controller.php:109
msgid "ToS accept parameter is missing"
msgstr "Brakuje parametru akceptacji Warunków świadczenia usług"

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:129
msgid "Challenge dispute"
msgstr "Zakwestionuj spór"

#: client/payment-details/dispute-details/dispute-awaiting-response-details.tsx:135
msgid "Dispute details"
msgstr "Szczegóły sporu"

#: includes/admin/class-wc-payments-admin-settings.php:257
#: includes/admin/class-wc-payments-admin.php:492
msgid "Settings"
msgstr "Ustawienia"

#: client/transactions/index.tsx:56 client/transactions/list/index.tsx:391
msgid "Transactions"
msgstr "Transakcje"

#: includes/class-wc-payments-address-provider.php:39
msgid "WooCommerce Payments"
msgstr "Płatności WooCommerce"

