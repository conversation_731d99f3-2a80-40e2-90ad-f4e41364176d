# Translation of Plugins - Really Simple SSL - Stable (latest release) in Polish
# This file is distributed under the same license as the Plugins - Really Simple SSL - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-04-19 11:27:37+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n == 1) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pl\n"
"Project-Id-Version: Plugins - Really Simple SSL - Stable (latest release)\n"

#: lets-encrypt/config/notices.php:142
msgid "You are using the Really Simple SSL Shell Exec add on, but your current version needs to be updated."
msgstr "Używany jest dodatek Really Simple SSL Shell Exec, ale aktualna wersja wymaga aktualizacji."

#: settings/config/config.php:501
msgid "(recommended)"
msgstr "(zalecan)"

#: class-admin.php:2034
msgid "SSL is now activated. Follow the three steps in this article to check if your website is secure."
msgstr "Obsługa SSL została włączona. Wykonaj trzy kroki opisane w tym artykule, aby sprawdzić, czy witryna jest bezpieczna."

#: security/wordpress/file-editing.php:28
msgid "The DISALLOW_FILE_EDIT constant is defined and set to false. You can remove it from your wp-config.php."
msgstr "Stała DISALLOW_FILE_EDIT jest zdefiniowana i ustawiona na fałsz. Możesz ją usunąć z pliku wp-config.php."

#: lets-encrypt/integrations/cpanel/cpanel.php:111
msgid "Login credentials incorrect. Please check your login credentials for cPanel."
msgstr "Dane logowania są nieprawidłowe. Sprawdź swoje dane logowania do cPanel."

#: lets-encrypt/class-letsencrypt-handler.php:812
msgid "Failed retrieving account."
msgstr "Nie powiodło się pobranie danych konta."

#: security/notices.php:176
msgid "Rename admin user enabled: Please choose a new username of at least 3 characters, which is not in use yet."
msgstr "Zmiana nazwy użytkownika administratora włączona: wybierz nową nazwę użytkownika składającą się z co najmniej 3 znaków, która nie jest jeszcze używana."

#: upgrade/upgrade-to-pro.php:450
msgid "Could not rename folder!"
msgstr "Nie udało się zmienić nazwy katalogu!"

#: settings/config/config.php:810
msgid "Choose new username to replace 'admin'"
msgstr "Wybierz nową nazwę użytkownika, aby zastąpić nazwę „admin”"

#: lets-encrypt/config/fields.php:111 onboarding/class-onboarding.php:283
msgid "Terms & Conditions."
msgstr "Zasady i warunki."

#: settings/config/menu.php:96
msgid "Recommended Security Headers"
msgstr "Zalecane nagłówki zabezpieczeń"

#: settings/config/config.php:179 settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/Settings/Support.js:43
msgid "Send"
msgstr "Wyślij"

#: class-admin.php:549 settings/build/43.5782841cd77291fd781f.js:1
#: settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/Dashboard/TaskElement.js:52
#: settings/src/Dashboard/TaskElement.js:53
#: settings/src/Settings/MixedContentScan/MixedContentScan.js:80
msgid "View"
msgstr "Zobacz"

#: settings/config/config.php:297 settings/config/config.php:342
#: settings/config/config.php:388 settings/config/menu.php:489
#: settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/Settings/LearningMode/LearningMode.js:141
msgid "Blocked"
msgstr "Zablokowano"

#: settings/config/menu.php:468 settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/Settings/EventLog/EventLogDataTable.js:214
#: settings/src/Settings/GeoBlockList/GeoDatatable.js:452
#: settings/src/Settings/GeoBlockList/WhiteListDatatable.js:386
#: settings/src/Settings/LearningMode/LearningMode.js:139
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:369
#: settings/src/Settings/LimitLoginAttempts/IpAddressDatatable.js:372
msgid "All"
msgstr "Wszystko"

#: settings/config/config.php:500
msgid "disabled"
msgstr "wyłączono"

#: settings/config/config.php:318 settings/config/config.php:364
#: settings/config/config.php:424 settings/config/config.php:1131
#: settings/build/43.5782841cd77291fd781f.js:1
#: settings/src/Dashboard/SslLabs/SslLabsHeader.js:5
msgid "Status"
msgstr "Stan"

#: settings/config/config.php:1389
msgid "Source"
msgstr "Źródło"

#: settings/config/config.php:1211 settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/Settings/LearningMode/LearningMode.js:290
#: settings/src/Settings/LearningMode/LearningMode.js:296
#: settings/src/Settings/PermissionsPolicy.js:200
msgid "Disable"
msgstr "Wyłącz"

#: settings/config/config.php:1183 settings/config/config.php:1377
msgid "Location"
msgstr "Położenie"

#: settings/config/config.php:1177 settings/config/config.php:1627
msgid "Description"
msgstr "Opis"

#: settings/config/config.php:1170
msgid "Type"
msgstr "Typ"

#: settings/config/config.php:628
msgid "One year"
msgstr "Rok"

#: settings/config/config.php:627
msgid "One day (for testing only)"
msgstr "Dzień (tylko do testu)"

#: settings/config/config.php:611
msgid "Include subdomains"
msgstr "Włączaniąc poddomeny"

#: settings/config/config.php:1401 settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/Settings/LearningMode/Delete.js:12
#: settings/src/Settings/LimitLoginAttempts/IpAddressDatatable.js:254
#: settings/src/Settings/LimitLoginAttempts/IpAddressDatatable.js:351
#: settings/src/Settings/LimitLoginAttempts/UserDatatable.js:186
#: settings/src/Settings/LimitLoginAttempts/UserDatatable.js:274
msgid "Delete"
msgstr "Usuń"

#: settings/config/config.php:1211 settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/Settings/GeoBlockList/GeoDatatable.js:229
#: settings/src/Settings/GeoBlockList/GeoDatatable.js:246
#: settings/src/Settings/GeoBlockList/GeoDatatable.js:424
#: settings/src/Settings/GeoBlockList/GeoDatatable.js:435
#: settings/src/Settings/LearningMode/ChangeStatus.js:7
#: settings/src/Settings/LearningMode/LearningMode.js:240
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:231
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:242
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:251
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:325
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:336
#: settings/src/Settings/LimitLoginAttempts/CountryDatatable.js:343
msgid "Allow"
msgstr "Pozwól"

#: settings/config/config.php:1395 settings/config/config.php:1615
msgid "Action"
msgstr "Działanie"

#: settings/config/config.php:1007
msgid "Method"
msgstr "Metoda"

#: settings/config/config.php:1383
msgid "Directive"
msgstr "Dyrektywa"

#: settings/config/config.php:1344
msgid "Add additional domains which can embed your website, if needed. Comma seperated."
msgstr "W razie potrzeby dodaj domeny, które mogą osadzić twoją witrynę. Oddzielone przecinkami."

#: settings/config/config.php:1335
msgid "Allow your domain to be embedded"
msgstr "Pozwól osadzić swoją domenę"

#: settings/config/config.php:1331
msgid "Disable (Default)"
msgstr "Wyłącz (domyślnie)"

#: settings/config/config.php:1316
msgid "The content security policy has many options, so we always recommend starting in ‘learning mode’ to see what files and scripts are loaded."
msgstr "Polityka bezpieczeństwa treści ma wiele opcji, dlatego zawsze zalecamy uruchamianie w „trybie uczenia się”, aby zobaczyć, co jest ładowane."

#: settings/config/config.php:1315
msgid "About the Content Security Policy"
msgstr "Informacje o polityce bezpieczeństwa treści"

#: settings/config/config.php:1309
msgid "Serve encrypted and authenticated responses"
msgstr "Serwuj treść zaszyfrowaną i uwierzytelnioną"

#: settings/config/config.php:1300
msgid "Enable Permissions Policy"
msgstr "Włącz zasady uprawnień"

#: settings/config/config.php:1222
msgid "Feature"
msgstr "Funkcja"

#: settings/config/config.php:215 settings/config/config.php:1218
#: settings/config/config.php:1662
msgid "They might be misused if you don’t actively tell the browser to disable these features."
msgstr "Mogą być niewłaściwie używane, jeśli przeglądarka nie zostanie aktywnie poinformowana o braku wybranych funkcji."

#: settings/config/config.php:1217
msgid "About the Permission Policy"
msgstr "Informacje na temat polityki uprawnień"

#: settings/config/config.php:1218
msgid "Browser features are plentiful, but most are not needed on your website."
msgstr "Mnogość funkcje przeglądarki, zazwyczaj, nie jest potrzebna w witrynie."

#: settings/config/config.php:1211
msgid "Self (Default)"
msgstr "Samodzielnie (domyślnie)"

#: settings/config/config.php:1166 settings/config/menu.php:82
msgid "The extensive mixed content scan will list all current and future issues and provide a fix, or instructions to fix manually."
msgstr "Rozbudowane skanowanie mieszanej zawartości wyświetli listę wszystkich bieżących i przyszłych problemów oraz zapewni poprawkę lub instrukcje naprawy."

#: settings/config/config.php:1165
msgid "About the Mixed Content Scan"
msgstr "Informacje o skanowaniu zawartości mieszanej"

#: settings/config/config.php:1161 modal/build/index.5c0c53e2db8c73c5db9d.js:1
#: modal/build/index.855f8bc8ac202db563f6.js:1
#: modal/build/index.b167e8372488ba3980d4.js:1
#: modal/build/index.f0747a4e3a76f14cbb01.js:1
#: modal/src/components/DeactivationModal/DeactivationModal.js:87
msgid "Mixed content scan"
msgstr "Skanowanie zawartości mieszanej"

#: settings/config/config.php:689
msgid "Cross Origin Embedder Policy"
msgstr "Zasady osadzania między źródłami"

#: settings/config/config.php:674
msgid "Cross Origin Resource Policy"
msgstr "Zasady dotyczące zasobów między źródłami"

#: settings/config/config.php:659
msgid "Cross Origin Opener Policy"
msgstr "Zasady otwierania między źródłami"

#: settings/config/config.php:656
msgid "One of the most powerful features, and therefore the most complex are the Cross-Origin headers that can isolate your website so any data leaks are minimized."
msgstr "Jedną z najpotężniejszych funkcji, a przez to najbardziej złożoną, są nagłówki Cross-Origin, które mogą izolować witrynę, aby zminimalizować wszelkie wycieki danych."

#: settings/config/config.php:655
msgid "About Cross Origin Policies"
msgstr "Informacje o zasadach Cross Origin"

#: settings/config/config.php:631
msgid "Choose the max-age for HSTS"
msgstr "Wybierz maksymalny wiek dla HSTS"

#: settings/config/config.php:629
msgid "Two years (required for preload)"
msgstr "Dwa lata (wymagane do wstępnego załadowania)"

#: settings/config/config.php:587
msgid "After enabling this feature, you can submit your site to %shstspreload.org%s"
msgstr "Po włączeniu funkcji można zgłosić swoją witrynę do %shstspreload.org%s"

#: settings/config/config.php:586
msgid "Include preload"
msgstr "Uwzględnij wstępne ładowanie"

#: settings/config/config.php:571
msgid "Leveraging your SSL certificate with HSTS is a staple for every website. Force your website over SSL, mitigating risks of malicious counterfeit websites in your name."
msgstr "Wykorzystanie certyfikatu SSL z HSTS jest podstawą każdej witryny. Wymuś korzystanie z protokołu SSL w swojej witrynie, zmniejszając ryzyko złośliwych fałszywych witryn internetowych."

#: settings/config/config.php:570
msgid "About HTTP Strict Transport Security"
msgstr "Informacje o ścisłych zabezpieczeniach transportu HTTP"

#: settings/config/config.php:782
msgid "Disable user enumeration"
msgstr "Wyłącz możliwość pobrania użytkowników po numerach"

#: settings/config/config.php:772
msgid "Disable directory browsing"
msgstr "Zablokuj przeglądanie zawartości katalogów"

#: settings/config/config.php:97
msgid "301 PHP redirect"
msgstr "Przekierowanie 301 w PHP"

#: settings/config/config.php:96
msgid "No redirect"
msgstr "Brak przekierowania"

#: settings/config/config.php:93 settings/config/config.php:102
#: settings/config/disable-fields-filter.php:18
msgid "Redirect method"
msgstr "Motada przekierowań"

#: settings/config/menu.php:549
msgid "DNS verification"
msgstr "Weryfikacja DNS"

#: settings/config/config.php:990 settings/config/menu.php:258
msgid "XML-RPC"
msgstr "XML-RPC"

#: placeholders/class-placeholder.php:331
#: placeholders/class-placeholder.php:348
#: placeholders/class-placeholder.php:372
#: placeholders/class-placeholder.php:389
#: placeholders/class-placeholder.php:415
#: placeholders/class-placeholder.php:440
#: settings/build/43.5782841cd77291fd781f.js:1
#: settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/build/814.3a902150d64a573787ce.js:1
#: settings/src/Settings/MixedContentScan/MixedContentScan.js:90
#: settings/src/Settings/RiskConfiguration/RiskData.js:114
msgid "Details"
msgstr "Szczegóły"

#: settings/config/menu.php:564 settings/build/43.5782841cd77291fd781f.js:1
#: settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/Dashboard/OtherPlugins/OtherPluginsData.js:74
#: settings/src/Settings/License/License.js:71
msgid "Activate"
msgstr "Włącz"

#: onboarding/class-onboarding.php:182
msgid "Congratulations!"
msgstr "Gratulacje!"

#: settings/config/menu.php:59 settings/build/index.949c228dda25c895581b.js:1
#: settings/src/Header.js:31
msgid "Let's Encrypt"
msgstr "Let's Encrypt"

#: security/notices.php:158 security/notices.php:175
#: settings/config/config.php:311 settings/config/config.php:1123
#: settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/Settings/LimitLoginAttempts/AddUserModal.js:53
msgid "Username"
msgstr "Nazwa użytkownika"

#: onboarding/class-onboarding.php:182
msgid "Thanks for updating!"
msgstr "Dziękujemy za aktualizację!"

#: lets-encrypt/config/fields.php:351
msgid "Plesk admin URL"
msgstr "Adres URL administracji Plesk"

#: lets-encrypt/class-le-restapi.php:163 lets-encrypt/class-le-restapi.php:224
msgid "Permission denied."
msgstr "Brak dostępu."

#: security/wordpress/rename-admin-user.php:15
msgid "Username 'admin' has been changed to %s"
msgstr "Nazwa użytkownika „admin” została zmieniona na %s"

#: security/wordpress/prevent-login-info-leakage.php:9
msgid "Invalid login details."
msgstr "Nieprawidłowe dane logowania."

#: security/wordpress/block-code-execution-uploads.php:37
msgid "The code to block code execution in the uploads folder cannot be added automatically on nginx. Add the following code to your nginx.conf file:"
msgstr "Kod blokujący wykonanie kodu w folderze przesyłania nie może zostać dodany automatycznie w nginx. Dodaj następujący kod do pliku nginx.conf:"

#: security/wordpress/block-code-execution-uploads.php:24
msgid "Could not copy code execution test file."
msgstr "Nie można skopiować pliku do testu wykonania."

#: security/wordpress/block-code-execution-uploads.php:19
msgid "Uploads folder not writable."
msgstr "Brak możliwości zapisu w folderze przesyłania."

#: security/wordpress/block-code-execution-uploads.php:14
msgid "Could not find code execution test file."
msgstr "Nie można znaleźć pliku testu wykonania kodu."

#: security/sync-settings.php:35
msgid "Changed debug.log location to:"
msgstr "Zmieniono lokalizację debug.log na:"

#: security/notices.php:281
msgid "Your WordPress version is visible to others."
msgstr "Wersja WordPress jest widoczna."

#: security/notices.php:267
msgid "Anyone can register an account on your site. Consider disabling this option in the WordPress general settings."
msgstr "Każdy może zarejestrować konto w witrynie. Rozważ wyłączenie w ogólnych ustawieniach WordPressa."

#: security/notices.php:253
msgid "The built-in file editors are accessible to others."
msgstr "Wbudowany edytor plików jest dostępny."

#: security/notices.php:207
msgid "Your database prefix is set to the default 'wp_'."
msgstr "Prefiks bazy danych jest ustawiony na domyślny „wp_”."

#: security/notices.php:202
msgid "Your database prefix is renamed and randomized. Awesome!"
msgstr "Prefiks bazy danych został zmieniony na losowy. Wspaniale!"

#: security/notices.php:191
msgid "Code execution is allowed in the public 'Uploads' folder."
msgstr "Wykonanie kodu jest dozwolone w publicznym folderze przesyłania."

#: security/notices.php:159
msgid "Your site registered a user with the name 'admin'."
msgstr "Witryna posiada użytkownika o nazwie „admin”."

#: security/notices.php:140
msgid "Prevent user enumeration"
msgstr "Zapobiegaj wyliczaniu użytkowników"

#: security/notices.php:138
msgid "Your site is vulnerable to user enumeration attacks."
msgstr "Witryna jest podatna na ataki polegające na wyliczaniu użytkowników."

#: security/notices.php:121
msgid "Your site logs information to a public debugging file."
msgstr "Witryna rejestruje informacje w publicznym pliku debugowania."

#: security/notices.php:120
msgid "Debugging"
msgstr "Debugowanie"

#: security/notices.php:106
msgid "We have detected administrator roles where the login and display names are the same."
msgstr "Wykryliśmy role administratora, w których nazwy logowania i nazwy wyświetlane są takie same."

#: security/notices.php:92
msgid "It is currently possible to create an administrator user with the same login and display name."
msgstr "Obecnie możliwe jest utworzenie użytkownika administratora z tą samą nazwą logowania i wyświetlaną."

#: settings/config/menu.php:534
msgid "General Settings"
msgstr "Ustawienia ogólne"

#: upgrade/upgrade-to-pro.php:579
msgid "Your license key has reached its activation limit."
msgstr "Twój klucz licencyjny osiągnął swój limit aktywacji."

#: upgrade/upgrade-to-pro.php:573
msgid "Your license is not active for this URL."
msgstr "Twoja licencja nie jest aktywna dla tego adresu URL."

#: upgrade/upgrade-to-pro.php:570
msgid "Invalid license."
msgstr "Nieprawidłowa licencja."

#: upgrade/upgrade-to-pro.php:564
msgid "Your license key has been disabled."
msgstr "Klucz licencji został dezaktywowany."

#: upgrade/upgrade-to-pro.php:558
msgid "Your license key expired on %s."
msgstr "Twój klucz licencyjny wygasł %s."

#: upgrade/upgrade-to-pro.php:550 upgrade/upgrade-to-pro.php:582
msgid "An error occurred, please try again."
msgstr "Wystąpił błąd, proszę spróbować ponownie."

#: upgrade/upgrade-to-pro.php:372 upgrade/upgrade-to-pro.php:373
msgid "An Error Occurred:"
msgstr "Wystąpił błąd:"

#: upgrade/upgrade-to-pro.php:367
msgid "Visit Dashboard"
msgstr "Przejdź do kokpitu"

#: upgrade/upgrade-to-pro.php:356
msgid "Installing"
msgstr "Instalowanie"

#: upgrade/upgrade-to-pro.php:110
msgid "Plugin activated"
msgstr "Wtyczka aktywowana"

#: upgrade/upgrade-to-pro.php:109
msgid "Activating plugin..."
msgstr "Włącz wtyczkę..."

#: upgrade/upgrade-to-pro.php:102
msgid "Installing plugin..."
msgstr "Instalowanie wtyczki..."

#: upgrade/upgrade-to-pro.php:83
msgid "Destination folder already exists"
msgstr "Docelowy folder już istnieje"

#: upgrade/upgrade-to-pro.php:103
msgid "Plugin installed"
msgstr "Wtyczka została zainstalowana"

#: upgrade/upgrade-to-pro.php:576
msgid "This appears to be an invalid license key for this plugin."
msgstr "Klucz licencyjny dla wtyczki jest niepoprawny."

#: upgrade/upgrade-to-pro.php:567
msgid "Missing license."
msgstr "Brakuje licencji."

#: upgrade/upgrade-to-pro.php:373
msgid "Check your %slicense%s."
msgstr "Sprawdź swoją %slicencję%s."

#: upgrade/upgrade-to-pro.php:372
msgid "Install %sManually%s."
msgstr "Zainstaluj %sręcznie%s."

#: upgrade/upgrade-to-pro.php:111
msgid "Failed to activate plugin"
msgstr "Nie udało się włączyć wtyczki"

#: upgrade/upgrade-to-pro.php:104
msgid "Failed to install plugin"
msgstr "Nie udało się zainstalować wtyczki"

#: upgrade/upgrade-to-pro.php:331
msgid "Recommended by Really Simple Plugins"
msgstr "Polecane przez Really Simple Plugins"

#: upgrade/upgrade-to-pro.php:233
msgid "Installation finished"
msgstr "Instalacja została zakończona"

#: upgrade/upgrade-to-pro.php:178
msgid "Really Simple SSL automatically detects your settings and configures your website to run over HTTPS. To keep it lightweight, we kept the options to a minimum. Your website will move to SSL with one click."
msgstr "Really Simple SSL automatycznie wykrywa ustawienia i konfiguruje witrynę do działania z HTTPS. Żeby wszystko było łatwe, ograniczyliśmy opcje do minimum. Twoja witryna zostanie przeniesiona do SSL jednym kliknięciem."

#: upgrade/upgrade-to-pro.php:174
msgid "One click SSL optimization"
msgstr "Optymalizacja SSL za pomocą jednego kliknięcia"

#: upgrade/upgrade-to-pro.php:157
msgid "GDPR/CCPA Privacy Suite"
msgstr "GDPR/CCPA Pakiet Prywatności"

#: upgrade/upgrade-to-pro.php:148
msgid "Get detailed insights into visitors' behaviour with Burst Statistics, the privacy-friendly analytics dashboard from Really Simple Plugins."
msgstr "Uzyskaj szczegółowy wgląd w zachowanie odwiedzających dzięki Burst Statistics, przyjaznemu dla prywatności pulpitowi analitycznemu od Really Simple Plugins."

#: settings/settings.php:524 upgrade/upgrade-to-pro.php:144
msgid "Self-hosted and privacy-friendly analytics tool."
msgstr "Samodzielne i przyjazne dla prywatności narzędzie analityczne."

#: upgrade/upgrade-to-pro.php:97
msgid "Failed to gather package information"
msgstr "Nie powiodło się zebranie informacji o paczce"

#: upgrade/upgrade-to-pro.php:96
msgid "Package information retrieved"
msgstr "Pobrane informacje o pakiecie"

#: upgrade/upgrade-to-pro.php:95
msgid "Retrieving package information..."
msgstr "Pobieranie informacji o pakiecie…"

#: upgrade/upgrade-to-pro.php:90
msgid "License invalid"
msgstr "Nieprawidłowa licencja"

#: upgrade/upgrade-to-pro.php:89
msgid "License valid"
msgstr "Prawidłowa licencja"

#: upgrade/upgrade-to-pro.php:88
msgid "Validating license..."
msgstr "Sprawdzam licencję…"

#: upgrade/upgrade-to-pro.php:82
msgid "Able to create destination folder"
msgstr "Możliwe utworzenie docelowego katalogu"

#: upgrade/upgrade-to-pro.php:81
msgid "Checking if plugin folder exists..."
msgstr "Sprawdzam czy katalog wtyczki istnieje…"

#: class-admin.php:2086
msgid "Could not test certificate"
msgstr "Nie można sprawdzić certyfikatu"

#: class-admin.php:2311
msgid "Improve security with Really Simple SSL Pro."
msgstr "Popraw bezpieczeństwo z Really Simple SSL Pro."

#: class-admin.php:2095
msgid "Check manually"
msgstr "Sprawdź ręcznie"

#: onboarding/class-onboarding.php:249
msgid "Could not test certificate."
msgstr "Nie można sprawdzić certyfikatu."

#: class-admin.php:2087 onboarding/class-onboarding.php:249
msgid "Automatic certificate detection is not possible on your server."
msgstr "Na serwerze nie ma możliwości automatycznego wykrywanie certyfikatów."

#: lets-encrypt/config/fields.php:441
msgid "Checking host..."
msgstr "Sprawdzanie serwera…"

#: lets-encrypt/class-letsencrypt-handler.php:1152
msgid "After completing the installation, you can let Really Simple SSL automatically configure your site for SSL by using the 'Activate SSL' button."
msgstr "Po zakończeniu instalacji możesz pozwolić Really Simple SSL automatycznie skonfigurować SSL witryny za pomocą przycisku „Włącz SSL”."

#: lets-encrypt/class-letsencrypt-handler.php:1137
msgid "We have not detected any known hosting limitations."
msgstr "Nie wykryliśmy żadnych znanych ograniczeń hostingu."

#: class-admin.php:2068
msgid "The wp-config.php file is not writable, and needs to be edited. Please set this file to writable."
msgstr "Plik wp-config.php nie jest zapisywalny i wymaga edycji. Ustaw go na zapisywalny."

#: class-site-health.php:150
msgid "Your website does not send all recommended security headers."
msgstr "Witryna nie wysyła wszystkich zalecanych nagłówków bezpieczeństwa."

#: class-admin.php:2299
msgid "Recommended security headers enabled."
msgstr "Włączono zalecane nagłówki zabezpieczeń."

#: class-site-health.php:71 class-site-health.php:101 class-site-health.php:132
#: class-site-health.php:172 security/wordpress/vulnerabilities.php:257
#: security/wordpress/vulnerabilities.php:277
msgid "Security"
msgstr "Bezpieczeństwo"

#: lets-encrypt/config/fields.php:126
msgid "Disable OCSP Stapling"
msgstr "Wyłącz OCSP"

#: lets-encrypt/config/fields.php:124
msgid "OCSP stapling is configured as enabled by default. You can disable this option if this is not supported by your hosting provider."
msgstr "OCSP powinno być domyślnie włączone. Można wyłączyć, jeśli nie jest to obsługiwane przez hostingodawcę."

#: lets-encrypt/functions.php:12
msgid "For more information, please read this %sarticle%s"
msgstr "Aby uzyskać więcej informacji, przeczytaj %sartykuł%s"

#: class-admin.php:2364
msgid "Black Friday sale! Get 40% Off Really Simple SSL Pro"
msgstr "Wyprzedaż w Czarny Piątek! Uzyskaj 40% zniżki na wersję pro"

#: lets-encrypt/config/fields.php:525
msgid "Checking if Terms & Conditions are accepted..."
msgstr "Sprawdzanie, czy regulamin został zaakceptowany…"

#: lets-encrypt/class-letsencrypt-handler.php:993
msgid "The Terms & Conditions were not accepted. Please accept in the general settings."
msgstr "Regulamin nie został zaakceptowany. Proszę zaakceptować w ustawieniach ogólnych."

#: lets-encrypt/class-letsencrypt-handler.php:989
msgid "Terms & Conditions are accepted."
msgstr "Regulamin został zaakceptowany."

#: class-admin.php:2259
msgid "HttpOnly Secure cookies have been set automatically!"
msgstr "HttpOnly Secure cookies zostało ustawione automatycznie!"

#. translators: %s is replaced with the plugin name.
#: class-admin.php:2350
msgid "We have detected the %s plugin on your website."
msgstr "Wykryliśmy wtyczkę %s w twojej witrynie."

#. translators: %s is replaced with the plugin name.
#: class-admin.php:2350
msgid "As Really Simple SSL handles all the functionality this plugin provides, we recommend to disable this plugin to prevent unexpected behavior."
msgstr "Ponieważ Really Simple SSL obsługuje wszystkie funkcje wtyczki, zalecamy wyłączenie wtyczki, aby zapobiec nieoczekiwanemu zachowaniu."

#: onboarding/class-onboarding.php:255
msgid "No SSL certificate has been detected."
msgstr "Nie wykryto certyfikatu SSL."

#: onboarding/class-onboarding.php:255
msgid "Please refresh the SSL status if a certificate has been installed recently."
msgstr "Proszę odświeżyć wykrywania SSL jeśli certyfikat został ostatnio zainstalowany."

#: settings/config/config.php:61 settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/LetsEncrypt/Installation.js:95
#: settings/src/LetsEncrypt/Installation.js:100
#: settings/src/LetsEncrypt/Installation.js:104
msgid "Download"
msgstr "Pobieranie"

#: settings/config/menu.php:194 settings/config/menu.php:199
#: settings/build/778.f7f7013a567067838984.js:1
#: settings/src/Settings/Settings.js:164
msgid "Notifications"
msgstr "Powiadomienia"

#: lets-encrypt/config/fields.php:29 settings/config/menu.php:559
msgid "Installation"
msgstr "Instalacja"

#: lets-encrypt/config/fields.php:17 settings/config/menu.php:544
msgid "Directories"
msgstr "Katalogi"

#: lets-encrypt/config/notices.php:28
msgid "Your Key and Certificate directories are not properly protected."
msgstr "Katalogi kluczy i certyfikatów nie są odpowiednio chronione."

#: lets-encrypt/config/notices.php:109
msgid "Your certificate will be renewed and installed automatically."
msgstr "Twój certyfikat zostanie odnowiony i zainstalowany automatycznie."

#: lets-encrypt/config/fields.php:499
msgid "Retrieving DNS verification token..."
msgstr "Pobieram token weryfikacyjny DNS…"

#: lets-encrypt/config/fields.php:493 lets-encrypt/config/fields.php:531
msgid "Creating account..."
msgstr "Tworzenie konta…"

#: lets-encrypt/config/fields.php:21
msgid "DNS Verification"
msgstr "Weryfikacja DNS"

#: lets-encrypt/config/fields.php:472
msgid "Checking challenge directory reachable over http..."
msgstr "Sprawdzam katalog wyzwań dostępny przez http…"

#: lets-encrypt/config/fields.php:465
msgid "Checking permissions..."
msgstr "Sprawdzam uprawnienia…"

#: lets-encrypt/config/fields.php:459
msgid "Checking certs directory..."
msgstr "Sprawdzam katalog certyfikatów…"

#: lets-encrypt/config/notices.php:69
msgid "Your certificate is valid until: %s"
msgstr "Twój certyfikat jest ważny do: %s"

#: lets-encrypt/config/notices.php:73
msgid "Your certificate will expire on %s. You can renew it %shere%s."
msgstr "Twój certyfikat wygaśnie %s. Można go odnowić %stutaj%s."

#: lets-encrypt/config/fields.php:25 settings/config/menu.php:554
msgid "Generation"
msgstr "Generowanie"

#: lets-encrypt/config/fields.php:453
msgid "Checking key directory..."
msgstr "Sprawdzanie katalogu kluczy…"

#: settings/config/menu.php:538
msgid "Hosting"
msgstr "Hosting"

#: lets-encrypt/config/fields.php:12
msgid "General settings"
msgstr "Ustawienia ogólne"

#: lets-encrypt/config/fields.php:8 settings/config/menu.php:526
msgid "System Status"
msgstr "Status systemu"

#: lets-encrypt/config/fields.php:134 lets-encrypt/config/notices.php:50
msgid "Domain"
msgstr "Domena"

#: settings/settings.php:539
msgid "Terms and Conditions"
msgstr "Zasady i warunki"

#: lets-encrypt/config/fields.php:94 lets-encrypt/config/fields.php:97
#: settings/config/config.php:169
msgid "Email address"
msgstr "Adres e-mail"

#: lets-encrypt/config/fields.php:58
msgid "Checking if CURL is available..."
msgstr "Sprawdzam, czy CURL jest dostępny…"

#: lets-encrypt/config/fields.php:52
msgid "Checking SSL certificate..."
msgstr "Sprawdzam certyfikat SSL…"

#: lets-encrypt/config/fields.php:47
msgid "Detected status of your setup."
msgstr "Wykryto ustawienia konfiguracji."

#: lets-encrypt/config/fields.php:419
msgid "Store for renewal purposes. If not stored, renewal may need to be done manually."
msgstr "Przechowuj w celu odnowienia. Jeśli nie będzie zachowane, odnowienie może wymagać ręcznej interwencji."

#: lets-encrypt/config/fields.php:396
msgid "Plesk password"
msgstr "Hasło Pleska"

#: lets-encrypt/config/fields.php:376
msgid "You can find your Plesk username and password in %s"
msgstr "Możesz znaleźć swoją nazwę użytkownika i hasło Plesk w %s"

#: lets-encrypt/config/fields.php:372
msgid "Plesk username"
msgstr "Nazwa użytkownika Pleska"

#: lets-encrypt/config/fields.php:352
msgid "The URL you use to access your Plesk dashboard. Ends on :8443."
msgstr "Adres URL używany do uzyskiwania dostępu do pulpitu Plesk. Kończy się :8443."

#: lets-encrypt/config/fields.php:348
msgid "Plesk host"
msgstr "Adres hosta Plesk"

#: lets-encrypt/config/fields.php:326
msgid "CloudWays api key"
msgstr "Klucz API CloudWays"

#: lets-encrypt/config/fields.php:290
msgid "DirectAdmin password"
msgstr "Hasło DirectAdmin"

#: lets-encrypt/config/fields.php:271
msgid "DirectAdmin username"
msgstr "Nazwa użytkownika DirectAdmin"

#: lets-encrypt/config/fields.php:175 settings/config/config.php:33
msgid "Hosting provider"
msgstr "Dostawca hostingu"

#: lets-encrypt/config/fields.php:310
msgid "CloudWays user email"
msgstr "Adres e-mail użytkownika CloudWays"

#: lets-encrypt/cron.php:84 security/cron.php:62
msgid "Once every 5 minutes"
msgstr "Raz na na 5 minut"

#: lets-encrypt/cron.php:80 security/cron.php:66
msgid "Once every day"
msgstr "Raz dziennie"

#: lets-encrypt/cron.php:76 security/cron.php:74
msgid "Once every week"
msgstr "Raz każdego tygodnia"

#: lets-encrypt/class-letsencrypt-handler.php:1762
msgid "Installation failed."
msgstr "Instalacja nie powiodła się."

#: lets-encrypt/class-letsencrypt-handler.php:1648
msgid "Error code %s"
msgstr "Kod błędu %s"

#: lets-encrypt/class-letsencrypt-handler.php:1299
#: lets-encrypt/class-letsencrypt-handler.php:1303
msgid "Error code %s."
msgstr "Kod błędu %s."

#: lets-encrypt/class-letsencrypt-handler.php:1275
#: lets-encrypt/class-letsencrypt-handler.php:1585
msgid "no response"
msgstr "brak odpowiedzi"

#: lets-encrypt/integrations/cloudways/cloudways.php:248
msgid "Could not retrieve server list"
msgstr "Nie można było pobrać listy serwerów"

#: lets-encrypt/integrations/cloudways/cloudways.php:208
#: lets-encrypt/integrations/cloudways/cloudways.php:241
msgid "Successfully retrieved server id and app id"
msgstr "Pobrano identyfikatory serwera i aplikacji"

#: lets-encrypt/integrations/cloudways/cloudways.php:192
msgid "Error enabling auto renew for Let's Encrypt"
msgstr "Błąd podczas włączania automatycznego odnawiania Let's Encrypt"

#: lets-encrypt/integrations/cloudways/cloudways.php:177
#: lets-encrypt/integrations/cloudways/cloudways.php:183
msgid "Successfully installed Let's Encrypt"
msgstr "Zainstalowano Let's Encrypt"

#: lets-encrypt/integrations/cloudways/cloudways.php:157
msgid "Failed retrieving access token"
msgstr "Nie udało się pobrać tokena dostępu"

#: lets-encrypt/integrations/cpanel/cpanel.php:296
msgid "Login credentials incorrect"
msgstr "Nieprawidłowe dane logowania"

#: lets-encrypt/integrations/cpanel/cpanel.php:205
msgid "Errors were reported during installation."
msgstr "Wystąpiły błędy podczas instalacji."

#: lets-encrypt/integrations/cpanel/cpanel.php:126
msgid "Errors were reported during installation"
msgstr "Wystąpiły błędy podczas instalacji"

#: lets-encrypt/config/fields.php:537
msgid "Generating SSL certificate..."
msgstr "Generowanie certyfikatu SSL…"

#: lets-encrypt/integrations/plesk/functions.php:33
msgid "Installing SSL certificate using PLESK API..."
msgstr "Instalowanie certyfikatu SSL z użyciem API PLESK…"

#: lets-encrypt/integrations/cpanel/functions.php:107
#: lets-encrypt/integrations/directadmin/functions.php:35
msgid "Attempting to install certificate..."
msgstr "Próba zainstalowania certyfikatu…"

#: lets-encrypt/integrations/cloudways/functions.php:49
msgid "Enabling auto renew..."
msgstr "Włączanie automatycznych odnowień…"

#: lets-encrypt/integrations/cpanel/cpanel.php:121
#: lets-encrypt/integrations/directadmin/directadmin.php:122
msgid "SSL successfully installed on %s"
msgstr "Zainstalowano SSL na %s"

#: lets-encrypt/integrations/cloudways/functions.php:43
msgid "Installing SSL certificate..."
msgstr "Instalowanie certyfikatu SSL…"

#: lets-encrypt/functions.php:378
msgid "Go to activation"
msgstr "Przejdź do właczania"

#: lets-encrypt/class-letsencrypt-handler.php:274
msgid "(unknown)"
msgstr "(nieznane)"

#: class-admin.php:1957
msgid "(Unknown)"
msgstr "(Nieznane)"

#: class-admin.php:2081
msgid "Retry"
msgstr "Ponów"

#: class-admin.php:2119 settings/build/43.5782841cd77291fd781f.js:1
#: settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/Dashboard/TaskElement.js:51
msgid "Re-check"
msgstr "Sprawdź ponownie"

#: class-admin.php:2080 class-admin.php:2094 class-admin.php:2118
msgid "Install SSL certificate"
msgstr "Zainstaluj certyfikat SSL"

#: onboarding/class-onboarding.php:243
msgid "An SSL certificate has been detected"
msgstr "Nie wykryto certyfikatu SSL"

#. translators: %s is replaced with date.
#: class-admin.php:2108
msgid "SSL certificate will expire on %s."
msgstr "Certyfikat SSL wygaśnie w dniu  %s."

#: class-admin.php:2279
msgid "Or set your wp-config.php to writable and reload this page."
msgstr "Lub ustaw plik wp-config.php jako zapisywalny i przeładuj stronę."

#: class-site-health.php:149
msgid "Not all recommended security headers are installed"
msgstr "Nie wszystkie rekomendowane nagłówki bezpieczeństwa są zainstalowane"

#: class-site-health.php:129
msgid "Recommended security headers installed"
msgstr "Rekomendowane nagłówki bezpieczeństwa zainstalowane."

#: class-site-health.php:34
msgid "Security Headers Test"
msgstr "Test nagłówków bezpieczeństwa"

#: class-site-health.php:29
msgid "SSL Status Test"
msgstr "SSL test status"

#: class-site-health.php:137
msgid "The recommended security headers are detected on your site."
msgstr "Rekomendowane nagłówki bezpieczeństwa zostały wykryte na twojej stronie."

#: class-admin.php:2072
msgid "No SSL detected"
msgstr "Nie wykryto SSL"

#: class-site-health.php:218
msgid "Enable 301 redirect"
msgstr "Włącz przekierowanie 301"

#: class-site-health.php:154
msgid "Learn more about security headers"
msgstr "Dowiedz się więcej o nagłówkach bezpieczeństwa"

#: class-site-health.php:210
msgid "No 301 redirect to SSL enabled."
msgstr "301 przekierowanie do SSL nie jest włączone."

#: class-site-health.php:189
msgid "Really Simple SSL detected an SSL certificate, but has not been configured to enforce SSL."
msgstr "Really Simple SSL wykrył certyfikat SSL, ale nie został skonfigurowany aby wymusić SSL."

#: class-site-health.php:204
msgid "Really Simple SSL is installed, but no valid SSL certificate is detected."
msgstr "Really Simple SSL został zainstalowany, ale nie wykryto ważnego certyfikatu SSL."

#: class-site-health.php:186
msgid "SSL is not enabled."
msgstr "SSL nie jest włączone."

#: class-site-health.php:213
msgid "To ensure all traffic passes through SSL, please enable a 301 redirect."
msgstr "Żeby się upewnić że cały ruch przechodzi przez SSL, proszę aktywuj przekierowanie 301."

#: upgrade/upgrade-to-pro.php:190 settings/build/43.5782841cd77291fd781f.js:1
#: settings/src/Dashboard/OtherPlugins/OtherPlugins.js:24
msgid "Installed"
msgstr "Zainstalowano"

#: class-admin.php:1900 settings/config/menu.php:321
msgid "Open"
msgstr "Otwórz"

#: class-admin.php:1899 settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/Settings/MixedContentScan/MixedContentScan.js:58
msgid "Warning"
msgstr "Ostrzeżenie"

#: class-admin.php:2200
msgid "WordPress 301 redirect enabled. We recommend to enable a 301 .htaccess redirect."
msgstr "Przekierowanie WordPress 301 włączone. Zalecamy włączenie przekierowanie 301 .htaccess"

#: class-admin.php:2243
msgid "Your site uses Divi. This can require some additional steps before getting the secure lock."
msgstr "Twoja strona używa Divi. To może wymagać jakiś dodatkowych kroków zanim strona uzyska zamek bezpieczeństwa."

#: class-admin.php:2229
msgid "Your site uses Elementor. This can require some additional steps before getting the secure lock."
msgstr "Twoja strona używa Elementor. To może wymagać jakiś dodatkowych kroków zanim strona uzyska zamek bezpieczeństwa."

#: class-admin.php:1898
msgid "Completed"
msgstr "Zakończono"

#: onboarding/class-onboarding.php:199 onboarding/class-onboarding.php:206
#: upgrade/upgrade-to-pro.php:146 upgrade/upgrade-to-pro.php:159
#: upgrade/upgrade-to-pro.php:176 settings/build/43.5782841cd77291fd781f.js:1
#: settings/src/Dashboard/OtherPlugins/OtherPluginsData.js:73
msgid "Install"
msgstr "Zainstaluj"

#: class-admin.php:2214
msgid "The .htaccess redirect rules selected by this plugin failed in the test. Set manually or dismiss to leave on PHP redirect."
msgstr "Reguła przekierowania .htaccess wybrana przez wtyczkę nie przeszła testu. Ustaw ręcznie lub odrzuć żeby zostawić przekierowanie PHP."

#: class-site-health.php:197 settings/build/43.5782841cd77291fd781f.js:1
#: settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/build/814.3a902150d64a573787ce.js:1
#: settings/src/Dashboard/Progress/ProgressFooter.js:24
#: settings/src/Onboarding/OnboardingControls.js:136
msgid "Activate SSL"
msgstr "Aktywuj SSL"

#: progress/class-progress.php:90
msgid "Basic SSL configuration finished! Improve your score with %sReally Simple SSL Pro%s."
msgstr "Podstawowa konfiguracja SSL zakończona! Popraw swój wynik z %sReally Simple SSL Pro%s."

#: settings/config/menu.php:23 settings/config/menu.php:29
#: settings/config/menu.php:364
msgid "General"
msgstr "Ogólnie"

#: class-admin.php:2154
msgid "Mixed content fixer not enabled. Enable the option to fix mixed content on your site."
msgstr "Naprawa mieszanej treści nie jest włączona. Włącz tę opcję, aby naprawić mieszaną treść na swojej stronie."

#: class-admin.php:1942
msgid "No recommended redirect rules detected."
msgstr "Nie wykryto rekomendowanych zasad przekierowania."

#: class-admin.php:2136
msgid "Really Simple SSL has received no response from the webpage."
msgstr "Really Simple SSL nie otrzymał odpowiedzi ze strony internetowej."

#: progress/class-progress.php:85
msgid "SSL configuration finished!"
msgstr "Konfiguracja SSL zakończona!"

#: progress/class-progress.php:83
msgid "SSL is activated on your site."
msgstr "SSL jest aktywne na twojej stronie."

#: class-multisite.php:114
msgid "SSL is enabled networkwide."
msgstr "SSL jest włączone w całej sieci."

#: class-admin.php:2051
msgid "SSL is not enabled yet."
msgstr "SSL nie jest jeszcze włączone."

#: progress/class-progress.php:96
msgid "SSL is not yet enabled on this site."
msgstr "SSL nie jest jeszcze włączone na tej stronie."

#: class-admin.php:125 class-multisite.php:223
#: settings/build/index.949c228dda25c895581b.js:1 settings/src/Header.js:36
msgid "Support"
msgstr "Pomoc"

#: class-admin.php:2143
msgid "The mixed content fixer is active, but was not detected on the frontpage."
msgstr "Naprawa mieszanej treści jest aktywna, ale nie została wykryta na frontpage."

#: settings/config/config.php:1642 settings/build/43.5782841cd77291fd781f.js:1
#: settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/src/Dashboard/SslLabs/SslLabs.js:319
#: settings/src/LetsEncrypt/DnsVerification.js:64
msgid "Read more"
msgstr "Dowiedz się więcej"

#. Author of the plugin
#: rlrsssl-really-simple-ssl.php
msgid "Really Simple Plugins"
msgstr "Wtyczki Really Simple"

#: onboarding/class-onboarding.php:170
msgid "Check out Really Simple SSL Pro"
msgstr "Sprawdź Really Simple SSL Pro"

#: class-admin.php:1711
msgid "Don't show again"
msgstr "Nie pokazuj ponownie"

#. translators: %s is replaced with the error description.
#: class-admin.php:2161
msgid "The mixed content fixer could not be detected due to a cURL error: %s. cURL errors are often caused by an outdated version of PHP or cURL and don't affect the front-end of your site. Contact your hosting provider for a fix."
msgstr "Naprawa mieszanej treści nie została wykryta ze względu na błąd cURL: %s. Błędy cURL często są spowodowane przestarzałą wersją PHP lub cURL i nie wpływają na front-end twojej strony. Skontaktuj się ze swoim dostawcom hostingu w celu naprawy."

#: class-admin.php:2148
msgid "Error occurred when retrieving the webpage."
msgstr "Wystąpił błąd podczas pobierania strony."

#: onboarding/class-onboarding.php:186
#: settings/build/43.5782841cd77291fd781f.js:1
#: settings/src/Dashboard/Vulnerabilities/Vulnerabilities.js:116
msgid "Enable"
msgstr "Włącz"

#: class-site-health.php:222
msgid "301 .htaccess redirect is not enabled."
msgstr "Nie włączono przekierowania 301 w pliku .htaccess."

#: class-site-health.php:177
msgid "You have set a 301 redirect to SSL. This is important for SEO purposes"
msgstr "Musisz ustawić przekierowanie 301 na SSL. Jest to ważne z punktu widzenia SEO"

#: class-site-health.php:169
msgid "301 SSL redirect enabled"
msgstr "301 SSL przekierowanie włączone"

#: class-site-health.php:225
msgid "The 301 .htaccess redirect is the fastest and most reliable redirect option."
msgstr "Przekierowanie 301 .htaccess  jest najszybszą oraz najbardziej niezawodną opcją."

#: class-admin.php:546
msgid "Dismiss"
msgstr "Odrzuć"

#: class-admin.php:2003
msgid "The 'force-deactivate.php' file has to be renamed to .txt. Otherwise your ssl can be deactivated by anyone on the internet."
msgstr "Plik \"force-deactivate.php\" musi zostać zmieniony do .txt. W innym przypadku twoje SSL może zostać dezaktywowane przez każdego w internecie."

#: class-admin.php:1698
msgid "Maybe later"
msgstr "Może później"

#: class-admin.php:1685
msgid "Leave a review"
msgstr "Dodaj opinię"

#: class-multisite.php:163
msgid "Activate SSL per site or install a wildcard certificate to fix this."
msgstr "Aby naprawić, aktywuj SSL dla witryny lub zainstaluj certyfikat typu Wildcard."

#: class-multisite.php:161
msgid "You run a Multisite installation with subdomains, but your site doesn't have a wildcard certificate."
msgstr "Używasz instalacji wielowitrynowej z subdomenami, ale witryna nie posiada certyfikatu typu Wildcard."

#: class-multisite.php:162
msgid "This leads to issues when activating SSL networkwide since subdomains will be forced over SSL as well while they don't have a valid certificate."
msgstr "To może spowodować problemy przy aktywacji SSL dla całej sieci, gdyż SSL będzie wymuszony dla subdomen, które nie mają ważnego certyfikatu."

#: upgrade/upgrade-to-pro.php:370 modal/build/204.cd378a48efe26fc7867c.js:1
#: modal/build/204.ded1ffcbe4657d8be922.js:1
#: modal/build/433.c979d76891c58ebf0fd9.js:1
#: modal/src/components/Modal/RssslModal.js:46
#: settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/build/814.3a902150d64a573787ce.js:1
#: settings/src/Settings/GeoBlockList/TrustIpAddressModal.js:121
#: settings/src/Settings/LimitLoginAttempts/AddIpAddressModal.js:112
#: settings/src/Settings/LimitLoginAttempts/AddUserModal.js:83
msgid "Cancel"
msgstr "Anuluj"

#: class-admin.php:549 settings/build/43.5782841cd77291fd781f.js:1
#: settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/build/778.f7f7013a567067838984.js:1
#: settings/src/Dashboard/TaskElement.js:50 settings/src/Settings/Help.js:24
msgid "More info"
msgstr "Więcej informacji"

#. Author URI of the plugin
#: rlrsssl-really-simple-ssl.php
msgid "https://really-simple-plugins.com"
msgstr "https://really-simple-plugins.com"

#: mailer/class-mail.php:33 mailer/class-mail.php:37
#: settings/build/43.5782841cd77291fd781f.js:1
#: settings/src/Dashboard/Vulnerabilities/Vulnerabilities.js:192
msgid "Learn more"
msgstr "Dowiedz się więcej"

#: settings/config/config.php:138
msgid "If this option is set to true, the mixed content fixer will fire on the init hook instead of the template_redirect hook. Only use this option when you experience problems with the mixed content fixer."
msgstr "Jeżeli opcja jest aktywna, naprawa treści mieszanej zostanie uruchomiona w zaczepie init, zamiast w template_redirect hook. Użyj opcji tylko jeżeli występują problemy z naprawą treści mieszanej."

#: onboarding/class-onboarding.php:170
msgid "You can also let the automatic scan of the pro version handle this for you, and get premium support, increased security with HSTS and more!"
msgstr "Automatyczny skan w wersji Pro może zrobić to za ciebie, dodatkowo otrzymasz pomoc techniczną premium, zwiększone zabezpieczenia z HSTS i wiele więcej!"

#: class-site-health.php:230
msgid "Enable 301 .htaccess redirect"
msgstr "Włącz przekierowanie 301 w .htaccess"

#: class-admin.php:2178
msgid "No 301 redirect is set. Enable the WordPress 301 redirect in the settings to get a 301 permanent redirect."
msgstr "Brak przekierowania 301. Włącz przekierowanie 301 w ustawieniach WordPress, aby przekierować 301 na stałe."

#: class-admin.php:2207
msgid "Enable a .htaccess redirect or PHP redirect in the settings to create a 301 redirect."
msgstr "Włącz przekierowanie .htaccess lub w PHP, aby stworzyć przekierowanie 301."

#: class-admin.php:2174
msgid "301 redirect to https set."
msgstr "301 przekierowanie do https."

#: class-admin.php:2212
msgid ".htaccess redirect."
msgstr "Przekierowanie .htaccess."

#: class-multisite.php:147
msgid "View settings page"
msgstr "Wyświetl stronę ustawień"

#: settings/config/menu.php:41
msgid "Premium Support"
msgstr "Pomoc techniczna premium"

#: class-admin.php:2047
msgid "SSL is enabled on your site."
msgstr "SSL jest włączone w twojej witrynie"

#: lets-encrypt/functions.php:369 lets-encrypt/functions.php:370
#: settings/build/778.f7f7013a567067838984.js:1
#: settings/src/Settings/SettingsGroup.js:112
msgid "Instructions"
msgstr "Instrukcje"

#: class-admin.php:1966
msgid "Your wp-config.php has to be edited, but is not writable."
msgstr "Plik wp-config.php musi zostać edytowany, ale nie jest zapisywalny."

#: lets-encrypt/class-letsencrypt-handler.php:30
#: onboarding/class-onboarding.php:9 security/firewall-manager.php:8
#: security/hardening.php:9
msgid "%s is a singleton class and you cannot create a second instance."
msgstr "%s jest klasy singleton i nie można utworzyć drugiej instancji."

#: class-admin.php:1988
msgid "A definition of a site url or home url was detected in your wp-config.php, but the file is not writable."
msgstr "Definicja site url lub home url została wykryta w pliku wp-config.php, ale plik nie jest edytowalny."

#: class-admin.php:1967 class-admin.php:1988
msgid "Set your wp-config.php to writable and reload this page."
msgstr "Dodaj możliwość zapisywania pliku wp-config.php i przeładuj stronę."

#: class-admin.php:2131
msgid "Mixed content fixer was successfully detected on the front-end."
msgstr "Naprawa treści mieszanej została wykryta poprawnie"

#. Plugin Name of the plugin
#: rlrsssl-really-simple-ssl.php
msgid "Really Simple SSL"
msgstr "Really Simple SSL"

#. Plugin URI of the plugin
#: rlrsssl-really-simple-ssl.php
msgid "https://really-simple-ssl.com"
msgstr "https://really-simple-ssl.com"

#: settings/config/config.php:698
msgid "Stop editing the .htaccess file"
msgstr "Nie zmieniaj pliku .htaccess"

#: class-site-health.php:201
msgid "No SSL detected."
msgstr "Nie wykryto SSL."

#: class-admin.php:2100
msgid "An SSL certificate was detected on your site."
msgstr "Wykryto certyfikat SSL w twojej witrynie."

#: settings/config/config.php:125 modal/build/index.5c0c53e2db8c73c5db9d.js:1
#: modal/build/index.855f8bc8ac202db563f6.js:1
#: modal/build/index.b167e8372488ba3980d4.js:1
#: modal/build/index.f0747a4e3a76f14cbb01.js:1
#: modal/src/components/DeactivationModal/DeactivationModal.js:124
msgid "Mixed content fixer"
msgstr "Naprawa mieszanych treści"

#: class-admin.php:118 class-admin.php:120 class-admin.php:1840
#: class-multisite.php:87 class-multisite.php:220 settings/config/menu.php:17
#: settings/config/menu.php:372 settings/build/43.5782841cd77291fd781f.js:1
#: settings/src/Dashboard/Vulnerabilities/Vulnerabilities.js:237
#: settings/src/Dashboard/Vulnerabilities/VulnerabilitiesFooter.js:24
msgid "Settings"
msgstr "Ustawienia"

#: class-admin.php:2004 settings/build/124.4fd0d3c41809db0344d3.js:1
#: settings/build/814.3a902150d64a573787ce.js:1
#: settings/src/Onboarding/Items/ListItem.js:46
msgid "Check again"
msgstr "Sprawdź ponownie"

#: class-multisite.php:133
msgid "Activate networkwide to fix this."
msgstr "Włącz dla całej sieci, aby naprawić."

#: class-multisite.php:132
msgid "Because the $_SERVER[\"HTTPS\"] variable is not set, your website may experience redirect loops."
msgstr "Witryna może zapętlić się w przekierowaniach, ponieważ zmienna $_SERVER[\"HTTPS\"] nie jest ustawiona."

#: class-multisite.php:131
msgid "You run a Multisite installation with subfolders, which prevents this plugin from fixing your missing server variable in the wp-config.php."
msgstr "Używasz instalacji wielowitrynowej z podfolderami, co powoduje, że wtyczka nie może dodać brakującej zmiennej serwera w wp-config.php."