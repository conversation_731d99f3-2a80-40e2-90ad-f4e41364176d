{"locale_data": {"messages": {"": {"plural_forms": "nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);", "language": "pl", "project_id_version": "WooCommerce - WooCommerce Payments"}, "Recommended for white or light-colored backgrounds with insufficient contrast.": [""], "Outline": ["Tylko kontur"], "Recommended for dark or colored backgrounds with high contrast.": [""], "Recommended for white or light-colored backgrounds with high contrast.": [""], "Some appearance settings may be overridden in the express payment section of the Cart & Checkout blocks.": ["Niektóre ustawienia wyglądu można zastąpić w sekcji płatności ekspresowych w blokach koszyka i realizacji płatności."], "Controls the corner roundness of express payment buttons.": ["Steruje zaokrągleniem narożników przycisków płatności ekspresowej."], "Border radius, slider": ["Promień krawędzi, suwak"], "Border radius, number input": ["Promień <PERSON>, wprowadzona liczba"], "WooPay button": ["Przycisk WooPay"], "Apple Pay / Google Pay buttons": ["Przyciski Apple Pay / Google Pay"], "Book with": ["Zarezerwuj z"], "Donate with": ["<PERSON><PERSON><PERSON>ć darowiznę z"], "Buy with": ["<PERSON><PERSON> <PERSON>"], "See the preview of enabled express payment buttons.": ["Wyświetl podgląd włączonych przycisków ekspresowej realizacji zamówień."], "Button size": ["Rozmiar przycisku"], "These settings will also apply to the %s on your store.": ["Te ustawienia będą też dotyczyły następujących przycisków w Twoim sklepie: %s."], "Border radius": ["Zaokrąglenia obramowania"], "Large {{helpText}}(55 px){{/helpText}}": ["<PERSON><PERSON><PERSON> {{helpText}}(55 pikseli){{/helpText}}"], "Medium {{helpText}}(48 px){{/helpText}}": ["<PERSON><PERSON><PERSON> {{helpText}}(48 pikseli){{/helpText}}"], "Small {{helpText}}(40 px){{/helpText}}": ["<PERSON><PERSON>y {{helpText}}(40 pikseli){{/helpText}}"], "Preview": ["Podgląd"], "Theme": ["Motyw"], "Select a button label that fits best with the flow of purchase or payment experience on your store.": ["<PERSON><PERSON><PERSON>rz etykietę przycisku najlepiej pasującą do przebiegu zakupu lub płatności w <PERSON>im sklepie."], "Call to action": ["Wezwanie do działania"], "Only icon": ["<PERSON><PERSON><PERSON>"], "Light": ["<PERSON><PERSON><PERSON>"], "Dark": ["Ciemny"]}}}