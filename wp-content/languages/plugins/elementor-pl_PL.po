# Translation of Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) in Polish
# This file is distributed under the same license as the Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-09-17 12:40:44+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n == 1) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pl\n"
"Project-Id-Version: Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release)\n"

#: includes/settings/settings.php:479
msgid "Load Google fonts locally to benefit from faster performance and ensure GDPR compliance. Fonts will be served from your own server instead of Google’s. Only the very first load (in the editor and on the front end) may take slightly longer."
msgstr "Wczytaj kroje pisma Google lokalnie, aby skorzystać z szybszej wydajności i zapewnić zgodność z RODO. Kroje pisma będą dostarczane z Twojego serwera, a nie z serwera Google. Tylko pierwsze wczytanie (w edytorze i w interfejsie użytkownika) może potrwać nieco dłużej."

#: modules/variables/module.php:58
msgid "Enable variables manager. (For this feature to work - Variables must be active)"
msgstr "Włącz menedżera zmiennych. (Aby ta funkcja działała, zmienne muszą być aktywne)"

#: modules/variables/module.php:57
msgid "Variables Manager"
msgstr "Menedżer zmiennych"

#: modules/variables/classes/rest-api.php:490
msgid "Batch operation failed"
msgstr "Operacja wsadowa nie powiodła się"

#. translators: %d: operation index
#: modules/variables/classes/rest-api.php:456
msgid "Invalid operation type at index %d"
msgstr "Nieprawidłowy rodzaj operacji pod indeksem %d"

#. translators: %d: operation index
#: modules/variables/classes/rest-api.php:443
msgid "Invalid operation structure at index %d"
msgstr "Nieprawidłowa struktura operacji pod indeksem %d"

#: modules/variables/classes/rest-api.php:433
msgid "Operations array cannot be empty"
msgstr "Tablica operacji nie może być pusta"

#: modules/variables/classes/rest-api.php:422
msgid "Watermark must be a non-negative integer"
msgstr "Znak wodny musi być liczbą całkowitą nieujemną"

#: modules/pro-install/pro-install-menu-item.php:182
msgid "Activate Elementor Pro"
msgstr "Włącz Elementora Pro"

#: modules/pro-install/pro-install-menu-item.php:181
msgid "Enjoy full access to powerful design tools, advanced widgets, and everything you need to create next-level websites."
msgstr "Ciesz się pełnym dostępem do potężnych narzędzi projektowych, zaawansowanych widżetów i wszystkiego, czego potrzebujesz, aby tworzyć witryny internetowe następnej generacji."

#: modules/pro-install/pro-install-menu-item.php:167
msgid "You've got Elementor Pro"
msgstr "Masz Elementor Pro"

#: modules/pro-install/pro-install-menu-item.php:156
msgid "Pro Upgrade"
msgstr "Ulepszenie Pro"

#: modules/pro-install/pro-install-menu-item.php:148
msgid "Build custom headers, footers, forms, popups, and WooCommerce stores."
msgstr "Twórz własne nagłówki, stopki, formularze, wyskakujące okienka i sklepy WooCommerce."

#: modules/pro-install/pro-install-menu-item.php:147
msgid "Upgrade to Pro to unlock powerful design tools and advanced features."
msgstr "Ulepsz do Pro, aby odblokować potężne narzędzia projektowe i zaawansowane funkcje."

#: modules/pro-install/pro-install-menu-item.php:118
msgid "Want to disconnect for any reason?"
msgstr "Chcesz się rozłączyć z jakiegokolwiek powodu?"

#. translators: %s: Connected user.
#: modules/pro-install/pro-install-menu-item.php:109
msgid "You're connected as %s."
msgstr "Jesteś połączony jako %s."

#: modules/pro-install/pro-install-menu-item.php:96
msgid "My Account"
msgstr "Moje konto"

#: modules/pro-install/pro-install-menu-item.php:93
msgid "Connected"
msgstr "Połączono"

#: modules/pro-install/pro-install-menu-item.php:71
msgid "Gain access to dozens of professionally designed templates, and connect your site to your My Elementor Dashboard."
msgstr "Uzyskaj dostęp do dziesiątek profesjonalnie zaprojektowanych szablonów i połącz swoją witrynę z kokpitem Mój Elementor."

#: modules/pro-install/pro-install-menu-item.php:68
msgid "Connect your Elementor Account"
msgstr "Połącz swoje konto Elementor"

#: modules/pro-install/pro-install-menu-item.php:27
msgid "Connect Settings"
msgstr "Ustawienia połączenia"

#: modules/pro-install/module.php:57
#: modules/pro-install/plugin-installer.php:21
msgid "There are no available subscriptions at the moment."
msgstr "W tej chwili nie ma dostępnych subskrypcji."

#: modules/pro-install/module.php:48
msgid "You do not have sufficient permissions to access this page."
msgstr "Nie masz wystarczających uprawnień, aby uzyskać dostęp do tej strony."

#: modules/pro-install/connect.php:17
msgid "pro-install"
msgstr "instalacja pro"

#: modules/pro-free-trial-popup/module.php:65
msgid "Show Pro free trial popup on 4th editor visit"
msgstr "Pokaż wyskakujące okienko z bezpłatną wersją próbną Pro przy czwartej wizycie edytora"

#: modules/pro-free-trial-popup/module.php:64
msgid "Pro Free Trial Popup"
msgstr "Wyskakujące okienko z bezpłatną wersją próbną Pro"

#: modules/components/module.php:35
msgid "Enable components."
msgstr "Włącz komponenty."

#: modules/components/documents/component.php:31
#: modules/components/module.php:34
#: assets/js/packages/editor-components/editor-components.js:2
#: assets/js/packages/editor-components/editor-components.strings.js:1
msgid "Components"
msgstr "Komponenty"

#: modules/components/documents/component.php:27
#: modules/components/widgets/component.php:23
msgid "Component"
msgstr "Komponent"

#. translators: %d: maximum components limit.
#: modules/components/components-rest-api.php:101
msgid "Components limit exceeded. Maximum allowed: %d"
msgstr "Przekroczono limit komponentów. Maksymalna dozwolona liczba: %d"

#: modules/atomic-widgets/module.php:204
msgid "Enable nested elements."
msgstr "Włącz zagnieżdżone elementy."

#: modules/atomic-widgets/module.php:195
msgid "Create endless custom styling."
msgstr "Twórz nieograniczone możliwości stylizacji."

#: modules/atomic-widgets/module.php:194
msgid "V4 Custom CSS"
msgstr "Własny CSS V4"

#: modules/atomic-widgets/elements/atomic-tabs/atomic-tabs.php:30
msgid "Atomic Tabs"
msgstr "Karty Atomic"

#: modules/atomic-widgets/elements/atomic-tabs/atomic-tabs-content.php:30
msgid "Atomic Tabs Content"
msgstr "Treść kart Atomic"

#: modules/atomic-widgets/elements/atomic-tabs/atomic-tab-link.php:30
msgid "Atomic Tab Link"
msgstr "Odnośnik karty Atomic"

#: modules/atomic-widgets/elements/atomic-tabs/atomic-tab-list.php:29
msgid "Atomic Tab List"
msgstr "Lista karty Atomic"

#. translators: %s: Theme name.
#: app/modules/import-export-customization/runners/import/site-settings.php:329
msgid "Theme: %s has been successfully installed"
msgstr "Motyw: %s został pomyślnie zainstalowany"

#. translators: %s: Theme name.
#: app/modules/import-export-customization/runners/import/site-settings.php:321
msgid "Failed to install theme: %s"
msgstr "Nie udało się zainstalować motywu: %s"

#. translators: %s: Theme name.
#: app/modules/import-export-customization/runners/import/site-settings.php:310
msgid "Theme: %s has already been installed and activated"
msgstr "Motyw: %s został już zainstalowany i włączony"

#. translators: %s: Theme name.
#: app/modules/import-export-customization/runners/import/site-settings.php:298
msgid "Theme: %s is already used"
msgstr "Motyw: %s jest już używany"

#: app/app.php:290
msgid "Enhanced import/export for website templates. Selectively include site content, templates, and settings with advanced granular control."
msgstr "Ulepszony import/eksport szablonów witryn internetowych. Selektywne uwzględnianie treści witryny, szablonów i ustawień dzięki zaawansowanej, szczegółowej kontroli."

#: core/editor/loader/common/editor-common-scripts-settings.php:145
msgid "Ally Accessibility"
msgstr "Dostępność Ally"

#: includes/widgets/heading.php:489
msgid "Accessible structure matters"
msgstr "Znaczenie struktury dostępności"

#: includes/widgets/heading.php:479
msgid "Connect to Ally"
msgstr "Połącz z Ally"

#: core/utils/hints.php:452
msgid "Customize the widget's look, position and the capabilities available for your visitors."
msgstr "Dostosuj wygląd i położenie widżetu oraz możliwości dostępności dla odwiedzających."

#: core/utils/hints.php:451
msgid "Connect the Ally plugin to your account to access all of it's accessibility features."
msgstr "Połącz wtyczkę Ally ze swoim kontem, aby uzyskać dostęp do wszystkich funkcji ułatwień dostępu."

#: core/utils/hints.php:450
msgid "Activate the Ally plugin to turn its accessibility features on across your site."
msgstr "Włącz wtyczkę Ally, aby włączyć funkcje ułatwień dostępu w całej witrynie."

#: core/utils/hints.php:449
msgid "Install Ally to add an accessibility widget visitors can use to navigate your site."
msgstr "Zainstaluj Ally, aby dodać widget ułatwień dostępu, dzięki któremu wszyscy odwiedzający będą mogli poruszać się po twojej witrynie."

#: includes/widgets/heading.php:464
msgid "Make sure your page is structured with accessibility in mind. Ally helps detect and fix common issues across your site."
msgstr "Zadbaj o to, aby twoja strona była zaprojektowana z myślą o dostępności. Ally pomaga wykrywać i naprawiać typowe problemy w witrynie."

#: core/utils/hints.php:505
msgid "Ally web accessibility"
msgstr "Dostępność sieci Ally"

#. translators: %s: Platform name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:270
msgid "Open %s"
msgstr "Otwórz %s"

#. translators: %s: Accessible name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:112
#: modules/floating-buttons/classes/render/floating-bars-core-render.php:112
msgid "Close %s"
msgstr "Zamknij %s"

#. translators: %s: Accessible name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:87
msgid "Toggle %s"
msgstr "Przełącz %s"

#. translators: %s: Theme name.
#: app/modules/import-export/runners/import/site-settings.php:155
msgid "Theme: %1$s is already used"
msgstr "Motyw: %1$s jest już używany"

#. translators: %s: Theme name.
#: app/modules/import-export/runners/import/site-settings.php:167
msgid "Theme: %1$s has already been installed and activated"
msgstr "Motyw: %1$s został zainstalowany i włączony"

#. translators: %s: Theme name.
#: app/modules/import-export/runners/import/site-settings.php:186
msgid "Theme: %1$s has been successfully installed"
msgstr "Motyw: %1$s został zainstalowany"

#. translators: %s: Theme name.
#: app/modules/import-export/runners/import/site-settings.php:178
msgid "Failed to install theme: %1$s"
msgstr "Nie udało się zainstalować motywu: %1$s"

#: modules/variables/classes/rest-api.php:389
msgid "Variable label already exists"
msgstr "Etykieta zmiennej już istnieje"

#. translators: %s: Maximum number of images allowed.
#: modules/link-in-bio/base/widget-link-in-bio-base.php:228
msgid "Add up to %s Images"
msgstr "Dodaj do %s obrazków"

#. translators: %s: Items limit.
#: modules/floating-buttons/base/widget-contact-button-base.php:766
msgid "Add up to %s contact buttons"
msgstr "Dodaj do %s przycisków kontaktowych"

#. translators: 1: Minimum items, 2: Items limit.
#: modules/floating-buttons/base/widget-contact-button-base.php:752
msgid "Add between %1$s to %2$s contact buttons"
msgstr "Dodaj od %1$s do %2$s przycisków kontaktowych"

#: includes/widgets/alert.php:240
msgid "Side Border Width"
msgstr "Szerokość obramowania bocznego"

#: includes/widgets/alert.php:229
msgid "Side Border Color"
msgstr "Kolor obramowania bocznego"

#. translators: %s: Maximum number of CTA links allowed.
#: modules/link-in-bio/base/widget-link-in-bio-base.php:316
msgid "Add up to %s CTA links"
msgstr "Dodaj do %s odnośników wezwaniem do działania"

#. translators: %s: Maximum number of icons allowed.
#: modules/link-in-bio/base/widget-link-in-bio-base.php:572
msgid "Add up to %s icons"
msgstr "Dodaj do %s ikonek"

#: app/modules/import-export-customization/module.php:149
msgid "Upload .zip file"
msgstr "Prześlij plik .zip"

#: app/app.php:289
msgid "Import/Export Customization"
msgstr "Dostosowywanie importu/eksportu"

#: app/modules/import-export-customization/module.php:146
msgid "Apply a Website Template"
msgstr "Zastosuj szablon witryny internetowej"

#: core/admin/admin-notices.php:494
msgid "Collecting leads is just the beginning. With Send by Elementor, you can manage contacts, launch automations, and turn form submissions into sales."
msgstr "Zbieranie potencjalnych klientów to dopiero początek. Dzięki Wyślij przez Elementor możesz zarządzać kontaktami, uruchamiać automatyzacje i przekształcać zgłoszenia z formularzy w sprzedaż."

#: core/admin/admin-notices.php:490
msgid "Turn leads into loyal shoppers"
msgstr "Zmień potencjalnych klientów w lojalnych klientów"

#: modules/cloud-kit-library/connect/cloud-kits.php:125
msgid "Failed to create kit: Content upload failed"
msgstr "Nie udało się utworzyć zestawu: Nie udało się przesłać treści"

#: modules/cloud-kit-library/connect/cloud-kits.php:117
msgid "Failed to create kit: No upload URL provided"
msgstr "Nie udało się utworzyć zestawu: Nie podano adresu URL przesyłania"

#: app/modules/import-export-customization/module.php:159
#: app/modules/import-export/module.php:164
msgid "Import from library"
msgstr "Importuj z biblioteki"

#: app/modules/import-export/module.php:151
msgid "Import website templates"
msgstr "Importuj szablony witryn internetowych"

#: app/modules/import-export-customization/module.php:231
#: app/modules/import-export/module.php:236
msgid "Remove Website Template"
msgstr "Usuń szablon witryny internetowej"

#: app/modules/import-export-customization/module.php:224
#: app/modules/import-export/module.php:229
msgid "Remove the most recent Website Template"
msgstr "Usuń najnowszy szablon witryny internetowej"

#: app/modules/import-export-customization/module.php:152
#: app/modules/import-export/module.php:157
msgid "You can import design and settings from a .zip file or choose from the library."
msgstr "Możesz zaimportować projekt i ustawienia z pliku .zip lub wybrać je z biblioteki."

#: app/modules/import-export-customization/module.php:143
#: app/modules/import-export/module.php:148
msgid "You can download this website as a .zip file, or upload it to the library."
msgstr "Możesz pobrać tę witrynę internetową jako plik .zip lub przesłać ją do biblioteki."

#: app/modules/import-export-customization/module.php:137
#: app/modules/import-export/module.php:142
msgid "Export this website"
msgstr "Eksportuj tę witrynę internetową"

#: modules/variables/classes/rest-api.php:404
msgid "Unexpected server error"
msgstr "Nieoczekiwany błąd serwera"

#: modules/variables/classes/rest-api.php:397
msgid "Variable not found"
msgstr "Zmienna nie została znaleziona"

#: modules/variables/classes/rest-api.php:381
msgid "Reached the maximum number of variables"
msgstr "Osiągnięto maksymalną liczbę zmiennych"

#. translators: %d: Maximum value length.
#: modules/variables/classes/rest-api.php:230
msgid "Value cannot exceed %d characters"
msgstr "Wartość nie może przekraczać %d znaków"

#: modules/variables/classes/rest-api.php:223
msgid "Value cannot be empty"
msgstr "Wartość nie może być pusta"

#. translators: %d: Maximum label length.
#: modules/variables/classes/rest-api.php:209
msgid "Label cannot exceed %d characters"
msgstr "Etykieta nie może przekraczać %d znaków"

#: modules/variables/classes/rest-api.php:202
msgid "Label cannot be empty"
msgstr "Etykieta nie może być pusta"

#. translators: %d: Maximum ID length.
#: modules/variables/classes/rest-api.php:182
msgid "ID cannot exceed %d characters"
msgstr "Identyfikator nie może przekraczać %d znaków"

#: modules/variables/classes/rest-api.php:175
msgid "ID cannot be empty"
msgstr "Identyfikator nie może być pusty"

#: modules/global-classes/module.php:66
msgid "Enforce global classes capabilities."
msgstr "Wymuś uprawnienia klas globalnych."

#: modules/global-classes/module.php:65
msgid "Enforce global classes capabilities"
msgstr "Wymuś uprawnienia klas globalnych"

#: modules/atomic-widgets/module.php:186
msgid "Enforce atomic widgets capabilities."
msgstr "Wymuś uprawnienia atomowych widżetów."

#: modules/atomic-widgets/module.php:185
msgid "Enforce atomic widgets capabilities"
msgstr "Wymuś uprawnienia atomowych widżetów"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:87
msgid "Related videos"
msgstr "Powiązane filmy"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:84
msgid "Player controls"
msgstr "Sterowanie odtwarzacza"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:83
msgid "Lazy load"
msgstr "Leniwe wczytywanie"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:79
msgid "End time"
msgstr "Czas zakończenia"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:78
msgid "Start time"
msgstr "Czas rozpoczęcia"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:76
msgid "YouTube URL"
msgstr "Adres URL YouTube"

#: includes/widgets/common-base.php:208
msgid "Hexagon Donut"
msgstr "Pączek sześciokątny"

#: includes/widgets/common-base.php:196
msgid "Trapezoid Down"
msgstr "Trapez dół"

#: includes/widgets/common-base.php:192
msgid "Trapezoid Up"
msgstr "Trapez góra"

#: includes/widgets/common-base.php:188
msgid "Parallelogram left"
msgstr "Równoległobok lewy"

#: includes/widgets/common-base.php:184
msgid "Parallelogram right"
msgstr "Równoległobok prawy"

#: includes/widgets/common-base.php:180
msgid "Octagon"
msgstr "Ośmiokąt"

#: includes/widgets/common-base.php:176
msgid "Heptagon"
msgstr "Siedmiokąt"

#: includes/widgets/common-base.php:172
msgid "Hexagon horizontal"
msgstr "Sześciokąt poziomy"

#: includes/widgets/common-base.php:168
msgid "Hexagon vertical"
msgstr "Sześciokąt pionowy"

#: includes/widgets/common-base.php:164
msgid "Pentagon"
msgstr "Pięciokąt"

#: includes/widgets/common-base.php:160
msgid "Diamond"
msgstr "Diament"

#: includes/widgets/common-base.php:152
msgid "Pill horizontal"
msgstr "Pigułka pozioma"

#: includes/widgets/common-base.php:148
msgid "Pill vertical"
msgstr "Pigułka pionowa"

#: includes/widgets/common-base.php:144
msgid "Oval horizontal"
msgstr "Owal poziomy"

#: includes/widgets/common-base.php:140
msgid "Oval vertical"
msgstr "Owal pionowy"

#: core/settings/editor-preferences/model.php:166
msgid "These will guide you through the first steps of creating your site."
msgstr "Pomogą Ci one wykonać pierwsze kroki w tworzeniu Twojej witryny."

#: core/settings/editor-preferences/model.php:161
msgid "Show launchpad checklist"
msgstr "Pokaż listę kontrolną platformy startowej"

#: core/common/modules/connect/rest/rest-api.php:163
msgid "Elementor Library app is not available."
msgstr "Aplikacja biblioteki Elementor jest niedostępna."

#: core/common/modules/connect/rest/rest-api.php:92
msgid "Failed to connect to Elementor Library."
msgstr "Nie udało się połączyć z biblioteką Elementor."

#: modules/cloud-kit-library/module.php:142
msgid "Cloud-Kits is not instantiated."
msgstr "Zestawy chmurowe nie są tworzone w formie instancji."

#: modules/cloud-kit-library/connect/cloud-kits.php:111
msgid "Failed to create kit: Invalid response"
msgstr "Nie udało się utworzyć zestawu: Nieprawidłowa odpowiedź"

#: modules/cloud-kit-library/connect/cloud-kits.php:17
msgid "Cloud Kits"
msgstr "Zestawy chmurowe"

#: core/admin/admin-notices.php:279
msgid "Opt in"
msgstr "Zgoda"

#: core/admin/admin-notices.php:276
msgid "Update regarding usage data collection"
msgstr "Aktualizacja dotycząca gromadzenia danych o użytkowaniu"

#: includes/settings/settings-page.php:396
msgid "Data Sharing"
msgstr "Udostępnianie danych"

#: core/admin/admin-notices.php:272
msgid "We're updating our Terms and Conditions to include the collection of usage and behavioral data. This information helps us understand how you use Elementor so we can make informed improvements to the product."
msgstr "Aktualizujemy warunki korzystania z usługi, aby uwzględnić zbieranie danych dotyczących użytkowania i zachowań. Informacje te pomagają nam zrozumieć, w jaki sposób korzystasz z Elementora, dzięki czemu możemy wprowadzać świadome ulepszenia produktu."

#: core/admin/admin-notices.php:234
msgid "Want to shape the future of web creation?"
msgstr "Chcesz mieć wpływ na przyszłość tworzenia stron internetowych?"

#: core/admin/admin-notices.php:218 includes/settings/settings-page.php:403
msgid "Become a super contributor by helping us understand how you use our service to enhance your experience and improve our product."
msgstr "Zostań naszym superwspółpracownikiem, pomagając nam zrozumieć, w jaki sposób korzystasz z naszych usług, aby zwiększyć wygodę korzystania i udoskonalić nasz produkt."

#: app/modules/import-export-customization/module.php:201
#: app/modules/import-export/module.php:206
msgid "Here’s where you can export this website as a .zip file, upload it to the cloud, or start the process of applying an existing template to your site."
msgstr "Tutaj możesz wyeksportować tę witrynę jako plik .zip, przesłać ją do chmury lub rozpocząć proces stosowania istniejącego szablonu do swojej witryny."

#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:81
#: modules/atomic-widgets/elements/atomic-divider/atomic-divider.php:69
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:118
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:89
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:81
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:75
#: modules/atomic-widgets/elements/atomic-tabs/atomic-tab-link.php:59
#: modules/atomic-widgets/elements/atomic-tabs/atomic-tab-list.php:58
#: modules/atomic-widgets/elements/atomic-tabs/atomic-tabs-content.php:59
#: modules/atomic-widgets/elements/atomic-tabs/atomic-tabs.php:55
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:99
#: modules/atomic-widgets/elements/div-block/div-block.php:105
#: modules/atomic-widgets/elements/flexbox/flexbox.php:105
msgid "ID"
msgstr "Identyfikator"

#: includes/widgets/common-base.php:222
msgid "Custom Mask"
msgstr "Maska własna"

#: modules/atomic-widgets/module.php:177
msgid "V4 Indications Popover"
msgstr "Okno dialogowe wskazań V4"

#: modules/atomic-widgets/module.php:178
msgid "Enable V4 Indication Popovers"
msgstr "Włącz okno dialogowe wskazań V4"

#: app/modules/import-export-customization/module.php:116
#: app/modules/import-export-customization/module.php:119
#: app/modules/import-export/module.php:121
#: app/modules/import-export/module.php:124
#: app/modules/kit-library/kit-library-menu-item.php:22
#: app/modules/kit-library/module.php:35 app/modules/kit-library/module.php:36
#: core/common/modules/finder/categories/general.php:78
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:1600
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:3793
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:4563
msgid "Website Templates"
msgstr "Szablony witryn internetowych"

#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:81
msgid "Image resolution"
msgstr "Rozdzielczość obrazka"

#: app/modules/kit-library/module.php:134
#: core/frontend/render-mode-manager.php:152
msgid "Not Authorized"
msgstr "Brak autoryzacji"

#: includes/editor-templates/templates.php:113
#: includes/editor-templates/templates.php:612
msgid "Site templates"
msgstr "Szablony witryny"

#: includes/editor-templates/templates.php:140
msgid "Grid view"
msgstr "Widok siatki"

#: includes/controls/url.php:68
#: modules/atomic-widgets/controls/types/link-control.php:23
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:75
msgid "Type or paste your URL"
msgstr "Wpisz lub wklej twój adres URL"

#: modules/variables/module.php:29
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:11
#: assets/js/packages/editor-variables/editor-variables.strings.js:14
msgid "Variables"
msgstr "Zmienne"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:54
msgid "This is a title"
msgstr "To jest tytuł"

#: includes/widgets/progress.php:154
msgid "Display Title"
msgstr "Wyświetl tytuł"

#: includes/template-library/sources/cloud.php:305
msgid "You do not have permission to create preview documents."
msgstr "Brak uprawnień do tworzenia podglądu dokumentów."

#: modules/atomic-widgets/opt-in.php:42
msgid "Enable Editor V4."
msgstr "Włącz edytor V4."

#: modules/promotions/pointers/birthday.php:35
msgid "View Deals"
msgstr "Zobacz oferty"

#: modules/promotions/pointers/birthday.php:31
msgid "Celebrate Elementor’s birthday with us—exclusive deals are available now."
msgstr "Świętuj z nami urodziny Elementora — ekskluzywne oferty są już dostępne."

#: modules/promotions/pointers/birthday.php:30
msgid "Elementor’s 9th Birthday sale!"
msgstr "Wyprzedaż z okazji 9. urodzin Elementora!"

#: includes/editor-templates/templates.php:267
#: includes/editor-templates/templates.php:370
msgid "Upgrade to get more storage space or delete old templates to make room."
msgstr "Dokonaj aktualizacji, aby uzyskać więcej miejsca do przechowywania lub usuń stare szablony, aby zrobić miejsce."

#. translators: %d: Maximum allowed items.
#: modules/global-classes/global-classes-rest-api.php:177
msgid "Global classes limit exceeded. Maximum allowed: %d"
msgstr "Przekroczono limit klas globalnych. Maksymalnie dozwolone: ​​%d"

#: modules/cloud-library/module.php:146 assets/js/editor.js:9855
msgid "Then you can find all your templates in one convenient library."
msgstr "Następnie wszystkie szablony znajdziesz w jednej wygodnej bibliotece."

#: modules/cloud-library/module.php:145 assets/js/editor.js:9854
msgid "Connect to your Elementor account"
msgstr "Połącz się ze swoim kontem Elementor"

#: modules/cloud-library/documents/cloud-template-preview.php:40
msgid "Cloud Template Preview"
msgstr "Podgląd szablonu chmury"

#: modules/cloud-library/documents/cloud-template-preview.php:44
msgid "Cloud Template Previews"
msgstr "Podglądy szablonów chmury"

#: modules/cloud-library/connect/cloud-library.php:221
msgid "Failed to mark preview as failed."
msgstr "Nie udało się oznaczyć podglądu jako nieudanego."

#: modules/cloud-library/connect/cloud-library.php:199
msgid "Failed to save preview."
msgstr "Nie udało się zapisać podglądu."

#: modules/atomic-opt-in/module.php:23
msgid "Enable the settings Opt In page"
msgstr "Włącz stronę ustawień zgody"

#: modules/atomic-opt-in/module.php:22
msgid "Editor v4 (Opt In Page)"
msgstr "Edytor v4 (Strona zgody)"

#: includes/editor-templates/templates.php:533
#: includes/editor-templates/templates.php:549
#: includes/editor-templates/templates.php:563
msgid "Learn more about the"
msgstr "Dowiedz się więcej o"

#. translators: %s is the "Upgrade now" link
#: includes/editor-templates/templates.php:518
msgid "To get more space %s"
msgstr "Aby uzyskać więcej miejsca %s"

#: includes/editor-templates/templates.php:514
msgid "You’ve saved 100% of the templates in your plan."
msgstr "Zapisałeś 100% szablonów w swoim planie."

#: includes/editor-templates/templates.php:508
msgid "Site Templates"
msgstr "Szablony witryny"

#: includes/editor-templates/templates.php:480
msgid "Cloud Templates"
msgstr "Szablony chmury"

#: includes/editor-templates/templates.php:473
msgid "Give your template a name"
msgstr "Nadaj swojemu szablonowi nazwę"

#: includes/editor-templates/templates.php:334
#: modules/cloud-library/module.php:75
msgid "Folder"
msgstr "Katalog"

#: includes/editor-templates/templates.php:310
#: includes/editor-templates/templates.php:395
msgid "Copy to"
msgstr "Kopiuj do"

#: includes/editor-templates/templates.php:161 assets/js/editor.js:8576
#: assets/js/editor.js:8589
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:108
#: assets/js/packages/editor-controls/editor-controls.strings.js:113
msgid "Move"
msgstr "Przesuń"

#: includes/editor-templates/templates.php:306
#: includes/editor-templates/templates.php:391
msgid "Move to"
msgstr "Przesuń do"

#: includes/editor-templates/templates.php:144
msgid "List view"
msgstr "Widok listy"

#: includes/editor-templates/templates.php:117
#: includes/editor-templates/templates.php:616
msgid "Cloud templates"
msgstr "Szablony chmury"

#: core/admin/admin-notices.php:537
msgid "Create a more inclusive site experience for all your visitors. With Ally, it's easy to add your statement page in just a few clicks."
msgstr "Stwórz bardziej inkluzywne doświadczenie witryny dla wszystkich odwiedzających. Dzięki Ally możesz łatwo dodać swoją stronę oświadczenia za pomocą kilku kliknięć."

#: core/admin/admin-notices.php:536
msgid "Make sure your site has an accessibility statement page"
msgstr "Upewnij się, że Twoja witryna ma stronę z oświadczeniem o dostępności"

#: modules/variables/module.php:30
msgid "Enable variables. (For this feature to work - Atomic Widgets must be active)"
msgstr "Włącz zmienne. (Aby ta funkcja działała - widżety Atomic muszą być włączone)"

#: includes/managers/elements.php:280
msgid "Atomic Elements"
msgstr "Elementy Atomic"

#: includes/controls/gallery.php:126 includes/controls/media.php:322
msgid "This image isn't optimized. You need to connect your Image Optimizer account first."
msgstr "Obrazek nie został zoptymalizowany. Musisz najpierw podłączyć twoje konto Optymalizatora Obrazków."

#: includes/settings/tools.php:317 modules/admin-bar/module.php:148
msgid "Clear Files & Data"
msgstr "Wyczyść pliki i dane"

#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:33
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:67
msgid "Paragraph"
msgstr "Akapit"

#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:67
msgid "Button text"
msgstr "Tekst przycisku"

#: includes/template-library/sources/cloud.php:122
msgid "New Folder"
msgstr "Nowy katalog"

#: includes/editor-templates/templates.php:287
msgid "Open"
msgstr "Otwórz"

#: includes/controls/gallery.php:127 includes/controls/media.php:323
msgid "Connect Now"
msgstr "Połącz teraz"

#: includes/template-library/sources/cloud.php:36
#: modules/cloud-library/connect/cloud-library.php:15
#: modules/cloud-library/module.php:86
msgid "Cloud Library"
msgstr "Biblioteka w chmurze"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/floating-buttons/module.php:352 modules/landing-pages/module.php:236
msgid "Or view %1$sTrashed Items%2$s"
msgstr "Lub przeglądaj %1$sElementy usunięte%2$s"

#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:50
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:66
msgid "Type your paragraph here"
msgstr "Wpisz tutaj swój akapit"

#: includes/template-library/sources/cloud.php:23
msgid "Cloud-Library is not instantiated."
msgstr "Biblioteka w chmurze nie jest tworzona."

#: includes/settings/tools.php:318
msgid "Clear outdated CSS files and cached data in the database (rendered HTML, JS/CSS assets, etc.). We'll regenerate those files the next time someone visits any page on your website."
msgstr "Wyczyść nieaktualne pliki CSS i dane z pamięci podręcznej w bazie danych (wyrenderowany HTML, zasoby JS/CSS itp.). Ponownie wygenerujemy te pliki następnym razem, gdy ktoś odwiedzi dowolną stronę w Twojej witrynie."

#: includes/settings/tools.php:314
msgid "Elementor Cache"
msgstr "Pamięć podręczna Elementora"

#: includes/controls/media.php:307
msgid "Image size settings don’t apply to Dynamic Images."
msgstr "Ustawienia rozmiaru obrazka nie mają zastosowania do obrazków dynamicznych."

#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:66
msgid "Type your button text here"
msgstr "Wpisz tutaj tekst swojego przycisku"

#: includes/editor-templates/templates.php:135
msgid "Create a New Folder"
msgstr "Utwórz nowy katalog"

#: includes/managers/elements.php:298
msgid "Hello+"
msgstr "Hello+"

#: includes/template-library/sources/local.php:1766
msgid "Sorry, you are not allowed to do that."
msgstr "Brak uprawnień żeby to zrobić."

#: includes/widgets/video.php:438
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:85
msgid "Captions"
msgstr "Podpisy"

#: modules/ai/module.php:225 modules/ai/module.php:260
msgid "Animate With AI"
msgstr "Animuj przy użyciu SI"

#: includes/widgets/image-gallery.php:303
msgid "Custom Gap"
msgstr "Własny odstęp"

#: modules/element-cache/module.php:124
msgid "Element Cache"
msgstr "Pamięć podręczna elementu"

#: modules/atomic-widgets/elements/div-block/div-block.php:34
#: modules/atomic-widgets/library/div-block.php:54
msgid "Div Block"
msgstr "Blok DIV"

#: modules/ai/site-planner-connect/module.php:51
msgid "Approve & Connect"
msgstr "Zatwierdź i połącz"

#: modules/ai/site-planner-connect/module.php:50
msgid "To connect your site to Site Planner, you need to generate an app password."
msgstr "Aby połączyć swoją witrynę z narzędziem Planista witryny, musisz wygenerować hasło aplikacji."

#: modules/ai/site-planner-connect/module.php:49
msgid "Connect to Site Planner"
msgstr "Połącz z Planistą witryny"

#: includes/elements/container.php:1403 includes/widgets/common-base.php:381
msgid "Grid Item"
msgstr "Element siatki"

#: includes/settings/settings.php:471
msgid "Load Google Fonts Locally"
msgstr "Wczytaj kroje pisma Google lokalnie"

#: includes/elements/container.php:1455 includes/widgets/common-base.php:433
msgid "Row Span"
msgstr "Odstęp wiersza"

#: includes/elements/container.php:1411 includes/widgets/common-base.php:389
msgid "Column Span"
msgstr "Odstęp kolumny"

#: modules/nested-tabs/widgets/nested-tabs.php:187
msgid "Add Tab"
msgstr "Dodaj kartę"

#: modules/promotions/promotion-data.php:110
msgid "Upgrade Your Testimonials"
msgstr "Aktualizuj swoje referencje"

#: modules/promotions/promotion-data.php:78
msgid "Combine text, buttons, and images."
msgstr "Łącz tekst, przyciski i obrazki."

#: modules/ai/module.php:417
msgid "Image added successfully"
msgstr "Dodano obrazek"

#: includes/widgets/image-carousel.php:664
msgid "Space Between Dots"
msgstr "Przerwa między kropkami"

#: includes/widgets/image-carousel.php:153
msgid "Carousel Name"
msgstr "Nazwa karuzeli"

#: modules/promotions/promotion-data.php:46
msgid "Fully customize your headlines."
msgstr "W pełni dostosuj swoje nagłówki."

#: modules/promotions/promotion-data.php:45
msgid "Apply rotating effects to text."
msgstr "Zastosuj efekt obracania tekstu."

#: modules/promotions/promotion-data.php:80
msgid "Create unique, interactive designs."
msgstr "Twórz kreatywne, unikatowe projekty."

#: modules/promotions/promotion-data.php:97
msgid "Showcase multiple items with style."
msgstr "Zaprezentuj wiele elementów w stylowy sposób."

#: modules/promotions/promotion-data.php:96
msgid "Adjust transitions and animations."
msgstr "Dostosuj przejścia i animacje."

#: modules/promotions/promotion-data.php:79
msgid "Add hover animations and CSS effects."
msgstr "Dodaj animacje najechania i efekty CSS."

#: modules/ai/feature-intro/product-image-unification-intro.php:35
msgid "Now you can process images in bulk and standardized the background and ratio - no manual editing required!"
msgstr "Teraz możesz przetwarzać obrazki hurtowo, ujednolicając tło i proporcje - bez konieczności ręcznej edycji!"

#: modules/promotions/promotion-data.php:114
msgid "Customize layouts for visual appeal."
msgstr "Dostosuj układy pod kątem atrakcyjności wizualnej."

#: modules/promotions/promotion-data.php:113
msgid "Boost credibility with dynamic testimonials."
msgstr "Zwiększ wiarygodność dzięki dynamicznym referencjom."

#: modules/promotions/promotion-data.php:112
msgid "Display reviews in a rotating carousel."
msgstr "Wyświetlaj recenzje w obrotowej karuzeli."

#: modules/promotions/promotion-data.php:76
msgid "Boost Conversions with CTAs"
msgstr "Zwiększ konwersje za pomocą wezwań do działania"

#: modules/promotions/promotion-data.php:63
msgid "Seamlessly customize video appearance."
msgstr "Bezproblemowa personalizacja wyglądu filmu."

#: modules/promotions/promotion-data.php:62
msgid "Adjust layout and playback settings."
msgstr "Dostosuj układ i ustawienia odtwarzania."

#: modules/promotions/promotion-data.php:61
msgid "Embed videos with full control."
msgstr "Osadzaj filmy z pełną kontrolą."

#: modules/promotions/promotion-data.php:59
msgid "Showcase Video Playlists"
msgstr "Wyświetl listy odtwarzania filmowe"

#: modules/promotions/promotion-data.php:44
msgid "Highlight key messages dynamically."
msgstr "Dynamicznie wyróżniaj kluczowe wiadomości."

#: modules/promotions/promotion-data.php:42
msgid "Bring Headlines to Life"
msgstr "Ożyw nagłówki"

#: modules/global-classes/module.php:57
msgid "Enable global CSS classes."
msgstr "Włącz globalne klasy CSS."

#: modules/global-classes/module.php:56
msgid "Global Classes"
msgstr "Klasy globalne"

#: modules/cloud-library/connect/cloud-library.php:340
msgid "Not connected"
msgstr "Nie połączono"

#: modules/ai/module.php:321 assets/js/ai-unify-product-images.js:16716
msgid "Unify with Elementor AI"
msgstr "Zjednocz się z Elementor SI"

#: modules/ai/feature-intro/product-image-unification-intro.php:34
msgid "New! Unify pack-shots with Elementor AI"
msgstr "Nowość! Ujednolicenie zdjęć pakietowych z Elementor SI"

#: modules/promotions/promotion-data.php:93
msgid "Design Custom Carousels"
msgstr "Projektuj własne karuzele"

#: modules/promotions/promotion-data.php:95
msgid "Create flexible custom carousels."
msgstr "Twórz elastyczne, własne karuzele."

#: core/admin/admin-notices.php:611
msgid "Use Elementor's Site Mailer to ensure your store emails like purchase confirmations, shipping updates and more are reliably delivered."
msgstr "Użyj modułu Site Mailer firmy Elementor, aby mieć pewność, że wiadomości e-mail ze sklepu, takie jak potwierdzenia zakupów, aktualizacje dotyczące wysyłki i inne, będą niezawodnie dostarczane."

#: core/admin/admin-notices.php:610
msgid "Improve Transactional Email Deliverability"
msgstr "Poprawa dostarczalności wiadomości e-mail transakcyjnych"

#: core/admin/admin-notices.php:589
msgid "Ensure your form emails avoid the spam folder!"
msgstr "Upewnij się, że e-maile z formularza nie trafiły do folderu ze spamem!"

#: core/admin/admin-notices.php:590
msgid "Use Site Mailer for improved email deliverability, detailed email logs, and an easy setup."
msgstr "Użyj Site Mailer, aby poprawić dostarczalność wiadomości e-mail, uzyskać szczegółowe dzienniki wiadomości e-mail i ułatwić konfigurację."

#: modules/checklist/steps/setup-header.php:70
msgid "Add a header"
msgstr "Dodaj nagłówek"

#: modules/checklist/steps/setup-header.php:62
msgid "Set up a header"
msgstr "Ustaw nagłówek"

#: modules/checklist/steps/add-logo.php:25
msgid "Add your logo"
msgstr "Dodaj twój logotyp"

#: modules/checklist/steps/setup-header.php:66
msgid "This element applies across different pages, so visitors can easily navigate around your site."
msgstr "Ten element ma zastosowanie na różnych stronach, dzięki czemu odwiedzający mogą łatwo poruszać się po Twojej witrynie."

#: modules/checklist/steps/set-fonts-and-colors.php:31
msgid "Global colors and fonts ensure a cohesive look across your site. Start by defining one color and one font."
msgstr "Globalne kolory i kroje pisma zapewniają spójny wygląd całej witryny. Zacznij od zdefiniowania jednego koloru i jednego kroju pisma."

#: modules/checklist/steps/set-fonts-and-colors.php:27
msgid "Set up your Global Fonts & Colors"
msgstr "Skonfiguruj swoje globalne kroje pisma i kolory"

#: modules/checklist/steps/assign-homepage.php:31
msgid "Assign homepage"
msgstr "Przypisz stronę główną"

#: modules/checklist/steps/assign-homepage.php:27
msgid "Before your launch, make sure to assign a homepage so visitors have a clear entry point into your site."
msgstr "Przed uruchomieniem witryny upewnij się, że ma ona stronę główną, dzięki czemu odwiedzający będą mieli łatwy dostęp do witryny."

#: modules/checklist/steps/assign-homepage.php:23
msgid "Assign a homepage"
msgstr "Przypisz stronę główną"

#: modules/checklist/steps/add-logo.php:33
#: modules/checklist/steps/set-fonts-and-colors.php:35
msgid "Go to Site Identity"
msgstr "Przejdź do tożsamości witryny"

#: modules/checklist/steps/add-logo.php:29
msgid "Let's start by adding your logo and filling in the site identity settings. This will establish your initial presence and also improve SEO."
msgstr "Zacznijmy od dodania logo i uzupełnienia ustawień tożsamości witryny. To ustanowi Twoją początkową obecność i również poprawi SEO."

#. translators: 1: Link opening tag, 2: Link closing tag, 3: Link opening tag,
#. 4: Link closing tag, 5: Link opening tag, 6: Link closing tag
#: core/experiments/manager.php:345
msgid "Create advanced layouts and responsive designs with %1$sFlexbox%2$s and %3$sGrid%4$s container elements. Give it a try using the %5$sContainer playground%6$s."
msgstr "Twórz zaawansowane układy i responsywne projekty za pomocą elementów kontenerowych %1$sElastyczne pole%2$s i %3$sSiatka%4$s. Wypróbuj to, używając %5$sPlac zabaw w kontenerze%6$s."

#: modules/floating-buttons/base/widget-floating-bars-base.php:1049
msgid "Horizontal position"
msgstr "Pozycja pozioma"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1183
msgid "Align Elements"
msgstr "Wyrównanie elementów"

#: modules/floating-buttons/base/widget-floating-bars-base.php:339
msgid "Enter your text"
msgstr "Wpisz twój tekst"

#: modules/floating-buttons/base/widget-floating-bars-base.php:315
msgid "Headlines"
msgstr "Nagłówki"

#: modules/floating-buttons/base/widget-floating-bars-base.php:228
#: modules/floating-buttons/base/widget-floating-bars-base.php:1130
msgid "Pause and Play"
msgstr "Pauza i odtwarzanie"

#: modules/floating-buttons/base/widget-floating-bars-base.php:219
#: modules/floating-buttons/base/widget-floating-bars-base.php:1173
msgid "Floating Bar"
msgstr "Pływający pasek"

#: modules/floating-buttons/base/widget-floating-bars-base.php:156
#: modules/floating-buttons/base/widget-floating-bars-base.php:204
msgid "Enter text"
msgstr "Wpisz tekst"

#: modules/ai/preferences.php:61
msgid "Elementor - AI"
msgstr "Elementor - SI"

#: modules/ai/preferences.php:73
msgid "Enable Elementor AI functionality"
msgstr "Włącz funkcjonalność Elementor SI"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1259
msgid "Headline"
msgstr "Nagłówek"

#: modules/floating-buttons/base/widget-floating-bars-base.php:131
msgid "Enter your text here"
msgstr "Wpisz swój tekst"

#: modules/floating-buttons/base/widget-floating-bars-base.php:105
#: modules/floating-buttons/base/widget-floating-bars-base.php:391
msgid "Announcement"
msgstr "Ogłoszenie"

#: modules/floating-buttons/base/widget-floating-bars-base.php:69
msgid "Banner"
msgstr "Baner"

#: modules/checklist/steps/create-pages.php:36
msgid "Create a new page"
msgstr "Utwórz stronę"

#: modules/checklist/steps/create-pages.php:28
msgid "Create your first 3 pages"
msgstr "Utwórz swoje 3  pierwsze strony"

#: modules/floating-buttons/base/widget-floating-bars-base.php:157
msgid "Shop now"
msgstr "Kup teraz"

#: modules/floating-buttons/admin-menu-items/floating-buttons-menu-item.php:22
#: modules/floating-buttons/admin-menu-items/floating-buttons-menu-item.php:26
#: modules/floating-buttons/documents/floating-buttons.php:207
msgid "Floating Elements"
msgstr "Pływające elementy"

#: modules/floating-buttons/module.php:47 assets/js/editor.js:50955
msgid "Floating Bars"
msgstr "Pływające paski"

#: modules/floating-buttons/documents/floating-buttons.php:203
#: modules/floating-buttons/module.php:342
msgid "Floating Element"
msgstr "Pływający element"

#: includes/admin-templates/new-floating-elements.php:31
msgid "Choose Floating Element"
msgstr "Wybierz element pływający"

#: includes/admin-templates/new-floating-elements.php:50
msgid "Create Floating Element"
msgstr "Utwórz element pływający"

#: modules/floating-buttons/base/widget-floating-bars-base.php:199
msgid "Accessible Name"
msgstr "Dostępna nazwa"

#: modules/floating-buttons/base/widget-floating-bars-base.php:64
msgid "Just in! Cool summer tees"
msgstr "Nowość! Fajne letnie koszulki"

#: modules/floating-buttons/base/widget-contact-button-base.php:466
#: modules/floating-buttons/base/widget-contact-button-base.php:722
msgid "Add accessible name"
msgstr "Dodaj dostępną nazwę"

#: modules/floating-buttons/base/widget-contact-button-base.php:463
#: modules/floating-buttons/base/widget-contact-button-base.php:719
msgid "Accessible name"
msgstr "Dostępna nazwa"

#: modules/checklist/steps/create-pages.php:32
msgid "Jumpstart your creation with professional designs from the Template Library or start from scratch."
msgstr "Rozpocznij tworzenie swoich prac dzięki profesjonalnym projektom z biblioteki szablonów lub zacznij od zera."

#: includes/settings/settings.php:467
msgid "Improve initial page load performance by lazy loading all background images except the first one."
msgstr "Popraw wydajność początkowego wczytywania strony, leniwie wczytuj wszystkie obrazki tła oprócz pierwszego."

#: includes/settings/settings.php:336
msgid "Personalize the way Elementor works on your website by choosing the advanced features and how they operate."
msgstr "Spersonalizuj sposób działania Elementora na swojej witrynie internetowej, wybierając zaawansowane funkcje i sposób ich działania."

#: includes/settings/settings.php:259
msgid "Tailor how Elementor enhances your site, from post types to other functions."
msgstr "Dostosuj sposób, w jaki Elementor udoskonala Twoją witrynę – od typów wpisów po inne funkcje."

#: includes/admin-templates/new-floating-elements.php:21
msgid "Use floating elements to engage your visitors and increase conversions."
msgstr "Użyj elementów pływających, aby przyciągnąć uwagę odwiedzających i zwiększyć konwersję."

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-floating-elements.php:16
msgid "Floating Elements Help You %1$sWork Efficiently%2$s"
msgstr "Elementy pływające pomagają Ci %1$spracować wydajniej%2$s"

#: core/debug/classes/shop-page-edit.php:23
msgid "Sorry, The content area was not been found on your page"
msgstr "Przepraszamy, obszar treści nie został znaleziony na Twojej stronie"

#: core/debug/classes/shop-page-edit.php:15
msgid "You are trying to edit the Shop Page although it is a Product Archive. Use the Theme Builder to create your Shop Archive template instead"
msgstr "Próbujesz edytować stronę sklepu, mimo że jest to archiwum produktów. Użyj Kreatora motywów, aby zamiast tego utworzyć szablon archiwum sklepu"

#: modules/floating-buttons/widgets/floating-bars-var-1.php:25
msgid "Floating Bar CTA"
msgstr "Pływający pasek wezwania do działania"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:111
msgid "Tag"
msgstr "Znacznik"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1213
msgid "Element spacing"
msgstr "Odstęp elementu"

#: modules/floating-buttons/base/widget-floating-bars-base.php:143
#: modules/floating-buttons/base/widget-floating-bars-base.php:555
msgid "CTA Button"
msgstr "Przycisk wezwania do działania"

#: modules/atomic-widgets/module.php:167
msgid "Enable atomic widgets."
msgstr "Włącz widżety atomic."

#: modules/atomic-widgets/module.php:166
msgid "Atomic Widgets"
msgstr "Widżety atomic"

#: modules/floating-buttons/base/widget-floating-bars-base.php:240
msgid "Pause Icon"
msgstr "Ikonka pauzy"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1545
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:140
msgid "Dimensions"
msgstr "Wymiary"

#: modules/link-in-bio/widgets/link-in-bio.php:28
msgid "Minimalist"
msgstr "Minimalistyczny"

#: modules/floating-buttons/module.php:277
msgid "Entire Site"
msgstr "Cała witryna"

#: modules/floating-buttons/module.php:191
msgid "Instances"
msgstr "Instancje"

#: modules/floating-buttons/module.php:188
msgid "Click Tracking"
msgstr "Śledzenie kliknięć"

#: modules/floating-buttons/widgets/contact-buttons.php:26
msgid "Single Chat"
msgstr "Pojedynczy czat"

#: modules/floating-buttons/module.php:344
msgid "Add a Floating element so your users can easily get in touch!"
msgstr "Dodaj element pływający, aby użytkownicy mogli łatwo nawiązać kontakt!"

#: modules/floating-buttons/documents/floating-buttons.php:195
msgid "Set as Entire Site"
msgstr "Ustaw jako całą witrynę"

#: modules/floating-buttons/documents/floating-buttons.php:188
msgid "Remove From Entire Site"
msgstr "Usuń z całej witryny"

#: modules/floating-buttons/documents/floating-buttons.php:32
msgid "After publishing this widget, you will be able to set it as visible on the entire site in the Admin Table."
msgstr "Po opublikowaniu tego widżetu będziesz mógł ustawić go jako widoczny na całej witrynie w tabeli administratora."

#: modules/link-in-bio/base/widget-link-in-bio-base.php:334
msgid "CTA link"
msgstr "Odnośnik wezwania do działania"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:285
#: assets/js/packages/editor-controls/editor-controls.strings.js:65
msgid "Add item"
msgstr "Dodaj element"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:243
msgid "Images Per Row"
msgstr "Obrazków na wiersz"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:215
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1026
msgid "Image Links"
msgstr "Odnośniki obrazka"

#. translators: 1: Plural label.
#: core/document-types/page-base.php:183
msgid "All %s"
msgstr "Wszystkie %s"

#: modules/floating-buttons/base/widget-contact-button-base.php:3082
#: modules/floating-buttons/base/widget-floating-bars-base.php:1488
msgid "CSS"
msgstr "CSS"

#: modules/floating-buttons/base/widget-contact-button-base.php:813
msgid "Enter title"
msgstr "Wpisz tytuł"

#. translators: %s: Singular label.
#: core/document-types/page-base.php:199
msgid "New %s"
msgstr "Nowy %s"

#. translators: %s: Plural label.
#: core/document-types/page-base.php:219
msgid "No %s found in Trash."
msgstr "Nie znaleziono %s w koszu."

#. translators: %s: Plural label.
#: core/document-types/page-base.php:214
msgid "No %s found."
msgstr "Nie znaleziono %s."

#. translators: %s: Plural label.
#: core/document-types/page-base.php:209
msgid "Search %s"
msgstr "Szukaj %s"

#: modules/floating-buttons/base/widget-contact-button-base.php:146
msgid "Tooltip"
msgstr "Podpowiedź"

#: modules/floating-buttons/base/widget-contact-button-base.php:2107
msgid "Tooltips"
msgstr "Podpowiedzi"

#: modules/floating-buttons/base/widget-contact-button-base.php:552
msgid "Enter the text"
msgstr "Wpisz tekst"

#: modules/announcements/module.php:110
msgid "Discover your new superpowers "
msgstr "Odkryj swoje nowe supermoce "

#: modules/floating-buttons/base/widget-contact-button-base.php:532
msgid "Display Text"
msgstr "Wyświetl tekst"

#: modules/floating-buttons/base/widget-contact-button-base.php:691
msgid "Typing Animation"
msgstr "Animacja pisania"

#: modules/floating-buttons/base/widget-contact-button-base.php:736
msgid "Start conversation:"
msgstr "Rozpocznij rozmowę:"

#: modules/floating-buttons/base/widget-contact-button-base.php:3045
msgid "Full Width on Mobile"
msgstr "Pełna szerokość na urządzeniach mobilnych"

#: includes/editor-templates/panel.php:136
msgid "Copy and Share Link"
msgstr "Kopiuj i udostępnij odnośnik"

#: modules/floating-buttons/base/widget-contact-button-base.php:2440
msgid "Link Spacing"
msgstr "Odstęp między odnośnikami"

#: modules/floating-buttons/classes/render/contact-buttons-core-render.php:58
msgid "Links window"
msgstr "Okno odnośników"

#: modules/floating-buttons/base/widget-contact-button-base.php:3069
#: modules/floating-buttons/base/widget-floating-bars-base.php:1475
msgid "Responsive visibility will take effect only on preview mode or live page, and not while editing in Elementor."
msgstr "Widoczność responsywna będzie obowiązywać tylko w trybie podglądu lub na żywo, a nie podczas edycji w Elementorze."

#: modules/floating-buttons/base/widget-contact-button-base.php:2390
msgid "Info Links"
msgstr "Odnośniki informacyjne"

#: modules/floating-buttons/base/widget-contact-button-base.php:2256
msgid "Resource Links"
msgstr "Odnośniki zasobów"

#: modules/floating-buttons/base/widget-contact-button-base.php:2152
msgid "Button Bar"
msgstr "Pasek przycisku"

#: modules/floating-buttons/base/widget-contact-button-base.php:2056
msgid "Buttons Spacing"
msgstr "Odstępy przycisków"

#: modules/floating-buttons/base/widget-contact-button-base.php:1830
msgid "Bubble Background Color"
msgstr "Kolor tła bąbelka"

#: modules/floating-buttons/base/widget-contact-button-base.php:1487
msgid "Hover animation is <b>desktop only</b>"
msgstr "Animacja najechania jest dostępna <b>tylko na komputerze stacjonarnym</b>"

#: modules/floating-buttons/base/widget-contact-button-base.php:516
msgid "Notification Dot"
msgstr "Kropka powiadomień"

#: modules/floating-buttons/base/widget-contact-button-base.php:626
msgid "Active Dot"
msgstr "Aktywna kropka"

#: modules/floating-buttons/base/widget-contact-button-base.php:405
#: modules/floating-buttons/base/widget-contact-button-base.php:967
#: modules/link-in-bio/base/widget-link-in-bio-base.php:516
#: modules/link-in-bio/base/widget-link-in-bio-base.php:762
msgid "Paste Waze link"
msgstr "Wklej odnośnik Waze"

#: modules/floating-buttons/module.php:46 assets/js/editor.js:50957
#: assets/js/editor.js:50959
msgid "Floating Buttons"
msgstr "Pływające przyciski"

#: core/common/modules/events-manager/module.php:50
msgid "Editor events processing"
msgstr "Przetwarzanie zdarzeń edytora"

#: core/common/modules/events-manager/module.php:49
msgid "Elementor Editor Events"
msgstr "Zdarzenia edytora Elementor"

#. translators: 1: `<a>` opening tag, 2: `</a>` closing tag.
#: includes/widgets/video.php:364
msgid "Note: Autoplay is affected by %1$s Google’s Autoplay policy %2$s on Chrome browsers."
msgstr "Uwaga: Na automatyczne odtwarzanie ma wpływ %1$s polityka automatycznego odtwarzania Google %2$s w przeglądarkach Chrome."

#. translators: %s: <head> tag.
#: includes/settings/settings.php:424
msgid "Internal Embedding places all CSS in the %s which works great for troubleshooting, while External File uses external CSS file for better performance (recommended)."
msgstr "Opcja Wewnętrzne osadzanie umieszcza cały kod CSS w %s, co świetnie sprawdza się przy rozwiązywaniu problemów, natomiast opcja Zewnętrzny plik wykorzystuje zewnętrzny plik CSS w celu zapewnienia lepszej wydajności (zalecane)."

#: includes/controls/repeater.php:194
msgid "In a Repeater control, if you specify a minimum number of items, you must also specify a default value that contains at least that number of items."
msgstr "W przypadku kontrolki powtarzalnej, jeśli określisz minimalną liczbę elementów, musisz także określić wartość domyślną zawierającą co najmniej tę liczbę elementów."

#: core/kits/documents/tabs/settings-background.php:75
msgid "Overscroll Behavior"
msgstr "Zachowanie podczas przewijania"

#: modules/floating-buttons/base/widget-contact-button-base.php:546
#: modules/floating-buttons/base/widget-contact-button-base.php:734
#: modules/floating-buttons/base/widget-contact-button-base.php:1876
msgid "Call to Action Text"
msgstr "Tekst wezwanie do działania"

#: modules/floating-buttons/base/widget-contact-button-base.php:537
msgid "Call to Action"
msgstr "Wezwanie do działania"

#: modules/announcements/module.php:111
msgid "<p>With AI for text, code, image generation and editing, you can bring your vision to life faster than ever. Start your free trial now - <b>no credit card required!</b></p>"
msgstr "<p>Dzięki SI do tekstu, kodu, generowania i edycji obrazków możesz urzeczywistnić swoją wizję szybciej niż kiedykolwiek. Rozpocznij bezpłatny okres próbny już teraz - <b>nie jest wymagana karta kredytowa!</b></p>"

#: modules/floating-buttons/base/widget-contact-button-base.php:2226
msgid "Adjust transition duration to change the speed of the <b>hover animation on desktop</b> and the <b>click animation on touchscreen</b>."
msgstr "Dostosuj czas trwania przejścia, aby zmienić szybkość <b>animacji najechania na komputerze stacjonarnym</b> i <b>animacji kliknięcia na ekranie dotykowym</b>."

#: includes/widgets/image-box.php:409 includes/widgets/image.php:386
msgid "Scale Down"
msgstr "Pomniejsz"

#: modules/floating-buttons/base/widget-contact-button-base.php:2481
#: modules/floating-buttons/base/widget-contact-button-base.php:2501
msgid "Text and Icon Color"
msgstr "Kolor tekstu i ikonki"

#: modules/floating-buttons/base/widget-contact-button-base.php:86
msgid "Call now"
msgstr "Zadzwoń teraz"

#: modules/floating-buttons/base/widget-contact-button-base.php:536
msgid "Contact Details"
msgstr "Szczegóły kontaktu"

#: modules/floating-buttons/base/widget-contact-button-base.php:828
msgid "Enter description"
msgstr "Wpisz opis"

#: modules/element-cache/module.php:139
msgid "1 Year"
msgstr "1 rok"

#: modules/element-cache/module.php:136
msgid "1 Week"
msgstr "1 tydzień"

#: modules/element-cache/module.php:135
msgid "3 Days"
msgstr "3 dni"

#: modules/element-cache/module.php:134
msgid "1 Day"
msgstr "1 dzień"

#: modules/element-cache/module.php:133
msgid "12 Hours"
msgstr "12 godzin"

#: modules/element-cache/module.php:132
msgid "6 Hours"
msgstr "6 godzin"

#: modules/element-cache/module.php:131
msgid "1 Hour"
msgstr "1 godzina"

#: modules/element-cache/module.php:103
msgid "Cache Settings"
msgstr "Ustawienia pamięci podręcznej"

#: modules/floating-buttons/base/widget-contact-button-base.php:342
#: modules/floating-buttons/base/widget-contact-button-base.php:896
#: modules/link-in-bio/base/widget-link-in-bio-base.php:737
msgid "+"
msgstr "+"

#: modules/element-cache/module.php:137
msgid "2 Weeks"
msgstr "2 tygodnie"

#: modules/floating-buttons/base/widget-contact-button-base.php:294
msgid "@"
msgstr "@"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:886
msgid "About Me"
msgstr "O mnie"

#: core/base/providers/social-network-provider.php:174
msgid "Behance"
msgstr "Behance"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:841
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1287
msgid "Bio"
msgstr "Życiorys"

#: modules/apps/admin-apps-page.php:121
msgid "Cannot Install"
msgstr "Nie można zainstalować"

#: modules/floating-buttons/base/widget-contact-button-base.php:1643
#: modules/floating-buttons/base/widget-floating-bars-base.php:295
#: modules/floating-buttons/base/widget-floating-bars-base.php:1036
msgid "Close Button"
msgstr "Przycisk Zamknij"

#: modules/floating-buttons/base/widget-contact-button-base.php:2081
#: modules/floating-buttons/base/widget-contact-button-base.php:2172
#: modules/floating-buttons/base/widget-contact-button-base.php:2865
#: modules/floating-buttons/base/widget-floating-bars-base.php:840
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1159
msgid "Corners"
msgstr "Narożniki"

#: core/base/providers/social-network-provider.php:180
msgid "Dribbble"
msgstr "Dribbble"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:335
msgid "Enter link text"
msgstr "Wpisz tekst odnośnika"

#: core/base/providers/social-network-provider.php:108
msgid "Facebook"
msgstr "Facebook"

#: core/base/providers/social-network-provider.php:222
msgid "File Download"
msgstr "Pobieranie pliku"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:914
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1247
msgid "Identity"
msgstr "Tożsamość"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1034
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1710
msgid "Image Height"
msgstr "Wysokość obrazka"

#: core/base/providers/social-network-provider.php:120
msgid "Instagram"
msgstr "Instagram"

#: core/base/providers/social-network-provider.php:126
msgid "LinkedIn"
msgstr "LinkedIn"

#: modules/floating-buttons/base/widget-contact-button-base.php:321
#: modules/floating-buttons/base/widget-contact-button-base.php:663
#: modules/floating-buttons/base/widget-contact-button-base.php:670
#: modules/floating-buttons/base/widget-contact-button-base.php:872
#: modules/floating-buttons/base/widget-contact-button-base.php:874
#: modules/floating-buttons/base/widget-contact-button-base.php:1768
#: modules/link-in-bio/base/widget-link-in-bio-base.php:465
#: modules/link-in-bio/base/widget-link-in-bio-base.php:476
#: modules/link-in-bio/base/widget-link-in-bio-base.php:716
#: modules/link-in-bio/base/widget-link-in-bio-base.php:718
msgid "Message"
msgstr "Komunikat"

#: core/base/providers/social-network-provider.php:198
msgid "Messenger"
msgstr "Messenger"

#: core/base/providers/social-network-provider.php:132
msgid "Pinterest"
msgstr "Pinterest"

#: modules/floating-buttons/base/widget-contact-button-base.php:479
#: modules/floating-buttons/base/widget-contact-button-base.php:779
#: modules/link-in-bio/base/widget-link-in-bio-base.php:598
msgid "Platform"
msgstr "Platforma"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:977
msgid "Profile"
msgstr "Profil"

#: core/base/providers/social-network-provider.php:228
msgid "SMS"
msgstr "SMS"

#: modules/floating-buttons/base/widget-contact-button-base.php:2087
#: modules/floating-buttons/base/widget-contact-button-base.php:2178
#: modules/floating-buttons/base/widget-contact-button-base.php:2871
#: modules/floating-buttons/base/widget-floating-bars-base.php:846
#: modules/link-in-bio/base/widget-link-in-bio-base.php:120
msgid "Sharp"
msgstr "Ostrość"

#: core/base/providers/social-network-provider.php:240
msgid "Skype"
msgstr "Skype"

#: includes/widgets/traits/button-trait.php:300
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:165
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:211
msgid "Space between"
msgstr "Odstęp między"

#: core/base/providers/social-network-provider.php:162
msgid "Spotify"
msgstr "Spotify"

#: modules/floating-buttons/base/widget-contact-button-base.php:130
msgid "Store Manager"
msgstr "Kierownik sklepu"

#: core/base/providers/social-network-provider.php:204
msgid "Telephone"
msgstr "Telefon"

#: core/base/providers/social-network-provider.php:144
msgid "TikTok"
msgstr "TikTok"

#: modules/floating-buttons/base/widget-contact-button-base.php:1799
msgid "Time"
msgstr "Czas"

#: modules/floating-buttons/base/widget-contact-button-base.php:677
msgid "Time format"
msgstr "Format czasu"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:69
#: modules/floating-buttons/base/widget-contact-button-base.php:131
msgid "Type your title here"
msgstr "Wpisz tutaj swój tytuł"

#: core/base/providers/social-network-provider.php:234
msgid "Viber"
msgstr "Viber"

#: core/base/providers/social-network-provider.php:192
msgid "Waze"
msgstr "Waze"

#: core/base/providers/social-network-provider.php:150
msgid "WhatsApp"
msgstr "WhatsApp"

#: modules/floating-buttons/base/widget-contact-button-base.php:682
msgid "14:20"
msgstr "14:20"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:864
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1323
msgid "Title or Tagline"
msgstr "Tytuł lub slogan"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:855
msgid "Sara Parker"
msgstr "Sara Parker"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1565
msgid "Layout Width"
msgstr "Szerokość układu"

#: modules/floating-buttons/base/widget-contact-button-base.php:305
#: modules/floating-buttons/base/widget-contact-button-base.php:857
#: modules/floating-buttons/base/widget-contact-button-base.php:859
#: modules/link-in-bio/base/widget-link-in-bio-base.php:447
#: modules/link-in-bio/base/widget-link-in-bio-base.php:458
#: modules/link-in-bio/base/widget-link-in-bio-base.php:701
#: modules/link-in-bio/base/widget-link-in-bio-base.php:703
msgid "Subject"
msgstr "Temat"

#: core/base/providers/social-network-provider.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:836
#: modules/link-in-bio/base/widget-link-in-bio-base.php:680
msgid "Email"
msgstr "E-mail"

#: core/base/providers/social-network-provider.php:114
msgid "X (Twitter)"
msgstr "X (Twitter)"

#: core/base/providers/social-network-provider.php:156
msgid "Apple Music"
msgstr "Apple Music"

#: core/base/providers/social-network-provider.php:102
msgid "Save contact (vCard)"
msgstr "Zapisz kontakt (vCard)"

#: modules/floating-buttons/base/widget-contact-button-base.php:186
msgid "Click to start chat"
msgstr "Kliknij, by rozpocząć czat"

#: core/settings/editor-preferences/model.php:183
#: modules/styleguide/module.php:127
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:441
msgid "Show global settings"
msgstr "Pokaż ustawienia ogólne"

#: modules/floating-buttons/base/widget-contact-button-base.php:669
msgid "Hey, how can I help you today?"
msgstr "Cześć, jak mogę ci pomóc?"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:91
msgid "Top 10 Recipes"
msgstr "10 najlepszych przepisów"

#: modules/floating-buttons/base/widget-contact-button-base.php:183
msgid "Send Button"
msgstr "Przycisk wysyłania"

#: modules/floating-buttons/base/widget-contact-button-base.php:122
msgid "Rob Jones"
msgstr "Rob Jones"

#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:222
msgid "Powered by Elementor"
msgstr "Obsługiwane przez Elementor"

#: modules/floating-buttons/base/widget-contact-button-base.php:141
msgid "Contact Buttons"
msgstr "Przyciski kontaktowe"

#: core/base/traits/shared-widget-controls-trait.php:107
msgid "Icons Per Row"
msgstr "Ikonki w wierszu"

#: modules/floating-buttons/base/widget-contact-button-base.php:147
#: modules/link-in-bio/base/widget-link-in-bio-base.php:590
msgid "Enter icon text"
msgstr "Wpisz tekst ikonki"

#: modules/floating-buttons/base/widget-contact-button-base.php:655
msgid "Rob"
msgstr "Rob"

#: modules/element-cache/module.php:141
msgid "Specify the duration for which data is stored in the cache. Elements caching speeds up loading by serving pre-rendered copies of elements, rather than rendering them fresh each time. This control ensures efficient performance and up-to-date content."
msgstr "Określ czas, przez jaki dane są przechowywane w pamięci podręcznej. Buforowanie elementów przyspiesza wczytywanie poprzez dostarczanie wstępnie renderowanych kopii elementów, zamiast renderowania ich na nowo za każdym razem. Ta kontrola zapewnia wydajną wydajność i aktualną treść."

#: modules/link-in-bio/base/widget-link-in-bio-base.php:880
msgid "About Heading"
msgstr "O nagłówku"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1629
msgid "Apply Full Screen Height on"
msgstr "Zastosuj pełną wysokość ekranu"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:97
msgid "Healthy Living Resources"
msgstr "Zasoby zdrowego życia"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:94
msgid "Meal Prep"
msgstr "Przygotowywanie posiłków"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:88
msgid "Get Healthy"
msgstr "Bądź zdrowy"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:55
msgid "Join me on my journey to a healthier lifestyle"
msgstr "Dołącz do mnie w mojej podróży do zdrowszego stylu życia"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:52
msgid "Kitchen Chronicles"
msgstr "Kroniki kuchenne"

#: modules/floating-buttons/base/widget-contact-button-base.php:645
#: modules/floating-buttons/base/widget-contact-button-base.php:1714
msgid "Message Bubble"
msgstr "Bańka wiadomości"

#: modules/floating-buttons/base/widget-contact-button-base.php:123
#: modules/floating-buttons/base/widget-contact-button-base.php:656
msgid "Type your name here"
msgstr "Wpisz tutaj swoje imię"

#: modules/floating-buttons/base/widget-contact-button-base.php:65
msgid "Chat Button"
msgstr "Przycisk czatu"

#: includes/settings/settings.php:455
msgid "Reduce unnecessary render-blocking loads by dequeuing unused Gutenberg block editor scripts and styles."
msgstr "Zredukuj niepotrzebne obciążenia blokujące renderowanie, usuwając z kolejki nieużywane skrypty i style edytora bloków Gutenberga."

#. translators: 1: fetchpriority attribute, 2: lazy loading attribute.
#: includes/settings/settings.php:440
msgid "Improve performance by applying %1$s on LCP image and %2$s on images below the fold."
msgstr "Popraw wydajność, stosując %1$s na obrazku LCP i %2$s na obrazkach poniżej linii zagięcia."

#: includes/settings/settings.php:408
msgid "Improve loading times on your site by selecting the optimization tools that best fit your requirements."
msgstr "Przyspiesz wczytywanie swojej witryny, wybierając narzędzia optymalizacyjne, które najlepiej odpowiadają Twoim potrzebom."

#: core/settings/editor-preferences/model.php:212
msgid "Decide where you want to go when leaving the editor."
msgstr "Zdecyduj, dokąd chcesz się udać po wyjściu z edytora."

#: core/settings/editor-preferences/model.php:188
#: modules/styleguide/module.php:129
msgid "Temporarily overlay the canvas with the style guide to preview your changes to global colors and fonts."
msgstr "Tymczasowo nałóż na płótno przewodnik po stylach, aby wyświetlić podgląd zmian w globalnych kolorach i krojach pisma."

#: core/settings/editor-preferences/model.php:144
msgid "This refers to elements you’ve hidden in the Responsive Visibility settings."
msgstr "Dotyczy to elementów ukrytych w ustawieniach widoczności responsywnej."

#: modules/link-in-bio/base/widget-link-in-bio-base.php:303
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1075
msgid "CTA Link Buttons"
msgstr "Odnośnik przycisków wezwania do działania"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:545
msgid "Add CTA Link"
msgstr "Dodaj odnośnik wezwania do działania"

#: modules/floating-buttons/base/widget-contact-button-base.php:920
#: modules/link-in-bio/base/widget-link-in-bio-base.php:534
#: modules/link-in-bio/base/widget-link-in-bio-base.php:783
msgid "Enter your username"
msgstr "Wpisz swoje imię"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:495
msgid "Enter your number"
msgstr "Wpisz swoją liczbę"

#: modules/floating-buttons/base/widget-contact-button-base.php:838
#: modules/link-in-bio/base/widget-link-in-bio-base.php:440
#: modules/link-in-bio/base/widget-link-in-bio-base.php:682
msgid "Enter your email"
msgstr "Wpisz swój e-mail"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:429
msgid "Mail"
msgstr "Adres e-mail"

#: modules/floating-buttons/base/widget-contact-button-base.php:2525
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1199
msgid "Dividers"
msgstr "Rozdzielacze"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:885
msgid "About"
msgstr "Informacje o"

#: modules/apps/admin-apps-page.php:114 modules/apps/admin-apps-page.php:145
msgid "Cannot Activate"
msgstr "Nie można włączyć"

#: modules/floating-buttons/base/widget-contact-button-base.php:247
msgid "Chat Box"
msgstr "Pole czatu"

#. translators: %s: `<br>` tag.
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:25
msgid "Create Forms and Collect Leads %s with Elementor Pro"
msgstr "Twórz formularze i zbieraj potencjalnych klientów %s za pomocą Elementor Pro"

#. translators: %s: `<br>` tag.
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:25
msgid "Enjoy creative freedom %s with Custom Icons"
msgstr "Ciesz się wolnością twórczą %s dzięki własnym ikonkom"

#: includes/managers/elements.php:306
msgid "Link In Bio"
msgstr "Odnośnik w biografii"

#: modules/floating-buttons/base/widget-contact-button-base.php:681
msgid "2:20 PM"
msgstr "14:20"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1674
msgid "Image Shape"
msgstr "Kształt obrazka"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:419
#: modules/link-in-bio/base/widget-link-in-bio-base.php:650
msgid "Enter your link"
msgstr "Wpisz swój odnośnik"

#: modules/floating-buttons/base/widget-contact-button-base.php:737
#: modules/floating-buttons/base/widget-contact-button-base.php:1047
msgid "Type your text here"
msgstr "Wpisz tutaj swój tekst"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:973
msgid "Image style"
msgstr "Styl obrazka"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:356
msgid "Link Type"
msgstr "Rodzaj odnośnika"

#: modules/floating-buttons/base/widget-contact-button-base.php:2766
msgid "Open Animation"
msgstr "Otwórz animację"

#: modules/floating-buttons/base/widget-contact-button-base.php:613
#: modules/floating-buttons/base/widget-contact-button-base.php:1532
msgid "Profile Image"
msgstr "Obrazek profilowy"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1738
msgid "Bottom Border"
msgstr "Dolne obramowanie"

#: modules/floating-buttons/base/widget-contact-button-base.php:2776
msgid "Close Animation"
msgstr "Zamknij animację"

#: modules/floating-buttons/base/widget-contact-button-base.php:1657
msgid "Close Button Color"
msgstr "Kolor przycisku Zamknij"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1612
msgid "Full Screen Height"
msgstr "Pełna wysokość ekranu"

#: modules/floating-buttons/base/widget-contact-button-base.php:2264
#: modules/link-in-bio/base/widget-link-in-bio-base.php:559
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1384
#: assets/js/app.js:10375
msgid "Icons"
msgstr "Ikonki"

#: modules/element-cache/module.php:138
msgid "1 Month"
msgstr "1 miesiąc"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:800
msgid "Add Icon"
msgstr "Dodaj Ikonkę"

#: modules/floating-buttons/base/widget-contact-button-base.php:380
#: modules/floating-buttons/base/widget-contact-button-base.php:982
msgid "Action"
msgstr "Działanie"

#: core/settings/editor-preferences/model.php:66
msgid "Display mode"
msgstr "Tryb ekranu"

#: core/settings/editor-preferences/model.php:70
msgid "Light mode"
msgstr "Tryb jasny"

#: core/settings/editor-preferences/model.php:58
msgid "Panel"
msgstr "Panel"

#: core/settings/editor-preferences/model.php:139
msgid "Show hidden elements"
msgstr "Pokaż ukryte elementy"

#: core/settings/editor-preferences/model.php:74
msgid "Dark mode"
msgstr "Tryb ciemny"

#: core/settings/editor-preferences/model.php:116
msgid "Show quick edit options"
msgstr "Pokaż opcje szybkiej edycji"

#: core/settings/editor-preferences/model.php:132
msgid "This only applies while you’re working in the editor. The front end won’t be affected."
msgstr "Dotyczy to tylko pracy w edytorze. Front-end nie zostanie naruszony."

#: core/settings/editor-preferences/model.php:127
msgid "Expand images in lightbox"
msgstr "Rozszerz obrazki w lightboxie"

#: core/settings/editor-preferences/model.php:120
msgid "Show additional actions while hovering over the handle of an element."
msgstr "Pokaż dodatkowe akcje po najechaniu na uchwyt elementu."

#: core/settings/editor-preferences/model.php:83
msgid "Set light or dark mode, or auto-detect to sync with your operating system settings."
msgstr "Ustaw tryb jasny lub ciemny albo włącz automatyczne wykrywanie, aby zsynchronizować ustawienia z systemem operacyjnym."

#: core/experiments/manager.php:370
msgid "Optimized Markup"
msgstr "Zoptymalizowane znaczniki"

#: core/admin/admin.php:1022 includes/controls/gallery.php:122
#: includes/controls/media.php:318
msgid "Optimize your images to enhance site performance by using Image Optimizer."
msgstr "Zoptymalizuj obrazki, aby zwiększyć wydajność witryny, korzystając z optymalizatora obrazków."

#: core/experiments/manager.php:372
msgid "Reduce the DOM size by eliminating HTML tags in various elements and widgets. This experiment includes markup changes so it might require updating custom CSS/JS code and cause compatibility issues with third party plugins."
msgstr "Zmniejsz rozmiar DOM, eliminując znaczniki HTML w różnych elementach i widżetach. Ten eksperyment obejmuje zmiany znaczników, więc może wymagać aktualizacji własnego kodu CSS/JS i powodować problemy ze zgodnością z wtyczkami innych firm."

#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:33
msgid "Upgrade Sale Now"
msgstr "Ulepsz Kup teraz"

#: core/admin/admin.php:349
msgid "Discounted Upgrades Now!"
msgstr "Teraz obniżone ceny ulepszeń!"

#: core/admin/admin-notices.php:709
msgid "Automatically compress and optimize images, resize larger files, or convert to WebP. Optimize images individually, in bulk, or on upload."
msgstr "Automatycznie kompresuj i optymalizuj obrazki, zmieniaj rozmiar większych plików lub konwertuj do formatu WebP. Optymalizuj obrazki indywidualnie, zbiorczo lub podczas przesyłania."

#: core/admin/admin-notices.php:708
msgid "Speed up your website with Image Optimizer by Elementor"
msgstr "Przyspiesz swoją witrynę dzięki optymalizatorowi obrazków firmy Elementor"

#: includes/widgets/counter.php:275
msgid "Title Position"
msgstr "Położenie tytułu"

#: elementor.php:77
msgid "Elementor isn’t running because PHP is outdated."
msgstr "Elementor nie działa, ponieważ twoja wersja PHP jest przestarzała."

#: includes/widgets/counter.php:343
msgid "Title Vertical Alignment"
msgstr "Pionowe wyrównanie tytułu"

#: includes/widgets/counter.php:314
msgid "Title Horizontal Alignment"
msgstr "Poziome wyrównanie tytułu"

#: includes/widgets/counter.php:424
msgid "Number Alignment"
msgstr "Wyrównanie liczby"

#: includes/widgets/counter.php:388
msgid "Number Position"
msgstr "Pozycja liczby"

#: modules/home/<USER>
msgid "Default Elementor menu page."
msgstr "Domyślna strona menu Elementora."

#: modules/home/<USER>
msgid "Elementor Home Screen"
msgstr "Ekran główny Elementora"

#: elementor.php:101
msgid "Elementor isn’t running because WordPress is outdated."
msgstr "Elementor nie działa, ponieważ WordPress jest nieaktualny."

#. translators: %s: PHP version.
#. translators: %s: WordPress version.
#: elementor.php:80 elementor.php:104
msgid "Update to version %s and get back to creating!"
msgstr "Zaktualizuj do wersji %s i wróć do tworzenia!"

#: core/files/uploads-manager.php:589
msgid "You do not have permission to upload JSON files."
msgstr "Nie masz uprawnień do przesyłania plików JSON."

#: includes/settings/settings.php:215
msgid "Home"
msgstr "Strona domowa"

#: includes/widgets/counter.php:452
msgid "Number Gap"
msgstr "Odstęp liczby"

#: includes/widgets/counter.php:372
msgid "Title Gap"
msgstr "Odstęp tytułu"

#: core/admin/admin.php:1024 modules/apps/admin-apps-page.php:118
msgid "Install"
msgstr "Instaluj"

#: core/admin/admin.php:1024
msgid "Image Optimizer"
msgstr "Optymalizator obrazków"

#: modules/site-navigation/rest-fields/page-user-can.php:28
msgid "Whether the current user can edit or delete this post"
msgstr "Czy bieżący użytkownik może edytować lub usunąć ten wpis"

#. translators: %s: `<br>` tag.
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:34
msgid "Expand your icon library beyond FontAwesome and add icon %s libraries of your choice"
msgstr "Rozszerz swoją bibliotekę ikonki poza FontAwesome i dodaj biblioteki ikonki %s wg własnego wyboru"

#. translators: %s: br
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:31
msgid "Remain GDPR compliant with Custom Fonts that let you disable %s Google Fonts from your website"
msgstr "Zachowaj zgodność z RODO dzięki niestandardowym krojom pisma, które umożliwiają wyłączenie %s krojów pisma Google na Twojej witrynie internetowej"

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:28
msgid "Upload any font to keep your website true to your brand"
msgstr "Prześlij dowolny krój pisma, aby Twoja witryna była zgodna z Twoją marką"

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:23
msgid "Stay on brand with a Custom Font"
msgstr "Zachowaj spójność z marką dzięki niestandardowemu krojowi pisma"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:30
msgid "Leverage Elementor AI to instantly generate Custom Code for Elementor"
msgstr "Wykorzystaj SI Elementora, aby natychmiast wygenerować własny kod dla Elementora"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:37
msgid "Add any icon, anywhere on your website"
msgstr "Dodaj dowolną ikonkę w dowolnym miejscu swojej witryny"

#: modules/shapes/widgets/text-path.php:149
msgid "Want to create custom text paths with SVG?"
msgstr "Chcesz utworzyć własne ścieżki tekstowe za pomocą SVG?"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:29
msgid "Use Custom Code to create sophisticated custom interactions to engage visitors"
msgstr "Użyj własnego kodu, aby tworzyć zaawansowane własne interakcje angażujące odwiedzających"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:28
msgid "Add Custom Code snippets anywhere on your website, including the header or footer to measure your page’s performance*"
msgstr "Dodawaj własne fragmenty kodu w dowolnym miejscu swojej witryny, w tym w nagłówku lub stopce, aby mierzyć wydajność swojej strony*"

#: includes/widgets/text-editor.php:182
msgid "1"
msgstr "1"

#: includes/widgets/text-editor.php:191
msgid "10"
msgstr "10"

#: includes/widgets/text-editor.php:183
msgid "2"
msgstr "2"

#: includes/widgets/text-editor.php:184
msgid "3"
msgstr "3"

#: includes/widgets/text-editor.php:185
msgid "4"
msgstr "4"

#: includes/widgets/text-editor.php:186
msgid "5"
msgstr "5"

#: includes/widgets/text-editor.php:187
msgid "6"
msgstr "6"

#: includes/widgets/text-editor.php:188
msgid "7"
msgstr "7"

#: includes/widgets/text-editor.php:189
msgid "8"
msgstr "8"

#: includes/widgets/text-editor.php:190
msgid "9"
msgstr "9"

#: modules/apps/admin-menu-apps.php:22 modules/apps/admin-menu-apps.php:26
#: modules/apps/module.php:37 assets/js/admin-top-bar.js:183
#: assets/js/editor.js:38655
msgid "Add-ons"
msgstr "Dodatki"

#: modules/apps/module.php:70
msgid "For Elementor"
msgstr "Dla Elementora"

#: modules/apps/admin-pointer.php:35
msgid "Explore Add-ons"
msgstr "Poznaj dodatki"

#: modules/apps/admin-pointer.php:29
msgid "New! Popular Add-ons"
msgstr "Nowość! Popularne dodatki"

#: modules/apps/admin-apps-page.php:34
msgid "Please note that certain tools and services on this page are developed by third-party companies and are not part of Elementor's suite of products or support. Before using them, we recommend independently evaluating them. Additionally, when clicking on their action buttons, you may be redirected to an external website."
msgstr "Należy pamiętać, że niektóre narzędzia i usługi na tej stronie są opracowywane przez firmy zewnętrzne i nie są częścią pakietu produktów lub wsparcia Elementora. Przed ich użyciem zalecamy ich niezależną ocenę. Ponadto po kliknięciu przycisków akcji możesz zostać przekierowany do zewnętrznej witryny."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:344
msgid "It is strongly recommended to %1$sbackup the database%2$s before using replacing URLs."
msgstr "Zdecydowanie zaleca się %1$swykonanie kopii zapasowej bazy danych%2$s przed użyciem zastępczych adresów URL."

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:23
msgid "Enjoy Creative Freedom with Custom Code"
msgstr "Ciesz się swobodą twórczą dzięki własnemu kodowi"

#: includes/widgets/icon-box.php:363 includes/widgets/image-box.php:324
msgid "Content Spacing"
msgstr "Odstęp treści"

#: includes/controls/base-units.php:138
msgid "Custom unit"
msgstr "Własna jednostka"

#: core/role-manager/role-manager.php:215
msgid "Giving broad access to edit the HTML widget can pose a security risk to your website because it enables users to run malicious scripts, etc."
msgstr "Udzielenie szerokiego dostępu do edycji widżetu HTML może stanowić zagrożenie dla bezpieczeństwa Twojej witryny, ponieważ umożliwia użytkownikom uruchamianie złośliwych skryptów itp."

#: core/role-manager/role-manager.php:213
msgid "Enable the option to use the HTML widget"
msgstr "Włącz opcję korzystania z widżetu HTML"

#: includes/editor-templates/panel-elements.php:33
msgid "Access all Pro widgets."
msgstr "Uzyskaj dostęp do wszystkich widżetów Pro."

#: core/utils/hints.php:167 includes/controls/notice.php:83
msgid "Don’t show again."
msgstr "Nie pokazuj ponownie."

#: includes/editor-templates/navigator.php:16
msgid "Access all Pro widgets"
msgstr "Dostęp do wszystkich widżetów Pro"

#: includes/managers/controls.php:1310 assets/js/editor.js:52964
msgid "Mouse Effects"
msgstr "Efekty myszy"

#: includes/managers/controls.php:1301 assets/js/editor.js:52949
msgid "Scrolling Effects"
msgstr "Efekty przewijania"

#: includes/managers/controls.php:1319
#: modules/floating-buttons/base/widget-floating-bars-base.php:1453
#: assets/js/editor.js:52979
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:146
msgid "Sticky"
msgstr "Przypięty"

#: core/admin/admin-notices.php:661 includes/controls/gallery.php:123
#: includes/controls/media.php:319 includes/widgets/heading.php:473
msgid "Install Plugin"
msgstr "Zainstaluj wtyczkę"

#: includes/widgets/testimonial.php:197
msgid "Designer"
msgstr "Projektant"

#: includes/managers/controls.php:1270 assets/js/editor.js:52934
msgid "Display Conditions"
msgstr "Wyświetl warunki"

#: includes/widgets/testimonial.php:182
msgid "John Doe"
msgstr "Jan Kowalski"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:35
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:48
msgid "* Requires an Advanced subscription or higher"
msgstr "* Wymagana subskrypcja zaawansowana lub wyższa"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:34
msgid "Integrate your favorite marketing software*"
msgstr "Zintegruj swoje, ulubione oprogramownaie marketingowe*"

#: includes/editor-templates/templates.php:437
msgid "Generate Variations"
msgstr "Wygeneruj warianty"

#: includes/template-library/sources/local.php:817
msgid "Invalid template type or template does not exist."
msgstr "Nieprawidłowy rodzaj szablonu lub szablon nie istnieje."

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:33
msgid "Use any field to collect the information you need"
msgstr "Użyj dowolnego pola, aby zebrać potrzebne informacje"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:35
msgid "Collect lead submissions directly within your WordPress Admin to manage, analyze and perform bulk actions on the submitted lead*"
msgstr "Zbieraj zgłoszenia potencjalnych klientów bezpośrednio w panelu administracyjnym WordPress, aby nimi zarządzać, analizować je i wykonywać działania zbiorcze na przesłanych potencjalnych klientach*"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:32
msgid "Create single or multi-step forms to engage and convert visitors"
msgstr "Twórz formularze jedno- lub wieloetapowe, aby angażować i konwertować odwiedzających"

#: includes/controls/groups/background.php:162
msgid "Set locations and angle for each breakpoint to ensure the gradient adapts to different screen sizes."
msgstr "Ustaw lokalizację i kąt dla każdego punktu przerwania, aby mieć pewność, że gradient dostosuje się do różnych rozmiarów ekranu."

#: includes/widgets/button.php:104
msgid "Convert visitors into customers"
msgstr "Zmień odwiedzających w klientów"

#: includes/widgets/video.php:130
msgid "Get the Video Playlist widget and grow your toolbox with Elementor Pro."
msgstr "Pobierz widżet listy odtwarzania filmów i rozszerz swój zestaw narzędzi dzięki Elementor Pro."

#: includes/widgets/video.php:129
msgid "Grab your visitors' attention"
msgstr "Przyciągnij uwagę odwiedzających"

#: includes/widgets/button.php:105
msgid "Get the Call to Action widget and grow your toolbox with Elementor Pro."
msgstr "Pobierz widżet „Wezwanie do działania” i rozszerz swój zestaw narzędzi dzięki Elementor Pro."

#: core/utils/hints.php:459
msgid "install Now"
msgstr "instaluj teraz"

#: includes/template-library/sources/local.php:826
msgid "You do not have permission to export this template."
msgstr "Nie masz uprawnień do eksportowania tego szablonu."

#: includes/template-library/manager.php:418
#: includes/template-library/manager.php:561
#: includes/template-library/sources/local.php:822
msgid "You do not have permission to access this template."
msgstr "Nie masz uprawnień dostępu do tego szablonu."

#: core/role-manager/role-manager.php:195
msgid "Enable the option to upload JSON files"
msgstr "Włącz tę opcję, by wczytywać pliki JSON"

#: core/role-manager/role-manager.php:197
#: core/role-manager/role-manager.php:215
msgid "Heads up"
msgstr "Uwaga"

#: core/role-manager/role-manager.php:197
msgid "Giving broad access to upload JSON files can pose a security risk to your website because such files may contain malicious scripts, etc."
msgstr "Udzielenie szerokiego dostępu do przesyłania plików JSON może stanowić zagrożenie dla bezpieczeństwa Twojej witryny, ponieważ takie pliki mogą zawierać złośliwe skrypty itp."

#: core/files/uploads-manager.php:290
msgid "Invalid file name."
msgstr "Nieprawidłowa nazwa pliku."

#: includes/editor-templates/templates.php:170
#: assets/js/element-manager-admin.js:663
#: assets/js/element-manager-admin.js:732
msgid "Usage"
msgstr "Użycie"

#: includes/template-library/sources/local.php:233
msgid "All Templates"
msgstr "Wszystkie szablony"

#: includes/controls/groups/flex-container.php:187
msgid "No Wrap"
msgstr "Bez zawijania"

#: includes/template-library/sources/local.php:234
msgid "View Template"
msgstr "Zobacz szablon"

#: modules/element-manager/ajax.php:127
msgid "WordPress Widgets"
msgstr "Widżety WordPress"

#: includes/controls/groups/flex-container.php:183
#: includes/controls/groups/flex-container.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:154
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:156
msgid "Wrap"
msgstr "Zawijanie"

#: modules/element-manager/ajax.php:117
msgid "Invalid nonce."
msgstr "Nieprawidłowy kod jednorazowy."

#: includes/controls/groups/image-size.php:296 includes/controls/media.php:297
#: includes/widgets/testimonial.php:328
msgid "Image Resolution"
msgstr "Rozdzielczość obrazka"

#: core/common/modules/finder/categories/settings.php:79
#: modules/element-manager/admin-menu-app.php:22
#: modules/element-manager/admin-menu-app.php:26
#: modules/element-manager/admin-menu-app.php:35
msgid "Element Manager"
msgstr "Menedżer elementów"

#: modules/element-manager/ajax.php:148
msgid "No elements to save."
msgstr "Nie ma elementów do zapisania."

#: modules/element-manager/ajax.php:112
msgid "You do not have permission to edit these settings."
msgstr "Nie masz uprawnień do edytowania tych ustawień."

#: includes/template-library/sources/local.php:236
msgid "No Templates found"
msgstr "Nie znaleziono motywów"

#: includes/template-library/sources/local.php:238
msgid "Parent Template:"
msgstr "Motyw nadrzędny:"

#: includes/template-library/sources/local.php:237
msgid "No Templates found in Trash"
msgstr "Nie znaleziono motywów w koszu"

#. translators: %s: Widget title.
#: modules/promotions/widgets/pro-widget-promotion.php:61
msgid "This result includes the Elementor Pro %s widget. Upgrade now to unlock it and grow your web creation toolkit."
msgstr "Ten wynik obejmuje widżet Elementor Pro %s. Zaktualizuj teraz, aby go odblokować i rozszerzyć swój zestaw narzędzi do tworzenia stron internetowych."

#: core/experiments/manager.php:362
msgid "Container-based content will be hidden from your site and may not be recoverable in all cases."
msgstr "Treść oparta na kontenerach będzie ukryta w Twojej witrynie i w niektórych przypadkach nie będzie można jej odzyskać."

#: modules/element-manager/ajax.php:154
msgid "Unexpected elements data."
msgstr "Nieoczekiwane dane elementów."

#: includes/widgets/image-carousel.php:128
msgid "Gain complete freedom to design every slide with Elementor\"s Pro Carousel."
msgstr "Uzyskaj pełną swobodę projektowania każdego slajdu dzięki Karuzeli Elementor Pro."

#: includes/widgets/heading.php:159
msgid "Create captivating headings that rotate with the Animated Headline Widget."
msgstr "Twórz przyciągające wzrok nagłówki, które będą się obracać dzięki widżetowi animowanych nagłówków."

#: includes/widgets/image-gallery.php:108 includes/widgets/testimonial.php:114
msgid "Use interesting masonry layouts and other overlay features with Elementor's Pro Gallery widget."
msgstr "Wykorzystaj ciekawe układy cegiełkowe i inne funkcje nakładek dzięki widżetowi galerii Pro w Elementorze."

#: includes/template-library/sources/local.php:229
#: includes/template-library/sources/local.php:230
msgid "Add New Template"
msgstr "Utwórz szablon"

#: includes/template-library/sources/local.php:235
msgid "Search Template"
msgstr "Wyszukaj szablon"

#: includes/widgets/video.php:162
msgid "VideoPress"
msgstr "VideoPress"

#: includes/widgets/video.php:313
msgid "VideoPress URL"
msgstr "Adres URL VideoPress"

#. translators: %s: Recommended PHP version.
#: modules/system-info/reporters/server.php:131
msgid "We recommend using PHP version %s or higher."
msgstr "Zalecamy korzystanie z PHP w wersji %s lub nowszej."

#. translators: 1: Rating value, 2: Rating scale.
#: includes/widgets/rating.php:310
msgid "Rated %1$s out of %2$s"
msgstr "Ocena %1$s z %2$s"

#: includes/settings/settings.php:430
msgid "Optimized Image Loading"
msgstr "Zoptymalizowane wczytywanie obrazka"

#: includes/settings/settings.php:447
msgid "Optimized Gutenberg Loading"
msgstr "Zoptymalizowane wczytywanie Gutenberga"

#: modules/nested-accordion/widgets/nested-accordion.php:325
msgid "Let Google know that this section contains an FAQ. Make sure to only use it only once per page"
msgstr "Daj znać Google, że ta sekcja zawiera FAQ. Upewnij się, że używasz jej tylko raz na stronę"

#: modules/page-templates/module.php:157
msgid "Elementor Canvas"
msgstr "Płótno Elementora"

#: modules/image-loading-optimization/module.php:240
msgid "An image should not be lazy-loaded and marked as high priority at the same time."
msgstr "Obrazek nie powinien być jednocześnie leniwie wczytywany i oznaczony jako priorytetowy."

#: includes/widgets/star-rating.php:129
msgid "You are currently editing a Star Rating widget in its old version. Drag a new Rating widget onto your page to use a newer version, providing better capabilities."
msgstr "Aktualnie edytujesz widżet oceny gwiazdkowej w starej wersji. Przeciągnij nowy widżet oceny na swoją stronę, aby skorzystać z nowszej wersji, która oferuje lepsze możliwości."

#: includes/editor-templates/templates.php:519 assets/js/ai-admin.js:1035
#: assets/js/ai-admin.js:1082 assets/js/ai-admin.js:2492
#: assets/js/ai-admin.js:3219 assets/js/ai-gutenberg.js:1173
#: assets/js/ai-gutenberg.js:1220 assets/js/ai-gutenberg.js:2630
#: assets/js/ai-gutenberg.js:3357 assets/js/ai-layout.js:733
#: assets/js/ai-layout.js:780 assets/js/ai-media-library.js:1035
#: assets/js/ai-media-library.js:1082 assets/js/ai-media-library.js:2492
#: assets/js/ai-media-library.js:3219 assets/js/ai-unify-product-images.js:1035
#: assets/js/ai-unify-product-images.js:1082
#: assets/js/ai-unify-product-images.js:2492
#: assets/js/ai-unify-product-images.js:3219 assets/js/ai.js:1823
#: assets/js/ai.js:1870 assets/js/ai.js:3280 assets/js/ai.js:4007
#: assets/js/editor.js:8242 assets/js/editor.js:8244 assets/js/editor.js:9862
#: assets/js/editor.js:9863
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:4179
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:300
msgid "Upgrade now"
msgstr "Ulepsz teraz"

#: modules/page-templates/module.php:158
msgid "Elementor Full Width"
msgstr "Elementor o pełnej szerokości"

#: includes/controls/groups/background.php:245
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:129
msgid "Angle"
msgstr "Kąt"

#: includes/controls/groups/background.php:301
msgid "Background Image"
msgstr "Obrazek tła"

#: includes/controls/groups/background.php:95
msgid "Classic"
msgstr "Klasyczny"

#: includes/controls/groups/background.php:478
msgid "Display Size"
msgstr "Rozmiar wyświetlacza"

#: includes/controls/groups/background.php:233
msgid "Linear"
msgstr "Liniowy"

#: includes/controls/groups/background.php:234
msgid "Radial"
msgstr "Promieniowy"

#: includes/controls/groups/background.php:201
msgid "Second Color"
msgstr "Drugi kolor"

#: includes/controls/groups/text-shadow.php:61
#: includes/controls/groups/text-shadow.php:85
msgid "Text Shadow"
msgstr "Cień tekstu"

#: includes/controls/groups/css-filter.php:162
msgid "CSS Filters"
msgstr "Filtry CSS"

#: includes/controls/groups/grid-container.php:131
msgid "Justify Items"
msgstr "Wyjustuj elementy"

#: includes/controls/groups/flex-item.php:76
#: includes/controls/groups/flex-item.php:109
msgid "This control will affect contained elements only."
msgstr "Ta kontrola będzie miała wpływ tylko na zawarte elementy."

#: includes/controls/groups/flex-container.php:128
#: includes/controls/groups/grid-container.php:159
msgid "Align Items"
msgstr "Wyrównaj elementy"

#: includes/controls/groups/flex-container.php:45
msgid "Column - reversed"
msgstr "Kolumna - odwrócona"

#: includes/controls/groups/flex-container.php:41
msgid "Row - reversed"
msgstr "Wiersz - odwrócony"

#: includes/controls/groups/flex-container.php:37
msgid "Column - vertical"
msgstr "Kolumna - pionowo"

#: includes/controls/groups/flex-container.php:33
msgid "Row - horizontal"
msgstr "Wiersz - poziomo"

#: includes/settings/tools.php:165
msgid "An error occurred, the selected version is invalid. Try selecting different version."
msgstr "Wystąpił błąd, wybrana wersja jest nieprawidłowa. Spróbuj wybrać inną wersję."

#: includes/controls/groups/flex-container.php:204
#: includes/controls/groups/grid-container.php:226
msgid "Align Content"
msgstr "Wyrównanie treści"

#: includes/controls/groups/image-size.php:380
#: modules/atomic-widgets/image/image-sizes.php:38
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:174
msgid "Full"
msgstr "Pełny"

#: includes/controls/groups/flex-item.php:51
msgid "Align Self"
msgstr "Wyrównanie własne"

#: includes/controls/groups/flex-item.php:20
msgid "Flex Basis"
msgstr "Elastyczna podstawa"

#: includes/controls/groups/background.php:607
msgid "Background Fallback"
msgstr "Tło zapasowe"

#: includes/controls/groups/box-shadow.php:61
#: includes/controls/groups/box-shadow.php:96
msgid "Box Shadow"
msgstr "Cień pola"

#: includes/controls/groups/flex-container.php:195
msgid "Items within the container can stay in a single line (No wrap), or break into multiple lines (Wrap)."
msgstr "Elementy wewnątrz kontenera mogą pozostać w jednym wierszu (bez zawijania) lub podzielić się na wiele wierszy (zawijanie)."

#: includes/controls/groups/image-size.php:301
msgid "Image Dimension"
msgstr "Wymiar obrazka"

#: includes/controls/groups/flex-container.php:91
#: includes/controls/groups/grid-container.php:186
msgid "Justify Content"
msgstr "Wyjustuj treść"

#: includes/controls/groups/typography.php:220
msgid "Letter Spacing"
msgstr "Odstępy między literami"

#: includes/controls/groups/typography.php:198
msgid "Line Height"
msgstr "Wysokość wiersza"

#: includes/controls/groups/flex-item.php:138
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:171
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:174
msgid "Shrink"
msgstr "Skurcz"

#: includes/controls/groups/flex-item.php:173
msgid "Flex Shrink"
msgstr "Elastyczny skurcz"

#: includes/controls/groups/flex-item.php:159
msgid "Flex Grow"
msgstr "Elastyczny wzrost"

#: includes/controls/groups/background.php:154
msgid "Background Type"
msgstr "Rodzaj tła"

#: includes/controls/groups/border.php:60
msgid "Border Type"
msgstr "Rodzaj obramowania"

#: includes/controls/groups/flex-item.php:113
msgid "Custom Order"
msgstr "Kolejność własna"

#: includes/controls/groups/border.php:69
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:234
msgid "Groove"
msgstr "Wgłębione"

#: includes/controls/groups/flex-item.php:134
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:170
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:173
msgid "Grow"
msgstr "Wzrost"

#: app/modules/onboarding/module.php:140
msgid "You do not have permission to perform this action."
msgstr "Nie masz uprawnień do wykonania tej czynności."

#: modules/nested-tabs/widgets/nested-tabs.php:1175
#: modules/nested-tabs/widgets/nested-tabs.php:1240
msgid "Tabs. Open items with Enter or Space, close with Escape and navigate using the Arrow keys."
msgstr "Karty. Otwieraj elementy za pomocą Enter lub Spacji, zamykaj za pomocą Escape i nawiguj za pomocą klawiszy strzałek."

#: modules/nested-tabs/widgets/nested-tabs.php:200
#: modules/nested-tabs/widgets/nested-tabs.php:866
msgid "Above"
msgstr "Powyżej"

#: modules/nested-tabs/widgets/nested-tabs.php:204
#: modules/nested-tabs/widgets/nested-tabs.php:874
msgid "Below"
msgstr "Poniżej"

#. translators: %s: Singular label.
#. translators: %s: Post type (e.g. Page, Post, etc.)
#: core/document-types/page-base.php:204
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:17
msgid "View %s"
msgstr "Zobacz %s"

#: modules/site-navigation/module.php:68
msgid "Pages Panel"
msgstr "Panel stron"

#: includes/editor-templates/templates.php:321
#: includes/editor-templates/templates.php:352
#: includes/editor-templates/templates.php:405 assets/js/editor.js:8765
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:75
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:55
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:19
msgid "Rename"
msgstr "Zmień nazwę"

#: modules/nested-accordion/widgets/nested-accordion.php:173
msgid "Item Position"
msgstr "Pozycja elementu"

#: modules/apps/admin-pointer.php:30
msgid "Discover our collection of plugins and add-ons carefully selected to enhance your Elementor website and unleash your creativity."
msgstr "Odkryj naszą kolekcję wtyczek i dodatków starannie dobranych, aby ulepszyć swoją witrynę Elementor i uwolnić swoją kreatywność."

#: modules/apps/admin-apps-page.php:26
msgid "Learn more about this page."
msgstr "Dowiedz się więcej o tej stronie."

#. translators: 1: Slide count, 2: Total slides count.
#: includes/widgets/image-carousel.php:980
msgid "%1$s of %2$s"
msgstr "%1$s z %2$s"

#: modules/nested-accordion/widgets/nested-accordion.php:257
msgid "Collapse"
msgstr "Zwiń"

#: modules/nested-accordion/widgets/nested-accordion.php:344
msgid "Default State"
msgstr "Domyślny stan"

#: modules/nested-accordion/widgets/nested-accordion.php:243
#: assets/js/ai-admin.js:9384 assets/js/ai-gutenberg.js:9602
#: assets/js/ai-layout.js:4872 assets/js/ai-media-library.js:9384
#: assets/js/ai-unify-product-images.js:9384 assets/js/ai.js:10936
msgid "Expand"
msgstr "Rozwiń"

#: modules/nested-accordion/widgets/nested-accordion.php:362
msgid "Multiple"
msgstr "Wielokrotny"

#: modules/nested-accordion/widgets/nested-accordion.php:361
msgid "One"
msgstr "Jeden"

#: modules/nested-accordion/widgets/nested-accordion.php:347
msgid "First expanded"
msgstr "Pierwszy rozwinięty"

#: modules/nested-accordion/widgets/nested-accordion.php:337
msgid "Interactions"
msgstr "Interakcje"

#: modules/apps/admin-apps-page.php:25
msgid "Boost your web-creation process with add-ons, plugins, and more tools specially selected to unleash your creativity, increase productivity, and enhance your Elementor-powered website."
msgstr "Przyspiesz proces tworzenia stron internetowych dzięki dodatkom, wtyczkom i innym narzędziom specjalnie dobranym z myślą o uwolnieniu Twojej kreatywności, zwiększeniu produktywności i udoskonaleniu Twojej witryny opartej na Elementorze."

#: modules/apps/admin-apps-page.php:24
msgid "Popular Add-ons, New Possibilities."
msgstr "Popularne dodatki, nowe możliwości."

#: includes/widgets/video.php:941
msgid "Note: These controls have been deprecated and are only visible if they were previously in use. The video’s width and position are now set based on its aspect ratio."
msgstr "Uwaga: Te kontrolki zostały wycofane i są widoczne tylko wtedy, gdy były wcześniej używane. Szerokość i pozycja filmu są teraz ustawiane na podstawie jego współczynnika proporcji."

#. translators: 1: Link open tag, 2: Link open tag, 3: Link close tag.
#: core/kits/documents/tabs/settings-site-identity.php:60
msgid "Changes will be reflected only after %1$s saving %3$s and %2$s reloading %3$s preview."
msgstr "Zmiany zostaną uwzględnione dopiero po zapisaniu %3$s przez %1$s i ponownym wczytaniu podglądu %3$s przez %2$s."

#: includes/widgets/icon-box.php:229 includes/widgets/image-box.php:204
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1456
msgid "Box"
msgstr "Pole"

#: core/admin/admin.php:628
msgid "Build Smart with AI"
msgstr "Buduj inteligentnie ze SI"

#: modules/nested-accordion/widgets/nested-accordion.php:348
msgid "All collapsed"
msgstr "Wszystkie zwinięte"

#: modules/nested-accordion/widgets/nested-accordion.php:406
msgid "Space between Items"
msgstr "Odstęp między elementami"

#: includes/widgets/video.php:256
msgid "Choose Video File"
msgstr "Wybierz plik filmowy"

#: modules/nested-accordion/widgets/nested-accordion.php:358
msgid "Max Items Expanded"
msgstr "Maksymalna liczba rozszerzonych elementów"

#: includes/frontend.php:1390
msgid "Next slide"
msgstr "Następny slajd"

#: includes/frontend.php:1389
msgid "Previous slide"
msgstr "Poprzedni slajd"

#: core/document-types/page-base.php:277
#: includes/controls/groups/flex-item.php:80
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:176
msgid "Order"
msgstr "Kolejność"

#: includes/controls/groups/background.php:99 assets/js/ai-admin.js:11319
#: assets/js/ai-gutenberg.js:11537 assets/js/ai-media-library.js:11319
#: assets/js/ai-unify-product-images.js:11319 assets/js/ai.js:12871
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:178
msgid "Gradient"
msgstr "Gradient"

#: modules/floating-buttons/base/widget-floating-bars-base.php:340
#: modules/nested-accordion/widgets/nested-accordion.php:122
#: modules/nested-accordion/widgets/nested-accordion.php:123
msgid "Item Title"
msgstr "Tytuł elementu"

#: includes/controls/gallery.php:84 assets/js/editor.js:14721
msgid "Clear gallery"
msgstr "Wyczyść galerię"

#: core/document-types/page-base.php:289
msgid "Allow Comments"
msgstr "Zezwól na komentarze"

#: includes/frontend.php:1393
msgid "Go to slide"
msgstr "Idź do slajdu"

#: includes/frontend.php:1391
msgid "This is the first slide"
msgstr "To jest pierwszy slajd"

#: includes/editor-templates/navigator.php:81
msgid "Show/hide Element"
msgstr "Pokaż/ukryj element"

#: includes/editor-templates/navigator.php:68
msgid "Show/hide inner elements"
msgstr "Pokaż/ukryj wewnętrzne elementy"

#: includes/widgets/image-box.php:423 includes/widgets/image.php:398
msgid "Object Position"
msgstr "Pozycja obiektu"

#: includes/widgets/icon.php:326
msgid "Fit to Size"
msgstr "Dopasuj do rozmiaru"

#: includes/editor-templates/hotkeys.php:91
msgid "Panels"
msgstr "Panele"

#: includes/frontend.php:1392
msgid "This is the last slide"
msgstr "To jest ostatni slajd"

#: modules/nested-tabs/widgets/nested-tabs.php:384
msgid "Note: Scroll tabs if they don’t fit into their parent container."
msgstr "Uwaga: Przewiń karty, jeśli nie mieszczą się w swoim kontenerze nadrzędnym."

#: includes/widgets/toggle.php:157
msgid "You are currently editing a Toggle widget in its old version. Drag a new Accordion widget onto your page to use a newer version, providing nested capabilities."
msgstr "Obecnie edytujesz widżet Przełącz w jego starej wersji. Przeciągnij nowy widżet Akordeon na swoją stronę, aby użyć nowszej wersji, zapewniającej zagnieżdżone możliwości."

#: includes/widgets/accordion.php:154
msgid "You are currently editing an Accordion Widget in its old version. Any new Accordion widget dragged into the canvas will be the new Accordion widget, with the improved Nested capabilities."
msgstr "Obecnie edytujesz widżet Akordeon w jego starej wersji. Każdy nowy widżet Akordeon przeciągnięty do obszaru roboczego będzie nowym widżetem Akordeon z ulepszonymi możliwościami Nested."

#: includes/editor-templates/navigator.php:58
msgid "Resize structure"
msgstr "Zmień rozmiar struktury"

#. translators: %d: Item index.
#: modules/nested-accordion/widgets/nested-accordion.php:68
msgid "item #%d"
msgstr "element nr %d"

#: modules/nested-tabs/widgets/nested-tabs.php:382
msgid "Horizontal Scroll"
msgstr "Przewijanie poziome"

#: modules/floating-buttons/base/widget-floating-bars-base.php:376
#: modules/nested-accordion/widgets/nested-accordion.php:162
msgid "Item #3"
msgstr "Element nr 3"

#: modules/floating-buttons/base/widget-floating-bars-base.php:370
#: modules/nested-accordion/widgets/nested-accordion.php:156
msgid "Item #1"
msgstr "Element nr 1"

#: modules/floating-buttons/base/widget-floating-bars-base.php:373
#: modules/nested-accordion/widgets/nested-accordion.php:159
msgid "Item #2"
msgstr "Element nr 2"

#: includes/template-library/sources/local.php:515
msgid "Invalid template type."
msgstr "Nieprawidłowy rodzaj szablonu."

#: core/admin/admin.php:347
msgid "Get Elementor Pro"
msgstr "Zdobądź Elementor Pro"

#: includes/editor-templates/global.php:68 includes/elements/container.php:96
#: includes/elements/container.php:104 includes/elements/container.php:379
#: assets/js/editor.js:33688 assets/js/editor.js:42750
msgid "Grid"
msgstr "Siatka"

#: includes/editor-templates/global.php:52
msgid "Which layout would you like to use?"
msgstr "Którego układu chciałbyś użyć?"

#: includes/controls/gaps.php:58
#: includes/controls/groups/grid-container.php:118
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:42
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:182
msgid "Row"
msgstr "Wiersz"

#: includes/controls/groups/grid-container.php:71
msgid "Rows"
msgstr "Wiersze"

#: modules/floating-buttons/base/widget-contact-button-base.php:116
msgid "Top Bar"
msgstr "Górny pasek"

#: includes/editor-templates/navigator.php:32 assets/js/editor.js:35748
msgid "Expand all elements"
msgstr "Rozwiń wszystkie elementy"

#: includes/editor-templates/global.php:9
msgid "Select your structure"
msgstr "Wybierz strukturę"

#: includes/elements/container.php:373
msgid "Container Layout"
msgstr "Układ kontenera"

#: includes/controls/base-units.php:130
msgid "Switch units"
msgstr "Przełącz jednostki"

#. translators: %s: Page title.
#. translators: %s: Document title.
#: core/editor/loader/v1/templates/editor-body-v1-view.php:29
#: core/editor/loader/v2/templates/editor-body-v2-view.php:29
#: includes/editor-templates/editor-wrapper.php:32
#: assets/js/packages/editor-documents/editor-documents.js:2
#: assets/js/packages/editor-documents/editor-documents.strings.js:2
msgid "Edit \"%s\" with Elementor"
msgstr "Edytuj „%s” w Elementorze"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/widgets/image-carousel.php:391
#: includes/widgets/image-gallery.php:204 includes/widgets/image.php:229
msgid "Manage your site’s lightbox settings in the %1$sLightbox panel%2$s."
msgstr "Zarządzaj ustawieniami lightbox swojej witryny w %1$spanelu Lightbox%2$s."

#: includes/editor-templates/navigator.php:36
msgid "Close structure"
msgstr "Zamknij strukturę"

#: includes/controls/groups/grid-container.php:115
msgid "Auto Flow"
msgstr "Przepływ automatyczny"

#: includes/controls/groups/grid-container.php:31
msgid "Grid Outline"
msgstr "Zarys siatki"

#. translators: 1: Link open tag, 2: Link close tag.
#: core/document-types/page-base.php:100
msgid "Set a different selector for the title in the %1$sLayout panel%2$s."
msgstr "Ustaw inny selektor dla tytułu w %1$sPanelu układu%2$s."

#: core/kits/documents/tabs/settings-layout.php:100
#: includes/controls/groups/flex-container.php:156
#: includes/controls/groups/grid-container.php:96
#: includes/elements/container.php:511
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:168
msgid "Gaps"
msgstr "Odstępy"

#: includes/editor-templates/global.php:60 includes/elements/container.php:378
#: modules/atomic-widgets/elements/flexbox/flexbox.php:34
#: modules/atomic-widgets/library/flexbox.php:54
msgid "Flexbox"
msgstr "Elastyczne pole"

#: modules/ai/connect/ai.php:27 assets/js/ai-admin.js:657
#: assets/js/ai-admin.js:7770 assets/js/ai-gutenberg.js:795
#: assets/js/ai-gutenberg.js:7988 assets/js/ai-layout.js:489
#: assets/js/ai-layout.js:3258 assets/js/ai-media-library.js:657
#: assets/js/ai-media-library.js:7770 assets/js/ai-unify-product-images.js:657
#: assets/js/ai-unify-product-images.js:7770 assets/js/ai.js:1445
#: assets/js/ai.js:9322
msgid "AI"
msgstr "SI"

#: includes/editor-templates/panel-elements.php:15
msgid "Globals"
msgstr "Globalne"

#: core/breakpoints/manager.php:329
msgid "Tablet Landscape"
msgstr "Poziomy tablet"

#: core/breakpoints/manager.php:324
msgid "Tablet Portrait"
msgstr "Pionowy tablet"

#: core/admin/admin-notices.php:762 core/admin/admin.php:1036
msgid "Dismiss this notice."
msgstr "Odrzuć to powiadomienie."

#: includes/widgets/alert.php:506 includes/widgets/alert.php:561
msgid "Dismiss this alert."
msgstr "Odrzuć ten alert."

#: includes/elements/column.php:211 includes/widgets/icon-list.php:541
msgid "Horizontal Alignment"
msgstr "Wyrównanie poziome"

#: includes/widgets/icon-list.php:598
msgid "Adjust Vertical Position"
msgstr "Dostosuj położenie pionowe"

#: core/experiments/manager.php:541
msgid "Deactivate All"
msgstr "Wyłącz wszystko"

#: core/experiments/manager.php:540
msgid "Activate All"
msgstr "Włącz wszystko"

#: core/kits/documents/tabs/theme-style-form-fields.php:188
msgid "Accent Color"
msgstr "Kolor akcentu"

#: core/experiments/manager.php:517
msgid "Experiments and Features"
msgstr "Eksperymenty i funkcje"

#: modules/generator-tag/module.php:84
msgid "A generator tag is a meta element that indicates the attributes used to create a webpage. It is used for analytical purposes."
msgstr "Znacznik generatora to element meta, który wskazuje atrybuty użyte do utworzenia strony internetowej. Jest używany do celów analitycznych."

#. translators: %d: Number of rows.
#: includes/utils.php:254
msgid "%d database row affected."
msgid_plural "%d database rows affected."
msgstr[0] "%d wiersz bazy danych został zmieniony."
msgstr[1] "%d wiersze bazy danych zostało zmienione."
msgstr[2] "%d wierszy bazy danych zostało zmienionych."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/experiments/manager.php:523
msgid "Personalize your Elementor experience by controlling which features and experiments are active on your site. Help make Elementor better by %1$ssharing your experience and feedback with us%2$s."
msgstr "Spersonalizuj swoje doświadczenie Elementora, kontrolując, które funkcje i eksperymenty są włączone na Twojej stronie. Pomóż ulepszyć Elementora, %1$sdzieląc się z nami swoim doświadczeniem i opiniami%2$s."

#: modules/generator-tag/module.php:76
msgid "Generator Tag"
msgstr "Generator znacznika"

#: modules/atomic-opt-in/opt-in-page.php:90
#: modules/atomic-widgets/opt-in.php:41 assets/js/editor-v4-opt-in.js:344
#: assets/js/editor-v4-opt-in.js:502
msgid "Editor V4"
msgstr "Edytor V4"

#: modules/nested-tabs/widgets/nested-tabs.php:718
msgid "Titles"
msgstr "Tytuły"

#: includes/widgets/video.php:865
msgid "Shadow"
msgstr "Cień"

#. translators: 1: Breakpoint label, 2: `>` character, 3: Breakpoint value.
#: modules/nested-tabs/widgets/nested-tabs.php:427
msgid "%1$s (%2$s %3$dpx)"
msgstr "%1$s (%2$s %3$dpx)"

#: includes/widgets/video.php:599
msgid "Metadata"
msgstr "Metadane"

#: modules/nested-tabs/widgets/nested-tabs.php:347
msgid "Align Title"
msgstr "Wyrównanie tytułu"

#: modules/atomic-widgets/module.php:203 modules/nested-elements/module.php:17
msgid "Nested Elements"
msgstr "Elementy zagnieżdżone"

#: modules/nested-elements/module.php:20
msgid "Create a rich user experience by layering widgets together inside \"Nested\" Tabs, etc. When turned on, we’ll automatically enable new nested features. Your old widgets won’t be affected."
msgstr "Ułatwia tworzenie bogatych doświadczeń użytkownika poprzez układanie widżetów wewnątrz \"zagnieżdżonych\" kart, itp. Po uruchomieniu, automatycznie włączymy nowe funkcje zagnieżdżone. Nie będzie to miało wpływu na stare widżety."

#: includes/widgets/video.php:605
msgid "Preload attribute lets you specify how the video should be loaded when the page loads."
msgstr "Atrybut Wczytuj wstępnie pozwala określić, jak powinien zostać wczytany film po wczytaniu strony."

#: includes/widgets/tabs.php:153
msgid "You are currently editing a Tabs Widget in its old version. Any new tabs widget dragged into the canvas will be the new Tab widget, with the improved Nested capabilities."
msgstr "Obecnie edytujesz widżet Karty w jego starej wersji. Każdy nowy widżet Karty przeciągnięty do obszaru roboczego będzie nowym widżetem Kart z ulepszonymi możliwościami Nested."

#. translators: 1: Link opening tag, 2: Link closing tag
#: includes/settings/settings.php:375
msgid "Disable this option if you want to prevent Google Fonts from being loaded. This setting is recommended when loading fonts from a different source (plugin, theme or %1$scustom fonts%2$s)."
msgstr "Wyłącz tę opcję, jeśli chcesz zapobiec wczytywaniu kroju pisma Google. To ustawienie jest zalecane podczas wczytywania kroju pisma z innego źródła (wtyczki, motywu lub %1$skroju pisma niestandardowego%2$s)."

#: includes/settings/settings.php:365
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:253
msgid "Google Fonts"
msgstr "Kroje pisma Google"

#: includes/widgets/video.php:596
msgid "Preload"
msgstr "Wstępne wczytywanie"

#: modules/nested-tabs/widgets/nested-tabs.php:183
msgid "Tab #3"
msgstr "Karta nr 3"

#. translators: %d: Tab index.
#: modules/nested-tabs/widgets/nested-tabs.php:63
#: modules/nested-tabs/widgets/nested-tabs.php:85
msgid "Tab #%d"
msgstr "Karta nr %d"

#: modules/nested-tabs/widgets/nested-tabs.php:439
msgid "Note: Choose at which breakpoint tabs will automatically switch to a vertical (“accordion”) layout."
msgstr "Uwaga: Wybierz punkt zmiany, w którym karty automatycznie przełączą się na układ pionowy („akordeon”)."

#: modules/nested-accordion/widgets/nested-accordion.php:432
#: modules/nested-tabs/widgets/nested-tabs.php:474
msgid "Distance from content"
msgstr "Odstęp od treści"

#: modules/nested-tabs/widgets/nested-tabs.php:454
msgid "Gap between tabs"
msgstr "Odstęp między kartami"

#: includes/elements/container.php:1941
msgid "Note: Avoid applying transform properties on sticky containers. Doing so might cause unexpected results."
msgstr "Uwaga: Unikaj stosowania właściwości transformacji w kontenerach przypiętych. Może to spowodować nieoczekiwane rezultaty."

#: includes/settings/settings.php:459
msgid "Lazy Load Background Images"
msgstr "Obrazki tła leniwego wczytywania"

#: core/experiments/manager.php:613
msgid "Requires"
msgstr "Wymaga"

#: core/admin/admin.php:1024 core/utils/hints.php:460
#: modules/apps/admin-apps-page.php:111 modules/apps/admin-apps-page.php:142
#: modules/home/<USER>/filter-plugins.php:82 assets/js/admin.js:795
#: assets/js/editor-v4-opt-in.js:355
msgid "Activate"
msgstr "Włącz"

#: includes/elements/container.php:580
msgid "(link)"
msgstr "(odnośnik)"

#: includes/controls/groups/typography.php:150
msgctxt "Typography Control"
msgid "(Black)"
msgstr "(czarna)"

#: includes/controls/groups/typography.php:149
msgctxt "Typography Control"
msgid "(Extra Bold)"
msgstr "(bardzo pogrubiona)"

#: includes/controls/groups/typography.php:148
msgctxt "Typography Control"
msgid "(Bold)"
msgstr "(pogrubiona)"

#: includes/controls/groups/typography.php:147
msgctxt "Typography Control"
msgid "(Semi Bold)"
msgstr "(pół pogrubiona)"

#: includes/controls/groups/typography.php:146
msgctxt "Typography Control"
msgid "(Medium)"
msgstr "(średnia)"

#: includes/controls/groups/typography.php:145
msgctxt "Typography Control"
msgid "(Normal)"
msgstr "(normalna)"

#: includes/controls/groups/typography.php:144
msgctxt "Typography Control"
msgid "(Light)"
msgstr "(lekka)"

#: includes/controls/groups/typography.php:143
msgctxt "Typography Control"
msgid "(Extra Light)"
msgstr "(bardzo lekka)"

#: includes/controls/groups/typography.php:142
msgctxt "Typography Control"
msgid "(Thin)"
msgstr "(cienka)"

#: app/modules/import-export-customization/module.php:178
#: app/modules/import-export-customization/module.php:181
#: app/modules/import-export-customization/module.php:188
#: app/modules/import-export/module.php:183
#: app/modules/import-export/module.php:186
#: app/modules/import-export/module.php:193
msgid "imported kit"
msgstr "importowany zestaw"

#. translators: 1: kit title, 2: date, 3: line break, 4: kit title, 5: date.
#. translators: 1: Last imported kit title, 2: Last imported kit date, 3: Line
#. break <br>, 4: Penultimate imported kit title, 5: Penultimate imported kit
#. date.
#: app/modules/import-export-customization/module.php:177
#: app/modules/import-export/module.php:182
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s %3$s and revert to the site setting that came with \"%4$s\" on %5$s."
msgstr "Usuń wszystkie ustawienia treści i witryny dostarczone z „%1$s” na %2$s %3$s i przywróć ustawienia witryny dostarczone z „%4$s” na %5$s."

#. translators: 1: kit title, 2: date, 3: line break
#. translators: 1: Last imported kit title, 2: Last imported kit date, 3: Line
#. break <br>.
#: app/modules/import-export-customization/module.php:187
#: app/modules/import-export/module.php:192
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s.%3$s Your original site settings will be restored."
msgstr "Usuń całą treść i ustawienia witryny dostarczone z „%1$s” w dniu %2$s.%3$s Twoje oryginalne ustawienia witryny zostaną przywrócone."

#: includes/widgets/alert.php:161 includes/widgets/alert.php:342
msgid "Dismiss Icon"
msgstr "Ikonka odrzucenia"

#: includes/elements/container.php:601
msgid "Don’t add links to elements nested in this container - it will break the layout."
msgstr "Nie dodawaj odnośników do elementów zagnieżdżonych w tym kontenerze - popsuje to aktualny układ."

#: core/admin/admin-notices.php:407
msgid "Try it out"
msgstr "Wypróbuj"

#: core/settings/editor-preferences/model.php:209
msgid "All Posts"
msgstr "Wszystkie wpisy"

#: core/settings/editor-preferences/model.php:208
msgid "This Post"
msgstr "Ten wpis"

#: core/common/modules/finder/categories/settings.php:64
#: core/experiments/manager.php:313 core/experiments/manager.php:371
#: includes/settings/settings.php:401 includes/settings/settings.php:404
msgid "Performance"
msgstr "Wydajność"

#: core/kits/documents/tabs/settings-layout.php:91
msgid "Sets the default space inside the container (Default is 10px)"
msgstr "Ustawia domyślną przestrzeń wewnątrz kontenera (domyślnie 10px)"

#: core/settings/editor-preferences/model.php:204
msgid "Exit to"
msgstr "Wyjdź do"

#: core/admin/admin-notices.php:403
msgid "Improve your site’s performance score."
msgstr "Popraw wynik wydajności swojej witryny."

#. translators: 1: Opening HTML <a> tag, 2: closing HTML <a> tag.
#: includes/editor-templates/panel.php:57
msgid "Any time you can change the settings in %1$sUser Preferences%2$s"
msgstr "W każdym momencie możesz zmienić ustawienia w %1$sPreferencjach użytkownika%2$s"

#: core/settings/editor-preferences/model.php:210
msgid "WP Dashboard"
msgstr "Kokpit WP"

#: includes/editor-templates/panel.php:53
msgid "Now you can choose where you want to go on the site from the following options"
msgstr "Teraz możesz wybrać, gdzie chcesz się udać na stronie, spośród następujących opcji"

#: app/modules/onboarding/module.php:158
msgid "There was a problem setting your site name."
msgstr "Wystąpił problem podczas ustawiania nazwy witryny."

#: includes/widgets/alert.php:394
#: modules/floating-buttons/base/widget-contact-button-base.php:2936
msgid "Horizontal Position"
msgstr "Pozycja pozioma"

#: includes/widgets/alert.php:376
#: modules/floating-buttons/base/widget-contact-button-base.php:2990
#: modules/floating-buttons/base/widget-floating-bars-base.php:1433
msgid "Vertical Position"
msgstr "Pozycja pionowa"

#: core/admin/admin-notices.php:404
msgid "With our experimental speed boosting features you can go faster than ever before. Look for the Performance label on our Experiments page and activate those experiments to improve your site loading speed."
msgstr "Dzięki naszym eksperymentalnym funkcjom zwiększającym szybkość możesz działać szybciej niż kiedykolwiek wcześniej. Szukaj etykiety „Wydajność” na naszej stronie „Eksperymenty” i włącz te eksperymenty, aby poprawić szybkość wczytywania witryny."

#: core/kits/documents/tabs/settings-layout.php:88
msgid "Container Padding"
msgstr "Dopełnienie kontenera"

#. translators: %d: Item index.
#. Translators: %s is the current item index.
#: modules/nested-accordion/widgets/nested-accordion.php:90
#: modules/nested-elements/base/widget-nested-base.php:46
#: assets/js/editor.js:25305
msgid "Item #%d"
msgstr "Element nr %d"

#: includes/widgets/image-carousel.php:248
msgid "Previous Arrow Icon"
msgstr "Ikonka strzałki Poprzednia"

#: includes/widgets/image-carousel.php:303
msgid "Next Arrow Icon"
msgstr "Ikonka strzałki Następna"

#: core/editor/notice-bar.php:45 assets/js/element-manager-admin.js:725
msgid "Unleash the full power of Elementor's features and web creation tools."
msgstr "Uwolnij pełną moc funkcji Elementora i narzędzi do tworzenia stron internetowych."

#: includes/editor-templates/hotkeys.php:184 assets/js/notes.js:135
#: assets/js/notes.js:139 assets/js/notes.js:225
msgid "Notes"
msgstr "Notatki"

#: core/admin/admin.php:632 core/role-manager/role-manager.php:241
#: includes/editor-templates/panel-elements.php:40
#: includes/editor-templates/panel-elements.php:46
#: includes/editor-templates/panel-elements.php:65
#: includes/editor-templates/panel.php:322
#: includes/editor-templates/templates.php:493
#: includes/managers/controls.php:1152 includes/widgets/button.php:103
#: includes/widgets/heading.php:158 includes/widgets/image-carousel.php:127
#: includes/widgets/image-gallery.php:107 includes/widgets/testimonial.php:113
#: includes/widgets/video.php:128
#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:30
#: modules/promotions/promotion-data.php:41
#: modules/promotions/promotion-data.php:58
#: modules/promotions/promotion-data.php:75
#: modules/promotions/promotion-data.php:92
#: modules/promotions/promotion-data.php:109 assets/js/ai-admin.js:1025
#: assets/js/ai-admin.js:2961 assets/js/ai-admin.js:3089
#: assets/js/ai-gutenberg.js:1163 assets/js/ai-gutenberg.js:3099
#: assets/js/ai-gutenberg.js:3227 assets/js/ai-layout.js:723
#: assets/js/ai-layout.js:1002 assets/js/ai-media-library.js:1025
#: assets/js/ai-media-library.js:2961 assets/js/ai-media-library.js:3089
#: assets/js/ai-unify-product-images.js:1025
#: assets/js/ai-unify-product-images.js:2961
#: assets/js/ai-unify-product-images.js:3089 assets/js/ai.js:1813
#: assets/js/ai.js:3749 assets/js/ai.js:3877 assets/js/app-packages.js:4730
#: assets/js/app.js:8965 assets/js/e-react-promotions.js:196
#: assets/js/editor.js:4710
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:1362
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:3152
#: assets/js/notes.js:148
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:552
#: assets/js/styleguide.js:196
msgid "Upgrade"
msgstr "Ulepsz"

#: core/editor/notice-bar.php:41 core/editor/promotion.php:34
#: includes/editor-templates/navigator.php:17
#: includes/editor-templates/panel-elements.php:29
#: includes/editor-templates/panel-elements.php:34
#: includes/editor-templates/panel-elements.php:103
#: includes/managers/controls.php:1161 includes/widgets/button.php:107
#: includes/widgets/heading.php:161 includes/widgets/image-carousel.php:130
#: includes/widgets/image-gallery.php:110 includes/widgets/testimonial.php:116
#: includes/widgets/video.php:132 modules/admin-top-bar/module.php:79
#: modules/checklist/steps/setup-header.php:93
#: modules/element-manager/ajax.php:73 modules/element-manager/ajax.php:80
#: modules/element-manager/ajax.php:88
#: modules/pro-install/pro-install-menu-item.php:152
#: modules/promotions/admin-menu-items/base-promotion-item.php:32
#: modules/promotions/admin-menu-items/base-promotion-template.php:37
#: modules/promotions/admin-menu-items/popups-promotion-item.php:24
#: modules/promotions/promotion-data.php:48
#: modules/promotions/promotion-data.php:65
#: modules/promotions/promotion-data.php:82
#: modules/promotions/promotion-data.php:99
#: modules/promotions/promotion-data.php:116 assets/js/app-packages.js:920
#: assets/js/app-packages.js:4489 assets/js/app-packages.js:4765
#: assets/js/app.js:1263 assets/js/checklist.js:241 assets/js/editor.js:6189
#: assets/js/editor.js:10916 assets/js/editor.js:52939
#: assets/js/editor.js:52954 assets/js/editor.js:52969
#: assets/js/editor.js:52984
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:300
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:1279
msgid "Upgrade Now"
msgstr "Ulepsz teraz"

#: modules/announcements/module.php:118
msgid "Let's do it"
msgstr "Zróbmy to"

#: modules/container-converter/module.php:86
#: modules/container-converter/module.php:119
msgid "Convert"
msgstr "Konwertuj"

#: includes/controls/groups/typography.php:153
msgid "Bold"
msgstr "Pogrubienie"

#: elementor.php:83 elementor.php:107 assets/js/ai-admin.js:1064
#: assets/js/ai-gutenberg.js:1202 assets/js/ai-layout.js:762
#: assets/js/ai-media-library.js:1064 assets/js/ai-unify-product-images.js:1064
#: assets/js/ai.js:1852 assets/js/app.js:7377 assets/js/app.js:11663
msgid "Show me how"
msgstr "Pokaż mi jak"

#: modules/container-converter/module.php:85
#: modules/container-converter/module.php:118
msgid "Convert to container"
msgstr "Konwersja do kontenera"

#. translators: %s: 100vh.
#: includes/elements/container.php:495
msgid "To achieve full height Container use %s."
msgstr "Aby osiągnąć pełną wysokość, użyj Kontenera %s."

#: core/experiments/manager.php:342 includes/elements/container.php:72
#: includes/elements/container.php:357
#: modules/library/documents/container.php:52 assets/js/editor.js:8497
#: assets/js/editor.js:33688 assets/js/editor.js:39522
msgid "Container"
msgstr "Kontener"

#: includes/elements/column.php:461 includes/elements/section.php:730
#: includes/widgets/heading.php:338
msgid "Hue"
msgstr "Odcień"

#: includes/elements/column.php:460 includes/elements/section.php:729
#: includes/widgets/heading.php:337
msgid "Exclusion"
msgstr "Wykluczenie"

#: includes/elements/column.php:459 includes/elements/section.php:728
#: includes/widgets/heading.php:336
msgid "Difference"
msgstr "Różnica"

#: core/common/modules/finder/categories/tools.php:68
msgid "Import Export"
msgstr "Import Eksport"

#: core/common/modules/event-tracker/personal-data.php:24
msgid "Elementor Event Tracker"
msgstr "Śledzenie zdarzeń Elementora"

#: core/kits/views/panel.php:36
msgid "Reorder"
msgstr "Zmień kolejność"

#: core/kits/documents/tabs/global-colors.php:101
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:178
msgid "System Colors"
msgstr "Kolory systemowe"

#: app/modules/onboarding/module.php:265 app/modules/onboarding/module.php:350
msgid "There was a problem uploading your file."
msgstr "Wystąpił problem podczas wgrywania twojego pliku."

#: app/modules/onboarding/module.php:215
msgid "There was a problem setting your site logo."
msgstr "Wystąpił problem z ustawieniem logo twojej strony."

#: includes/editor-templates/global.php:33 assets/js/editor.js:33547
msgid "Add New Container"
msgstr "Utwórz kontener"

#: includes/widgets/image-carousel.php:635
msgid "Pagination"
msgstr "Stronicowanie"

#: includes/controls/groups/background.php:727
#: includes/widgets/image-carousel.php:434
msgid "Lazyload"
msgstr "Leniwe wczytywanie"

#: core/kits/documents/tabs/global-typography.php:160
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:227
msgid "System Fonts"
msgstr "Kroje pisma systemowe"

#: modules/container-converter/module.php:88
#: modules/container-converter/module.php:121
msgid "Copies all of the selected sections and columns and pastes them in a container beneath the original."
msgstr "Kopiuje wszystkie zaznaczone sekcje i kolumny i wkleja je do kontenera pod oryginałem."

#: includes/widgets/video.php:1001
msgid "Play Video about"
msgstr "Odtwórz film o"

#: modules/library/documents/page.php:65
msgid "Add New Page Template"
msgstr "Utwórz szablon strony"

#: core/utils/hints.php:462
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:115
msgid "Customize"
msgstr "Spersonalizuj"

#. translators: 1. "Terms of service" link, 2. "Privacy policy" link
#: includes/admin-templates/beta-tester.php:44
msgid "By clicking Sign Up, you agree to Elementor's %1$s and %2$s"
msgstr "Klikając przycisk Zarejestruj się, zgadzasz się na warunki Elementora %1$s i %2$s"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:19
msgid "Custom Code"
msgstr "Własny kod"

#: modules/usage/usage-reporter.php:22
msgid "Elements Usage"
msgstr "Wykorzystanie elementów"

#: core/logger/log-reporter.php:25
msgid "Log"
msgstr "Dziennik"

#: includes/base/element-base.php:987
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:152
msgid "Perspective"
msgstr "Perspektywa"

#: includes/admin-templates/beta-tester.php:53 assets/js/ai-admin.js:6382
#: assets/js/ai-admin.js:15897 assets/js/ai-gutenberg.js:6600
#: assets/js/ai-gutenberg.js:16115 assets/js/ai-layout.js:2488
#: assets/js/ai-layout.js:5196 assets/js/ai-media-library.js:6382
#: assets/js/ai-media-library.js:15897
#: assets/js/ai-unify-product-images.js:6382
#: assets/js/ai-unify-product-images.js:15897 assets/js/ai.js:7832
#: assets/js/ai.js:7934 assets/js/ai.js:17449
msgid "Privacy Policy"
msgstr "Polityka prywatności"

#. translators: 1: Integration settings link open tag, 2: Create API key link
#. open tag, 3: Link close tag.
#: includes/widgets/google-maps.php:140
msgid "Set your Google Maps API Key in Elementor's %1$sIntegrations Settings%3$s page. Create your key %2$shere.%3$s"
msgstr "Ustaw klucz API Google Maps na stronie %1$sustawień integracji%3$s w Elementorze. Utwórz swój klucz %2$stutaj.%3$s"

#: core/experiments/manager.php:503
msgid "Stable Features"
msgstr "Stabilne funkcje"

#: includes/controls/groups/text-stroke.php:85
msgid "Stroke Color"
msgstr "Kolor obrysu"

#: includes/controls/groups/text-stroke.php:60
#: includes/controls/groups/text-stroke.php:111
msgid "Text Stroke"
msgstr "Obrys tekstu"

#: includes/base/element-base.php:1088
msgid "Keep Proportions"
msgstr "Zachowaj proporcje"

#: includes/base/element-base.php:1048
msgid "Offset Y"
msgstr "Przesunięcie Y"

#: includes/base/element-base.php:941
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:161
msgid "Rotate X"
msgstr "Obrót X"

#: includes/base/element-base.php:964
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:162
msgid "Rotate Y"
msgstr "Obrót Y"

#: includes/base/element-base.php:1121
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:158
msgid "Scale X"
msgstr "Skala X"

#: includes/base/element-base.php:1143
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:159
msgid "Scale Y"
msgstr "Skala Y"

#: includes/base/element-base.php:1167
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:111
#: assets/js/packages/editor-controls/editor-controls.strings.js:116
msgid "Skew"
msgstr "Pochylenie"

#: includes/base/element-base.php:1179
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:156
msgid "Skew X"
msgstr "Pochylenie X"

#: includes/base/element-base.php:1201
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:157
msgid "Skew Y"
msgstr "Pochylenie Y"

#: includes/base/element-base.php:1318
msgid "X Anchor Point"
msgstr "Punkt kotwiczenia X"

#: includes/base/element-base.php:1346
msgid "Y Anchor Point"
msgstr "Punkt kotwiczenia Y"

#: includes/admin-templates/beta-tester.php:48 assets/js/ai-admin.js:6378
#: assets/js/ai-admin.js:15893 assets/js/ai-gutenberg.js:6596
#: assets/js/ai-gutenberg.js:16111 assets/js/ai-layout.js:2484
#: assets/js/ai-layout.js:5192 assets/js/ai-media-library.js:6378
#: assets/js/ai-media-library.js:15893
#: assets/js/ai-unify-product-images.js:6378
#: assets/js/ai-unify-product-images.js:15893 assets/js/ai.js:7828
#: assets/js/ai.js:7930 assets/js/ai.js:17445
msgid "Terms of Service"
msgstr "Warunki usługi"

#: includes/base/element-base.php:861
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:106
#: assets/js/packages/editor-controls/editor-controls.strings.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:151
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:217
msgid "Transform"
msgstr "Przekształcenie"

#: includes/base/element-base.php:1225 includes/base/element-base.php:1229
msgid "Flip Horizontal"
msgstr "Odwróć poziomo"

#: includes/base/element-base.php:1244 includes/base/element-base.php:1248
msgid "Flip Vertical"
msgstr "Odwróć pionowo"

#: core/editor/promotion.php:31 assets/js/e-react-promotions.js:196
#: assets/js/editor.js:4710 assets/js/notes.js:148 assets/js/styleguide.js:196
msgid "Connect & Activate"
msgstr "Połącz i włącz"

#: includes/base/element-base.php:1022
msgid "Offset X"
msgstr "Przesunięcie X"

#: includes/base/element-base.php:924
msgid "3D Rotate"
msgstr "Obrót 3D"

#: core/experiments/manager.php:546
msgid "Ongoing Experiments"
msgstr "Trwające eksperymenty"

#: core/experiments/experiments-reporter.php:21
msgid "Elementor Experiments"
msgstr "Eksperymenty z Elementorem"

#: core/editor/data/globals/endpoints/base.php:46
msgid "Invalid title"
msgstr "Nieprawidłowy tytuł"

#: core/experiments/manager.php:312
msgid "Inline Font Icons"
msgstr "Ikonki kroju pisma wbudowanego"

#: core/experiments/manager.php:316
msgid "The “Inline Font Icons” will render the icons as inline SVG without loading the Font-Awesome and the eicons libraries and its related CSS files and fonts."
msgstr "Opcja „Ikonki kroju pisma wbudowanego” spowoduje wyświetlenie ikonek jako wbudowane SVG bez wczytywania bibliotek Font-Awesome i eicons oraz powiązanych z nimi plików CSS i krojów pisma."

#: includes/settings/tools.php:158
msgid "Not allowed to rollback versions"
msgstr "Nie wolno wycofywać wersji"

#: modules/page-templates/module.php:316
msgid "The default page template as defined in Elementor Panel → Hamburger Menu → Site Settings."
msgstr "Domyślny szablon strony zdefiniowany w Panelu Elementor → Menu hamburgera → Ustawienia witryny."

#: includes/elements/column.php:453 includes/elements/container.php:864
#: includes/elements/section.php:721 includes/widgets/heading.php:330
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:175
msgid "Overlay"
msgstr "Nakładka"

#: includes/elements/column.php:452 includes/elements/container.php:863
#: includes/elements/section.php:720 includes/widgets/heading.php:329
msgid "Screen"
msgstr "Ekran"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:261 modules/safe-mode/module.php:274
msgid "%1$sClick here%2$s to troubleshoot"
msgstr "%1$sKliknij tutaj%2$s, aby rozwiązać problem"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/history/views/revisions-panel-template.php:31
msgid "Learn more about %1$sWordPress revisions%2$s"
msgstr "Dowiedz się więcej o %1$swersjach WordPressa%2$s"

#. translators: 1: Function argument, 2: Elementor version number.
#: modules/dev-tools/deprecation.php:292
msgid "The %1$s argument is deprecated since version %2$s!"
msgstr "Argument %1$s jest przestarzały od wersji %2$s!"

#. translators: 1: Function argument, 2: Elementor version number, 3:
#. Replacement argument name.
#: modules/dev-tools/deprecation.php:288
msgid "The %1$s argument is deprecated since version %2$s! Use %3$s instead."
msgstr "Argument %1$s jest przestarzały od wersji %2$s! Zamiast tego użyj %3$s."

#: includes/elements/column.php:457 includes/elements/container.php:868
#: includes/elements/section.php:725 includes/widgets/heading.php:334
msgid "Saturation"
msgstr "Nasycenie"

#. translators: %s: Device name.
#: includes/base/element-base.php:1393
msgid "Hide On %s"
msgstr "Ukryj na %s"

#: includes/elements/column.php:455 includes/elements/container.php:866
#: includes/elements/section.php:723 includes/widgets/heading.php:332
msgid "Lighten"
msgstr "Rozjaśnienie"

#: includes/elements/column.php:456 includes/elements/container.php:867
#: includes/elements/section.php:724 includes/widgets/heading.php:333
msgid "Color Dodge"
msgstr "Rozjaśnienie kolorów"

#: includes/elements/column.php:462 includes/elements/container.php:870
#: includes/elements/section.php:727 includes/widgets/heading.php:339
msgid "Luminosity"
msgstr "Jasność"

#: core/kits/documents/tabs/settings-page-transitions.php:19
#: includes/managers/controls.php:1120
msgid "Page Transitions"
msgstr "Przejścia między stronami"

#: core/kits/documents/tabs/settings-layout.php:371
msgid "Widescreen breakpoint settings will apply from the selected value and up."
msgstr "Ustawienia punktu zmiany dla szerokiego ekranu będą stosowane od wybranej wartości wzwyż."

#: core/experiments/manager.php:329
msgid "Additional Custom Breakpoints"
msgstr "Dodatkowe niestandardowe punkty zmiany"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/maintenance-mode.php:375
msgid "Select one or go ahead and %1$screate one%2$s now."
msgstr "Wybierz jedną z nich lub %1$sutwórz teraz%2$s."

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-template.php:52
msgid "Templates Help You %1$sWork Efficiently%2$s"
msgstr "Szablony pomogą %1$spracować wydajnie%2$s"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/editor-templates/panel.php:207
msgid "You can enable it from the %1$sElementor settings page%2$s."
msgstr "Można ją włączyć na stronie %1$sestawień Elementora%2$s."

#. translators: %d: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:26
msgid "Widescreen <br> Settings added for the Widescreen device will apply to screen sizes %dpx and up"
msgstr "Ustawienia<br> szerokoekranowe dodane dla urządzenia szerokoekranowego będą miały zastosowanie do rozmiarów ekranu %dpx i większych"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:403
msgid "%1$sClick here%2$s %3$sto join our first-to-know email updates.%4$s"
msgstr "%1$sKliknij tutaj%2$s %3$s, aby jako jeden z pierwszych otrzymywać aktualizacje na skrzynkę e-mail.%4$s"

#: includes/managers/controls.php:1126
msgid "Page Transitions let you style entrance and exit animations between pages as well as display loader until your page assets load."
msgstr "Przejścia stron umożliwiają stylizowanie animacji wejścia i wyjścia między stronami, a także wyświetlanie modułu wczytującego do momentu wczytania zasobów strony."

#: core/experiments/manager.php:332
msgid "Get pixel-perfect design for every screen size. You can now add up to 6 customizable breakpoints beyond the default desktop setting: mobile, mobile extra, tablet, tablet extra, laptop, and widescreen."
msgstr "Uzyskaj projekt idealny co do piksela dla każdego rozmiaru ekranu. Możesz teraz dodać do 6 konfigurowalnych punktów zmiany poza domyślnym ustawieniem dla komputerów stacjonarnych: urządzenie mobilne, dodatkowe urządzenie mobilne, tablet, dodatkowe urządzenie tablet, laptop i ekran panoramiczny."

#. translators: 1: Link open tag, 2: Link close tag.
#: core/kits/documents/tabs/tab-base.php:80
msgid "In order for Theme Style to affect all relevant Elementor elements, please disable Default Colors and Fonts from the %1$sSettings Page%2$s."
msgstr "Aby styl motywu miał wpływ na wszystkie odpowiednie elementy Elementora, wyłącz opcję Domyślne kolory i kroje pisma na %1$sstronie ustawień%2$s."

#: includes/managers/controls.php:1138
msgid "Meet Page Transitions"
msgstr "Poznaj przejścia stron"

#: includes/elements/column.php:451 includes/elements/container.php:862
#: includes/elements/section.php:719 includes/widgets/heading.php:328
msgid "Multiply"
msgstr "Mnożenie"

#: includes/elements/column.php:454 includes/elements/container.php:865
#: includes/elements/section.php:722 includes/widgets/heading.php:331
msgid "Darken"
msgstr "Ściemnienie"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:290
msgid "Please note! We couldn't deactivate all of your plugins on Safe Mode. Please %1$sread more%2$s about this issue"
msgstr "Uwaga! Nie mogliśmy wyłączyć wszystkich Twoich wtyczek w trybie awaryjnym. Przeczytaj %1$swięcej%2$s na temat tego problemu"

#: includes/template-library/sources/admin-menu-items/templates-categories-menu-item.php:23
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:3693
msgid "Categories"
msgstr "Kategorie"

#: includes/managers/elements.php:328
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:3173
msgid "Favorites"
msgstr "Ulubione"

#: core/common/modules/finder/categories/settings.php:74
#: core/experiments/manager.php:480
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:3702
msgid "Features"
msgstr "Funkcje"

#: modules/nested-accordion/widgets/nested-accordion.php:559
#: assets/js/app-packages.js:4235
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:64
msgid "Header"
msgstr "Nagłówek"

#: modules/pro-install/pro-install-menu-item.php:23
#: assets/js/admin-top-bar.js:317
msgid "Connect Account"
msgstr "Połącz się z kontem"

#: includes/settings/tools.php:112
msgid "New kit have been created successfully"
msgstr "Nowy zestaw został utworzony pomyślnie"

#: includes/settings/tools.php:107
msgid "An error occurred while trying to create a kit."
msgstr "Wystąpił błąd podczas próby utworzenia zestawu."

#: includes/settings/tools.php:101
msgid "There's already an active kit."
msgstr "Jest już aktywny zestaw."

#: includes/editor-templates/panel.php:296 assets/js/editor.js:13860
msgid "Color Sampler"
msgstr "Próbnik kolorów"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:15
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:19
msgid "Submissions"
msgstr "Zgłoszenia"

#: modules/library/documents/section.php:47
msgid "Sections"
msgstr "Sekcje"

#: includes/settings/tools.php:431 includes/settings/tools.php:434
#: assets/js/editor.js:28925
msgid "Recreate Kit"
msgstr "Odtwórz zestaw"

#: includes/settings/tools.php:435
msgid "It seems like your site doesn't have any active Kit. The active Kit includes all of your Site Settings. By recreating your Kit you will able to start edit your Site Settings again."
msgstr "Wygląda na to, że witryna nie ma żadnego aktywnego zestawu. Aktywny zestaw zawiera wszystkie ustawienia witryny. Poprzez odtworzenie zestawu będzie można rozpocząć edycję ustawień witryny ponownie."

#: app/modules/kit-library/data/repository.php:147
#: app/modules/kit-library/data/repository.php:167
msgid "Kit not found"
msgstr "Zestaw nie został znaleziony"

#: app/modules/kit-library/data/kits/controller.php:29
msgid "Kit not exists."
msgstr "Zestaw nie istnieje."

#: app/modules/kit-library/connect/kit-library.php:16
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:5019
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:5337
msgid "Kit Library"
msgstr "Biblioteka zestawów"

#: core/common/modules/connect/rest/rest-api.php:139
#: modules/components/components-rest-api.php:149
#: modules/global-classes/global-classes-rest-api.php:223
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:36
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:36
msgid "Something went wrong"
msgstr "Coś poszło nie tak"

#: includes/settings/tools.php:341 assets/js/app.js:12618
msgid "Important:"
msgstr "Istotne:"

#: app/modules/import-export-customization/module.php:149
#: app/modules/import-export/module.php:154 assets/js/app.js:7321
#: assets/js/app.js:7407 assets/js/app.js:7538 assets/js/app.js:7714
#: assets/js/app.js:13012 assets/js/app.js:16528
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:4324
msgid "Import"
msgstr "Importuj"

#: includes/settings/settings.php:318
msgid "API Key"
msgstr "Klucz API"

#: modules/compatibility-tag/compatibility-tag-report.php:169
msgid "Compatible"
msgstr "Kompatybilny"

#: modules/compatibility-tag/compatibility-tag-report.php:172
msgid "Compatibility unknown"
msgstr "Kompatybilność nieznana"

#: modules/compatibility-tag/compatibility-tag-report.php:171
msgid "Compatibility not specified"
msgstr "Kompatybilność nieokreślona"

#: includes/settings/settings.php:307
msgid "Google Maps Embed API"
msgstr "API osadzania map Google"

#: modules/compatibility-tag/compatibility-tag-report.php:170
msgid "Incompatible"
msgstr "Niekompatybilne"

#. translators: 1: Link open tag, 2: Link close tag
#: includes/settings/settings.php:311
msgid "Google Maps Embed API is a free service by Google that allows embedding Google Maps in your site. For more details, visit Google Maps' %1$sUsing API Keys%2$s page."
msgstr "Interfejs API osadzania Map Google to bezpłatna usługa Google, która umożliwia osadzanie Map Google w witrynie. Aby uzyskać więcej informacji, odwiedź stronę %1$sUżywanie kluczy API%2$s w Mapach Google."

#: includes/controls/groups/background.php:385
#: includes/widgets/common-base.php:1246
msgid "Y Position"
msgstr "Pozycja Y"

#: includes/widgets/common-base.php:1081 includes/widgets/common-base.php:1089
msgid "Mask"
msgstr "Maska"

#: includes/widgets/common-base.php:204
msgid "Sketch"
msgstr "Szkic"

#: includes/widgets/common-base.php:200
msgid "Flower"
msgstr "Kwiat"

#: includes/settings/settings.php:388
msgid "Blocking"
msgstr "Blokowanie"

#: includes/widgets/common-base.php:156
msgid "Triangle"
msgstr "Trójkąt"

#: includes/widgets/common-base.php:1138
msgid "Fit"
msgstr "Dopasuj"

#: includes/settings/settings.php:389
msgid "Swap"
msgstr "Zamień"

#: core/breakpoints/manager.php:339
msgid "Widescreen"
msgstr "Szeroki ekran"

#: core/admin/admin-notices.php:366
msgid "Managing a multi-user site?"
msgstr "Zarządzanie witryną z wieloma użytkownikami?"

#: core/admin/admin-notices.php:367
msgid "With Elementor Pro, you can control user access and make sure no one messes up your design."
msgstr "Z Elementor Pro, można kontrolować dostęp użytkowników i upewnić się, że nikt nie zepsuje twojego projektu."

#: includes/editor-templates/responsive-bar.php:62
msgid "Manage Breakpoints"
msgstr "Zarządzaj punktami zmiany"

#: core/base/db-upgrades-manager.php:114
msgid "Database update process is running in the background. Taking a while?"
msgstr "Proces aktualizacji bazy danych działa w tle. Trochę to trwa?"

#: includes/controls/groups/background.php:342
#: includes/widgets/common-base.php:1210
msgid "X Position"
msgstr "Położenie X"

#. translators: %1$s: Device name, %2$s: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:32
msgid "%1$s <br> Settings added for the %1$s device will apply to %2$spx screens and down"
msgstr "%1$s <br> Ustawienia dodane dla urządzenia %1$s będą obowiązywać dla ekranów %2$spx i niższych"

#: includes/settings/settings.php:391
msgid "Optional"
msgstr "Opcjonalnie"

#: includes/controls/groups/background.php:465
#: includes/widgets/common-base.php:1287
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:194
msgid "Repeat-x"
msgstr "Powtórz-x"

#: includes/controls/groups/background.php:466
#: includes/widgets/common-base.php:1288
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:195
msgid "Repeat-y"
msgstr "Powtórz-y"

#: includes/settings/settings.php:382
msgid "Google Fonts Load"
msgstr "Wczytaj kroje pisma Google"

#: includes/controls/groups/background.php:276
#: includes/controls/groups/background.php:328
#: includes/controls/groups/background.php:716
#: includes/widgets/common-base.php:1196 includes/widgets/image-box.php:434
#: includes/widgets/image.php:409
#: modules/link-in-bio/base/widget-link-in-bio-base.php:188
msgid "Bottom Right"
msgstr "Dół prawo"

#: includes/controls/groups/background.php:270
#: includes/controls/groups/background.php:322
#: includes/controls/groups/background.php:710
#: includes/widgets/common-base.php:1190 includes/widgets/image-box.php:428
#: includes/widgets/image.php:403
#: modules/link-in-bio/base/widget-link-in-bio-base.php:182
msgid "Center Right"
msgstr "Środek prawo"

#: includes/controls/groups/background.php:271
#: includes/controls/groups/background.php:323
#: includes/controls/groups/background.php:711
#: includes/widgets/common-base.php:1191 includes/widgets/image-box.php:429
#: includes/widgets/image.php:404
#: modules/link-in-bio/base/widget-link-in-bio-base.php:183
msgid "Top Center"
msgstr "Góra środek"

#: includes/controls/groups/background.php:268
#: includes/controls/groups/background.php:320
#: includes/controls/groups/background.php:708
#: includes/widgets/common-base.php:1188 includes/widgets/image-box.php:426
#: includes/widgets/image.php:401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:180
msgid "Center Center"
msgstr "Środek środek"

#: includes/controls/groups/background.php:274
#: includes/controls/groups/background.php:326
#: includes/controls/groups/background.php:714
#: includes/widgets/common-base.php:1194 includes/widgets/image-box.php:432
#: includes/widgets/image.php:407
#: modules/link-in-bio/base/widget-link-in-bio-base.php:186
msgid "Bottom Center"
msgstr "Dół środek"

#: includes/controls/groups/background.php:272
#: includes/controls/groups/background.php:324
#: includes/controls/groups/background.php:712
#: includes/widgets/common-base.php:1192 includes/widgets/image-box.php:430
#: includes/widgets/image.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:184
msgid "Top Left"
msgstr "Góra lewo"

#: includes/controls/groups/background.php:273
#: includes/controls/groups/background.php:325
#: includes/controls/groups/background.php:713
#: includes/widgets/common-base.php:1193 includes/widgets/image-box.php:431
#: includes/widgets/image.php:406
#: modules/link-in-bio/base/widget-link-in-bio-base.php:185
msgid "Top Right"
msgstr "Góra prawo"

#: includes/controls/groups/background.php:269
#: includes/controls/groups/background.php:321
#: includes/controls/groups/background.php:709
#: includes/widgets/common-base.php:1189 includes/widgets/image-box.php:427
#: includes/widgets/image.php:402
#: modules/link-in-bio/base/widget-link-in-bio-base.php:181
msgid "Center Left"
msgstr "Środek lewo"

#: includes/controls/groups/background.php:275
#: includes/controls/groups/background.php:327
#: includes/controls/groups/background.php:715
#: includes/widgets/common-base.php:1195 includes/widgets/image-box.php:433
#: includes/widgets/image.php:408
#: modules/link-in-bio/base/widget-link-in-bio-base.php:187
msgid "Bottom Left"
msgstr "Dół lewo"

#: includes/widgets/accordion.php:262 includes/widgets/toggle.php:265
#: modules/nested-accordion/widgets/nested-accordion.php:312
msgid "FAQ Schema"
msgstr "Schemat FAQ"

#: includes/settings/settings.php:393
msgid "Set the way Google Fonts are being loaded by selecting the font-display property (Recommended: Swap)."
msgstr "Ustaw sposób wczytywania krojów pisma Google, wybierając właściwość font-display (zalecane: Zamień)."

#: includes/editor-templates/responsive-bar.php:22
msgid "Desktop <br> Settings added for the base device will apply to all breakpoints unless edited"
msgstr "Ustawienia komputera stacjonarnego <br> dodane dla urządzenia bazowego będą miały zastosowanie do wszystkich punktów zmiany, chyba że zostaną edytowane"

#: includes/controls/groups/background.php:464
#: includes/widgets/common-base.php:1282 includes/widgets/common-base.php:1286
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:193
#: assets/js/packages/editor-controls/editor-controls.strings.js:197
msgid "Repeat"
msgstr "Powtórz"

#: includes/controls/groups/background.php:463
#: includes/widgets/common-base.php:1285
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:196
msgid "No-repeat"
msgstr "Bez powtórzenia"

#: includes/settings/settings.php:393
msgid "Font-display property defines how font files are loaded and displayed by the browser."
msgstr "Właściwość font-display definiuje sposób wczytywania i wyświetlania plików kroju pisma przez przeglądarkę."

#: includes/base/element-base.php:1076 includes/base/element-base.php:1099
#: includes/widgets/common-base.php:1153
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:109
#: assets/js/packages/editor-controls/editor-controls.strings.js:114
msgid "Scale"
msgstr "Skala"

#: includes/widgets/common-base.php:1289
#: modules/floating-buttons/base/widget-contact-button-base.php:2085
#: modules/floating-buttons/base/widget-contact-button-base.php:2176
#: modules/floating-buttons/base/widget-contact-button-base.php:2869
#: modules/floating-buttons/base/widget-floating-bars-base.php:844
#: modules/link-in-bio/base/widget-link-in-bio-base.php:118
msgid "Round"
msgstr "Okrągły"

#: includes/widgets/common-base.php:212
msgid "Blob"
msgstr "Kropelka"

#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:35
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:61
#: modules/shapes/widgets/text-path.php:136
msgid "SVG"
msgstr "SVG"

#: modules/shapes/module.php:43
msgid "Wave"
msgstr "Fala"

#: modules/shapes/module.php:44
msgid "Arc"
msgstr "Łuk"

#: app/modules/import-export-customization/module.php:202
#: app/modules/import-export/module.php:207 core/admin/admin-notices.php:284
#: core/admin/admin-notices.php:412 core/admin/admin-notices.php:503
#: core/admin/admin-notices.php:551 core/admin/admin-notices.php:599
#: core/experiments/manager.php:317 core/experiments/manager.php:333
#: core/experiments/manager.php:363 core/experiments/manager.php:534
#: includes/controls/url.php:78 includes/elements/section.php:473
#: includes/settings/settings-page.php:404 includes/widgets/video.php:606
#: modules/ai/feature-intro/product-image-unification-intro.php:40
#: modules/checklist/steps/step-base.php:102
#: modules/nested-elements/module.php:21
#: modules/shapes/widgets/text-path.php:150 assets/js/app.js:4850
#: assets/js/app.js:7666 assets/js/app.js:9913 assets/js/app.js:12608
#: assets/js/editor-v4-opt-in-alphachip.js:187
#: assets/js/editor-v4-opt-in.js:497 assets/js/editor-v4-welcome-opt-in.js:84
#: assets/js/editor.js:28029
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:4366
msgid "Learn more"
msgstr "Dowiedz się więcej"

#. translators: 1: Term taxonomy, 2: Term name.
#. translators: 1: Post type singular label, 2: Post title.
#. translators: 1: Taxonomy name, 2: Term name.
#: core/utils/import-export/wp-import.php:505
#: core/utils/import-export/wp-import.php:696
#: core/utils/import-export/wp-import.php:746
msgid "Failed to import %1$s %2$s"
msgstr "Nie udało się zaimportować %1$s %2$s"

#: includes/controls/groups/typography.php:245
#: modules/shapes/widgets/text-path.php:347
msgid "Word Spacing"
msgstr "Odstępy między wyrazami"

#: modules/shapes/widgets/text-path.php:217
msgid "Show Path"
msgstr "Pokaż ścieżkę"

#: modules/shapes/widgets/text-path.php:205
msgid "LTR"
msgstr "LTR"

#: modules/shapes/widgets/text-path.php:51
#: modules/shapes/widgets/text-path.php:103
#: modules/shapes/widgets/text-path.php:243
msgid "Text Path"
msgstr "Ścieżka tekstowa"

#: modules/shapes/module.php:48
msgid "Spiral"
msgstr "Spirala"

#: modules/shapes/module.php:47
msgid "Oval"
msgstr "Owalny"

#: modules/shapes/widgets/text-path.php:114
msgid "Add Your Curvy Text Here"
msgstr "Dodaj tutaj swój zakrzywiony tekst"

#: core/kits/documents/tabs/settings-layout.php:217
msgid "Mobile and Tablet options cannot be deleted."
msgstr "Nie można usunąć opcji urządzeń przenośnych i tabletów."

#: modules/shapes/widgets/text-path.php:199
msgid "Text Direction"
msgstr "Kierunek tekstu"

#: core/utils/import-export/wp-import.php:998
msgid "Fetching attachments is not enabled"
msgstr "Pobieranie załączników jest wyłączone"

#: core/utils/import-export/wp-import.php:1101
msgid "Zero size file downloaded"
msgstr "Pobrano pusty plik"

#: core/utils/import-export/wp-import.php:1093
msgid "Remote server did not respond"
msgstr "Zdalny serwer nie odpowiada"

#. translators: %s: Author display name.
#: core/utils/import-export/wp-import.php:405
msgid "Failed to create new user for %s. Their posts will be attributed to the current user."
msgstr "Nie udało się stworzyć nowego użytkownika dla %s. Jego wpisy zostaną przypisane do obecnego użytkownika."

#. translators: %s: Max file size.
#: core/utils/import-export/wp-import.php:1115
msgid "Remote file is too large, limit is %s"
msgstr "Zdalny plik jest zbyt duży, limit wynosi %s"

#: core/utils/import-export/wp-import.php:257
msgid "The file does not exist, please try again."
msgstr "Plik nie istnieje, spróbuj ponownie."

#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:57
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:65
msgid "There was an error when reading this WXR file"
msgstr "Wystąpił błąd podczas odczytywania pliku WXR"

#: core/utils/import-export/parsers/wxr-parser-regex.php:146
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:70
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:76
#: core/utils/import-export/parsers/wxr-parser-xml.php:190
msgid "This does not appear to be a WXR file, missing/invalid WXR version number"
msgstr "To nie wygląda na plik WXR, lub brakuje lub jest nieprawidłowy numer wersji"

#. translators: %s: Post author.
#: core/utils/import-export/wp-import.php:340
msgid "Failed to import author %s. Their posts will be attributed to the current user."
msgstr "Nie udało się zaimportować autora %s. Wpisy zostaną przypisane do obecnego użytkownika."

#: core/utils/import-export/wp-import.php:1059
msgid "Could not create temporary file."
msgstr "Nie można było utworzyć pliku tymczasowego."

#: core/utils/import-export/wp-import.php:1147
msgid "Sorry, this file type is not permitted for security reasons."
msgstr "Ze względów bezpieczeństwa taki typ pliku nie jest dozwolony."

#: modules/shapes/widgets/text-path.php:485
msgid "Path"
msgstr "Ścieżka"

#. translators: 1: WordPress error message, 2: WordPress error code.
#: core/utils/import-export/wp-import.php:1075
msgid "Request failed due to an error: %1$s (%2$s)"
msgstr "Żądanie nie powiodło się z powodu błędu: %1$s (%2$s)"

#. translators: 1: HTTP error message, 2: HTTP error code.
#: core/utils/import-export/wp-import.php:1084
msgid "Remote server returned the following unexpected result: %1$s (%2$s)"
msgstr "Serwer zdalny zwrócił następujący nieoczekiwany wynik: %1$s (%2$s)"

#: core/utils/import-export/wp-import.php:1107
msgid "Downloaded file has incorrect size"
msgstr "Pobrany plik ma nieprawidłowy rozmiar"

#: core/utils/import-export/wp-import.php:1163
msgid "The uploaded file could not be moved"
msgstr "Przesłany plik nie może być przeniesiony"

#: modules/shapes/widgets/text-path.php:382
msgid "Starting Point"
msgstr "Punkt początkowy"

#: core/utils/import-export/wp-import.php:898
msgid "Menu item skipped due to missing menu slug"
msgstr "Element menu został pominięty ze względu na brak uproszczonej nazwy"

#: core/kits/documents/tabs/settings-layout.php:215
msgid "Active Breakpoints"
msgstr "Aktywne punkty zmiany"

#. translators: %s: Menu slug.
#: core/utils/import-export/wp-import.php:911
msgid "Menu item skipped due to invalid menu slug: %s"
msgstr "Pominięto pozycja menu z powodu nieprawidłowej uproszczonej nazwy: %s"

#: modules/shapes/widgets/text-path.php:520
#: modules/shapes/widgets/text-path.php:591
msgid "Stroke"
msgstr "Obrys"

#: modules/shapes/widgets/text-path.php:204
msgid "RTL"
msgstr "RTL"

#: core/utils/import-export/wp-import.php:1015
msgid "Invalid file type"
msgstr "Nieprawidłowy rodzaj pliku"

#. translators: 1: Post title, 2: Post type.
#: core/utils/import-export/wp-import.php:604
msgid "Failed to import %1$s: Invalid post type %2$s"
msgstr "Nie udało się zaimportować %1$s: nieprawidłowy rodzaj wpisu %2$s"

#: modules/shapes/widgets/text-path.php:126
msgid "Path Type"
msgstr "Rodzaj ścieżki"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:86
msgid "Privacy mode"
msgstr "Tryb prywatności"

#: core/experiments/manager.php:405 modules/atomic-widgets/module.php:363
#: assets/js/editor-v4-opt-in.js:345 assets/js/editor-v4-opt-in.js:492
msgid "Alpha"
msgstr "Alfa"

#: core/experiments/manager.php:406 assets/js/ai-admin.js:658
#: assets/js/ai-admin.js:7771 assets/js/ai-gutenberg.js:796
#: assets/js/ai-gutenberg.js:7989 assets/js/ai-layout.js:490
#: assets/js/ai-layout.js:3259 assets/js/ai-media-library.js:658
#: assets/js/ai-media-library.js:7771 assets/js/ai-unify-product-images.js:658
#: assets/js/ai-unify-product-images.js:7771 assets/js/ai.js:1446
#: assets/js/ai.js:9323
msgid "Beta"
msgstr "Beta"

#: core/common/modules/finder/categories/settings.php:69
msgid "Experiments"
msgstr "Eksperymenty"

#: modules/pro-install/pro-install-menu-item.php:182
msgid "Install & Activate"
msgstr "Zainstaluj i włącz"

#: core/experiments/manager.php:407
msgid "Stable"
msgstr "Stabilna"

#. translators: %s Release status.
#: core/experiments/manager.php:565
msgid "Status: %s"
msgstr "Status: %s"

#: core/experiments/manager.php:465
msgid "No available experiments"
msgstr "Brak dostępnych eksperymentów"

#: core/experiments/manager.php:468
msgid "The current version of Elementor doesn't have any experimental features . if you're feeling curious make sure to come back in future versions."
msgstr "Obecna wersja Elementora nie posiada żadnych eksperymentalnych funkcji. Jeśli cię to ciekawi, upewnij się, że wrócisz do niej w kolejnych wersjach."

#: core/experiments/manager.php:404
msgid "Development"
msgstr "Rozwój"

#: core/experiments/manager.php:533
msgid "To use an experiment or feature on your site, simply click on the dropdown next to it and switch to Active. You can always deactivate them at any time."
msgstr "Aby użyć eksperymentu lub funkcji na swojej stronie, po prostu kliknij na rozwijaną listę obok niej i przełącz na Włączone. Zawsze możesz je wyłączyć w dowolnym momencie."

#: core/experiments/manager.php:679
msgid "Inactive by default"
msgstr "Domyślnie wyłączone"

#: core/experiments/manager.php:678
msgid "Active by default"
msgstr "Domyślnie włączono"

#: modules/landing-pages/documents/landing-page.php:46
#: modules/landing-pages/module.php:229 modules/landing-pages/module.php:295
msgid "Landing Page"
msgstr "Strona docelowa"

#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:22
#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:26
#: modules/landing-pages/documents/landing-page.php:54
#: modules/landing-pages/module.php:46 modules/landing-pages/module.php:163
#: modules/landing-pages/module.php:294 modules/landing-pages/module.php:306
#: assets/js/app.js:15445 assets/js/editor.js:52698
msgid "Landing Pages"
msgstr "Strony docelowe"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:42
#: assets/js/element-manager-admin.js:671
#: assets/js/element-manager-admin.js:732
msgid "Plugin"
msgstr "Wtyczka"

#: modules/landing-pages/module.php:300
msgid "All Landing Pages"
msgstr "Wszystkie strony docelowe"

#: modules/landing-pages/module.php:229
msgid "Build Effective Landing Pages for your business' marketing campaigns."
msgstr "Buduj efektywne strony docelowe dla kampanii marketingowych twojej firmy."

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:28
msgid "Compatibility Alert"
msgstr "Alert zgodności"

#: modules/landing-pages/module.php:298
msgid "Edit Landing Page"
msgstr "Edytuj stronę docelową"

#: modules/landing-pages/module.php:299
msgid "New Landing Page"
msgstr "Nowa strona docelowa"

#: modules/landing-pages/module.php:303
msgid "No landing pages found"
msgstr "Nie znaleziono stron docelowych"

#: modules/landing-pages/module.php:304
msgid "No landing pages found in trash"
msgstr "Nie znaleziono stron docelowych w koszu"

#: modules/landing-pages/module.php:302
msgid "Search Landing Pages"
msgstr "Szukanie stron docelowych"

#. translators: 1: Plugin name, 2: Plugin version.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:33
msgid "Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s."
msgstr "Niektóre z wtyczek, których używasz, nie zostały przetestowane z najnowszą wersją %1$s (%2$s). Aby uniknąć problemów, upewnij się, że wszystkie są aktualne i kompatybilne przed aktualizacją %1$s."

#. translators: %s: Elementor plugin name.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:45
msgid "Tested up to %s version"
msgstr "Przetestowane do wersji %s"

#: modules/landing-pages/module.php:301
msgid "View Landing Page"
msgstr "Zobacz stronę docelową"

#: modules/compatibility-tag/compatibility-tag-report.php:123
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:57
#: modules/element-manager/ajax.php:139
msgid "Unknown"
msgstr "Nieznane"

#: modules/landing-pages/module.php:297
msgid "Add New Landing Page"
msgstr "Utwórz stronę docelową"

#: includes/elements/section.php:301
msgid "Custom Columns Gap"
msgstr "Własny odstępy kolumn"

#: modules/landing-pages/module.php:47
msgid "Adds a new Elementor content type that allows creating beautiful landing pages instantly in a streamlined workflow."
msgstr "Dodaje nowy typ treści Elementor, który umożliwia błyskawiczne tworzenie pięknych stron docelowych w ramach usprawnionego przepływu pracy."

#: core/kits/views/trash-kit-confirmation.php:33
msgid "Keep my settings"
msgstr "Zachowaj moje ustawienia"

#: core/kits/views/trash-kit-confirmation.php:21
msgid "By removing this template you will delete your entire Site Settings. If this template is deleted, all associated settings: Global Colors & Fonts, Theme Style, Layout, Background, and Lightbox settings will be removed from your existing site. This action can not be undone."
msgstr "Usunięcie tego szablonu spowoduje usunięcie wszystkich ustawień witryny. Usunięcie tego szablonu spowoduje usunięcie wszystkich powiązanych z nim ustawień: globalnych kolorów i krojów pisma, stylu motywu, układu, tła i ustawień Lightbox z istniejącej witryny. Tej czynności nie można cofnąć."

#: core/kits/views/trash-kit-confirmation.php:17
msgid "Are you sure you want to delete your Site Settings?"
msgstr "Czy na pewno chcesz usunąć ustawienia witryny?"

#: core/editor/data/globals/endpoints/base.php:34
msgid "The Global value you are trying to use is not available."
msgstr "Wartość globalna, której próbujesz użyć, jest niedostępna."

#. Description of the plugin
#: elementor.php
msgid "The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!"
msgstr "Kreator witryn Elementor ma wszystko: narzędzie do tworzenia stron metodą \"przeciągnij i upuść\", doskonały projekt, responsywny edytor mobilny i nie tylko. Zacznij teraz!"

#: includes/controls/media.php:195
msgid "Choose SVG"
msgstr "Wybierz SVG"

#: core/kits/documents/tabs/global-colors.php:24
#: core/kits/documents/tabs/global-colors.php:43 assets/js/app.js:10375
#: assets/js/app.js:15453 assets/js/editor.js:47684
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:173
msgid "Global Colors"
msgstr "Kolory globalne"

#: core/kits/documents/tabs/settings-layout.php:350
#: modules/nested-tabs/widgets/nested-tabs.php:437
msgid "Breakpoint"
msgstr "Punkt zmiany"

#: includes/controls/groups/flex-container.php:24
#: includes/controls/groups/grid-container.php:26
#: includes/widgets/icon-list.php:180
#: modules/nested-accordion/widgets/nested-accordion.php:151
msgid "Items"
msgstr "Elementy"

#: includes/widgets/common-base.php:1139 includes/widgets/image-box.php:406
#: includes/widgets/image.php:383
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:133
msgid "Fill"
msgstr "Wypełnienie"

#: core/admin/admin.php:867
msgid "Heads up, Please backup before upgrade!"
msgstr "Uwaga, przed aktualizacją wykonaj kopię zapasową!"

#: core/settings/editor-preferences/model.php:38
#: includes/editor-templates/hotkeys.php:146 assets/js/editor.js:38635
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:6
msgid "User Preferences"
msgstr "Preferencje użytkownika"

#: core/kits/manager.php:436 includes/editor-templates/hotkeys.php:112
#: assets/js/app.js:15451 assets/js/app.js:15948 assets/js/editor.js:47633
#: assets/js/editor.js:47637 assets/js/editor.js:47647
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:9
msgid "Site Settings"
msgstr "Ustawienia witryny"

#: core/kits/documents/tabs/settings-layout.php:183
#: modules/page-templates/module.php:159 assets/js/app.js:9476
#: assets/js/app.js:9482
msgid "Theme"
msgstr "Motyw"

#: core/kits/documents/tabs/settings-site-identity.php:84
msgid "Choose description"
msgstr "Wybierz opis"

#: core/kits/documents/tabs/settings-site-identity.php:73
msgid "Choose name"
msgstr "Wybierz nazwę"

#: core/kits/documents/tabs/settings-site-identity.php:20
msgid "Site Identity"
msgstr "Tożsamość witryny"

#: core/kits/documents/tabs/settings-site-identity.php:71
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:826
msgid "Site Name"
msgstr "Nazwa witryny"

#: core/kits/documents/tabs/settings-site-identity.php:82
msgid "Site Description"
msgstr "Opis witryny"

#: core/kits/documents/tabs/settings-site-identity.php:93
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:830
msgid "Site Logo"
msgstr "Logo witryny"

#: core/kits/documents/tabs/settings-site-identity.php:118
msgid "Site Favicon"
msgstr "Favicona witryny"

#: core/kits/documents/tabs/settings-layout.php:47 assets/js/app.js:10375
#: assets/js/app.js:15453
msgid "Layout Settings"
msgstr "Ustawienia układu"

#: core/kits/documents/tabs/settings-background.php:65
msgid "Mobile Browser Background"
msgstr "Tło przeglądarki mobilnej"

#: core/kits/documents/tabs/settings-layout.php:180
msgid "Default Page Layout"
msgstr "Domyślny układ strony"

#: includes/widgets/image-box.php:402 includes/widgets/image.php:376
msgid "Object Fit"
msgstr "Dopasowanie obiektu"

#: modules/page-templates/module.php:365
msgid "Changes will be reflected in the preview only after the page reloads."
msgstr "Zmiany zostaną odzwierciedlone w podglądzie dopiero po przeładowaniu strony."

#: core/settings/editor-preferences/model.php:174 assets/js/editor.js:48051
msgid "Design System"
msgstr "System projektowania"

#: app/modules/kit-library/module.php:134
#: core/frontend/render-mode-manager.php:152
#: modules/compatibility-tag/compatibility-tag-report.php:173
msgid "Error"
msgstr "Błąd"

#: includes/frontend.php:1380
msgid "Download"
msgstr "Pobierz"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/admin/admin.php:873
msgid "The latest update includes some substantial changes across different areas of the plugin. We highly recommend you %1$sbackup your site before upgrading%2$s, and make sure you first update in a staging environment"
msgstr "Najnowsza aktualizacja zawiera kilka istotnych zmian w różnych obszarach wtyczki. Zdecydowanie zalecamy wykonanie %1$skopii zapasowej witryny przed aktualizacją%2$s i proszę się upewnić, że wykonano pierwszą aktualizację w środowisku testowym"

#: modules/nested-tabs/widgets/nested-tabs.php:376 assets/js/editor.js:48043
msgid "Additional Settings"
msgstr "Dodatkowe ustawienia"

#. translators: 1: Width number pixel, 2: Height number pixel.
#: core/kits/documents/tabs/settings-site-identity.php:102
msgid "Suggested image dimensions: %1$s × %2$s pixels."
msgstr "Sugerowane wymiary obrazka: %1$s × %2$s pikseli."

#: includes/widgets/social-icons.php:485
msgid "Rows Gap"
msgstr "Odstęp wierszy"

#: core/kits/documents/tabs/settings-layout.php:194
msgid "Breakpoints"
msgstr "Punkty zmian"

#: core/kits/documents/tabs/global-typography.php:197
msgid "Fallback Font Family"
msgstr "Rodzina kroju pisma zapasowego"

#: core/kits/documents/tabs/settings-background.php:67
msgid "The `theme-color` meta tag will only be available in supported browsers and devices."
msgstr "Meta znacznik `theme-color` będzie dostępny wyłącznie w obsługiwanych przeglądarkach i urządzeniach."

#: core/kits/documents/tabs/settings-site-identity.php:125
msgid "Suggested favicon dimensions: 512 × 512 pixels."
msgstr "Sugerowane wymiary favicony: 512 × 512 pikseli."

#: includes/widgets/icon-list.php:213
msgid "Apply Link On"
msgstr "Zastosuj odnośnik na"

#: core/common/modules/connect/apps/base-app.php:109
msgid "Reset Data"
msgstr "Reset danych"

#: includes/settings/settings.php:353 assets/js/admin.js:294
#: assets/js/admin.js:304 assets/js/ai-admin.js:64 assets/js/ai-admin.js:74
#: assets/js/ai-gutenberg.js:64 assets/js/ai-gutenberg.js:74
#: assets/js/ai-media-library.js:64 assets/js/ai-media-library.js:74
#: assets/js/ai-unify-product-images.js:64
#: assets/js/ai-unify-product-images.js:74 assets/js/ai.js:64
#: assets/js/ai.js:74 assets/js/common.js:64 assets/js/common.js:74
#: assets/js/editor.js:40443 assets/js/editor.js:40453
msgid "Enable Unfiltered File Uploads"
msgstr "Włącz niefiltrowane przesyłanie plików"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:74
msgid "Watch the Full Guide"
msgstr "Obejrzyj pełen poradnik"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:62
msgid "Get introduced to Elementor by watching our \"Getting Started\" video series. It will guide you through the steps needed to create your website. Then click to create your first page."
msgstr "Zapoznaj się z Elementorem oglądając naszą serię wideo \"Rozpoczęcie pracy\". Przeprowadzi cię ona przez wszystkie kroki potrzebne do stworzenia strony. Następnie kliknij, aby utworzyć swoją pierwszą stronę."

#: includes/controls/media.php:270
msgid "Click the media icon to upload file"
msgstr "Kliknij ikonkę multimediów, aby przesłać plik"

#: modules/safe-mode/module.php:367
msgid "If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode."
msgstr "Jeśli masz problem z ładowaniem, skontaktuj się z administratorem witryny, aby rozwiązać problem w trybie awaryjnym."

#: includes/frontend.php:1377
msgid "Share on Facebook"
msgstr "Udostępnij na Facebooku"

#: core/isolation/elementor-adapter.php:28 core/kits/manager.php:156
#: core/kits/manager.php:174
msgid "Default Kit"
msgstr "Zestaw domyślny"

#: core/kits/documents/tabs/settings-lightbox.php:102
#: core/kits/documents/tabs/settings-lightbox.php:119
msgid "Alt"
msgstr "Alt"

#: core/kits/documents/tabs/settings-lightbox.php:86 includes/frontend.php:1384
msgid "Share"
msgstr "Udostępnij"

#: includes/managers/icons.php:490
msgid "The upgrade process includes a database update"
msgstr "Proces aktualizacji obejmuje aktualizację bazy danych"

#: includes/frontend.php:1379
msgid "Pin it"
msgstr "Przypnij"

#: includes/managers/controls.php:1204
msgid "Attributes"
msgstr "Atrybuty"

#: core/kits/documents/tabs/theme-style-form-fields.php:99
msgid "Field"
msgstr "Pole"

#: core/kits/documents/tabs/theme-style-form-fields.php:21
#: core/kits/documents/tabs/theme-style-form-fields.php:60
msgid "Form Fields"
msgstr "Pola formularza"

#: core/kits/documents/tabs/theme-style-buttons.php:23
#: core/kits/documents/tabs/theme-style-buttons.php:63
#: modules/floating-buttons/base/widget-contact-button-base.php:221
msgid "Buttons"
msgstr "Przyciski"

#: includes/managers/controls.php:1214
msgid "Meet Our Attributes"
msgstr "Poznaj nasze atrybuty"

#: core/kits/documents/tabs/theme-style-form-fields.php:71
msgid "Label"
msgstr "Etykieta"

#: core/kits/documents/kit.php:154
msgid "Draft"
msgstr "Szkic"

#: core/kits/documents/kit.php:43
msgid "Kit"
msgstr "Zestaw"

#: includes/controls/url.php:119
msgid "Custom Attributes"
msgstr "Własne atrybuty"

#: includes/managers/controls.php:1216
msgid "Attributes lets you add custom HTML attributes to any element."
msgstr "Atrybuty pozwalają na dodanie własnych atrybutów HTML do dowolnego elementu."

#: core/base/db-upgrades-manager.php:118
msgid "Click here to run it now"
msgstr "Kliknij tutaj, aby uruchomić go teraz"

#: includes/controls/url.php:77
msgid "Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma."
msgstr "Ustawienie własnych atrybutów dla elementu odnośnika. Oddzielenie kluczy atrybutów od wartości za pomocą znaku | (pipe). Oddziel pary klucz-wartość przecinkiem."

#: includes/editor-templates/panel.php:319
msgid "You’re missing out!"
msgstr "Tracisz na tym!"

#: core/kits/documents/tabs/settings-lightbox.php:66 includes/frontend.php:1382
msgid "Fullscreen"
msgstr "Tryb pełnoekranowy"

#: core/kits/documents/tabs/theme-style-form-fields.php:128
msgid "Focus"
msgstr "Skupienie"

#. translators: %s: Widget title.
#: core/editor/promotion.php:56
msgid "Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better."
msgstr "Użyj widżetu %s i dziesiątek innych profesjonalnych funkcji, aby rozszerzyć swój zestaw narzędzi i tworzyć witryny szybciej i lepiej."

#: core/kits/documents/tabs/settings-lightbox.php:187
msgid "Navigation Icons Size"
msgstr "Rozmiar ikonek nawigacyjnych"

#: core/kits/documents/tabs/theme-style-typography.php:75
#: includes/widgets/text-editor.php:306
msgid "Paragraph Spacing"
msgstr "Odstępy akapitu"

#: includes/managers/icons.php:491
msgid "We highly recommend backing up your database before performing this upgrade."
msgstr "Zdecydowanie zalecamy wykonanie kopii zapasowej bazy danych przed wykonaniem tej aktualizacji."

#: core/kits/documents/tabs/settings-lightbox.php:174
msgid "Toolbar Icons Size"
msgstr "Rozmiar ikonek paska narzędzi"

#. translators: %s: Widget title.
#: core/editor/promotion.php:54
msgid "%s Widget"
msgstr "Widżet %s"

#: core/kits/documents/tabs/theme-style-typography.php:48
msgid "Body"
msgstr "Ciało"

#: includes/editor-templates/panel.php:320
msgid "Get more dynamic capabilities by incorporating dozens of Elementor's native dynamic tags."
msgstr "Uzyskaj więcej dynamicznych możliwości, włączając dziesiątki natywnych, dynamicznych znaczników Elementora."

#: includes/editor-templates/panel.php:290
msgid "Dynamic Tags"
msgstr "Znaczniki dynamiczne"

#: core/experiments/manager.php:104 includes/editor-templates/global.php:27
#: includes/editor-templates/templates.php:224 assets/js/ai-admin.js:9560
#: assets/js/ai-gutenberg.js:9778 assets/js/ai-media-library.js:9560
#: assets/js/ai-unify-product-images.js:9560 assets/js/ai.js:11112
#: assets/js/app.js:7449 assets/js/app.js:12090 assets/js/editor.js:47939
msgid "Back"
msgstr "Wstecz"

#: includes/editor-templates/panel.php:316
msgid "Elementor Dynamic Content"
msgstr "Treść dynamiczna Elementor"

#: includes/frontend.php:1381
msgid "Download image"
msgstr "Pobierz obrazek"

#: includes/frontend.php:1378
msgid "Share on Twitter"
msgstr "Udostępnij na X"

#: core/common/modules/connect/apps/base-app.php:161
msgid "Already connected."
msgstr "Już połączony."

#: includes/controls/groups/background.php:683
msgid "Background Size"
msgstr "Rozmiar tła"

#: core/kits/documents/tabs/settings-background.php:81
#: includes/controls/groups/background.php:486
#: includes/controls/groups/background.php:691
#: includes/widgets/image-box.php:408 includes/widgets/image.php:385
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:190
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:135
msgid "Contain"
msgstr "Zawiera"

#: includes/widgets/image-carousel.php:473
msgid "Pause on Interaction"
msgstr "Pauza przy interakcji"

#: includes/controls/groups/background.php:485
#: includes/controls/groups/background.php:690
#: includes/widgets/image-box.php:407 includes/widgets/image.php:384
#: modules/link-in-bio/base/widget-link-in-bio-base.php:923
#: modules/link-in-bio/base/widget-link-in-bio-base.php:978
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:189
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:134
msgid "Cover"
msgstr "Okładka"

#. translators: %s: Remote user.
#: core/common/modules/connect/apps/base-app.php:84
msgid "Connected as %s"
msgstr "Połączono jako %s"

#: core/settings/editor-preferences/model.php:78
msgid "Auto detect"
msgstr "Automatyczne wykrywanie"

#: core/settings/editor-preferences/model.php:152 assets/js/ai-admin.js:15907
#: assets/js/ai-gutenberg.js:16125 assets/js/ai-layout.js:5206
#: assets/js/ai-media-library.js:15907
#: assets/js/ai-unify-product-images.js:15907 assets/js/ai.js:7842
#: assets/js/ai.js:17459 assets/js/editor.js:8001
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:286
msgid "Get Started"
msgstr "Rozpocznij"

#: includes/controls/groups/background.php:702
msgid "Background Position"
msgstr "Pozycja tła"

#: core/kits/documents/tabs/settings-background.php:80
#: includes/controls/groups/background.php:484
#: includes/controls/groups/background.php:689
#: includes/elements/container.php:563 includes/widgets/social-icons.php:299
#: includes/widgets/video.php:600
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:53
#: assets/js/packages/editor-controls/editor-controls.strings.js:188
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:131
msgid "Auto"
msgstr "Automatycznie"

#. translators: %s: Video provider
#: includes/embed.php:185
msgid "%s Video Player"
msgstr "%s Odtwarzacz filmu"

#: core/settings/editor-preferences/model.php:51
msgid "Preferences"
msgstr "Preferencje"

#: core/common/modules/connect/apps/library.php:29
#: core/common/modules/connect/apps/library.php:56
msgid "Connecting to the Library failed. Please try reloading the page and try again"
msgstr "Połączenie z biblioteką nie powiodło się. Spróbuj ponownie wczytać stronę i spróbować jeszcze raz"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:19
msgid "Custom Icons"
msgstr "Własne ikonki"

#: includes/controls/groups/background.php:645
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:96
msgid "Duration"
msgstr "Czas trwania"

#: includes/controls/groups/background.php:655
msgid "Transition"
msgstr "Przejście"

#: core/logger/log-reporter.php:44
msgid "Clear Log"
msgstr "Wyczyść dziennik"

#: includes/frontend.php:1387 assets/js/app.js:11910 assets/js/app.js:13858
#: assets/js/app.js:14803
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:1500
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:1551
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:1830
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:2106
msgid "Next"
msgstr "Następne"

#: includes/frontend.php:1386 assets/js/app.js:13000 assets/js/app.js:13845
#: assets/js/app.js:14796
msgid "Previous"
msgstr "Poprzednie"

#: includes/widgets/divider.php:496
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:21
msgid "Add Element"
msgstr "Dodaj element"

#: includes/widgets/divider.php:340 modules/shapes/module.php:46
msgid "Line"
msgstr "Linia"

#: includes/widgets/divider.php:310
msgctxt "Shapes"
msgid "X"
msgstr "X"

#: includes/widgets/divider.php:292
msgctxt "Shapes"
msgid "Trees"
msgstr "Drzewa"

#: includes/widgets/divider.php:283
msgctxt "Shapes"
msgid "Squares"
msgstr "Kwadraty"

#: includes/widgets/divider.php:274
msgctxt "Shapes"
msgid "Stripes"
msgstr "Paski"

#: includes/widgets/divider.php:256
msgctxt "Shapes"
msgid "Half Rounds"
msgstr "Półokręgi"

#: includes/widgets/divider.php:247
msgctxt "Shapes"
msgid "Fir Tree"
msgstr "Jodła"

#: includes/widgets/divider.php:238
msgctxt "Shapes"
msgid "Dots"
msgstr "Kropki"

#: includes/widgets/divider.php:229
msgctxt "Shapes"
msgid "Rectangles"
msgstr "Prostokąty"

#: includes/widgets/divider.php:221
msgctxt "Shapes"
msgid "Parallelogram"
msgstr "Równoległobok"

#: includes/widgets/divider.php:205
msgctxt "Shapes"
msgid "Pluses"
msgstr "Plusy"

#: includes/widgets/divider.php:197
msgctxt "Shapes"
msgid "Arrows"
msgstr "Strzałki"

#: includes/widgets/divider.php:173
msgctxt "Shapes"
msgid "Squared"
msgstr "Kwadratowy"

#: includes/widgets/divider.php:164
msgctxt "Shapes"
msgid "Slashes"
msgstr "Ukośniki"

#: includes/widgets/divider.php:148
msgctxt "Shapes"
msgid "Curved"
msgstr "Zakrzywiony"

#: includes/widgets/divider.php:140
msgctxt "Shapes"
msgid "Curly"
msgstr "Kręcony"

#: includes/controls/groups/background.php:583 includes/widgets/video.php:376
msgid "Play On Mobile"
msgstr "Odtwarzaj na urządzeniach mobilnych"

#: includes/settings/tools.php:386
msgid "Reinstall"
msgstr "Zainstaluj ponownie"

#: includes/widgets/divider.php:301
msgctxt "Shapes"
msgid "Tribal"
msgstr "Plemienne"

#: includes/controls/groups/background.php:754
msgid "Out"
msgstr "Na zewnątrz"

#: core/document-types/post.php:51
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:61
msgid "Post"
msgstr "Wpis"

#: includes/widgets/image-gallery.php:44 includes/widgets/image-gallery.php:130
msgid "Basic Gallery"
msgstr "Galeria podstawowa"

#: includes/widgets/divider.php:265
msgctxt "Shapes"
msgid "Leaves"
msgstr "Liście"

#: includes/widgets/divider.php:213
msgctxt "Shapes"
msgid "Rhombus"
msgstr "Romb"

#: includes/widgets/divider.php:181
msgctxt "Shapes"
msgid "Wavy"
msgstr "Falisty"

#: includes/widgets/divider.php:156
msgctxt "Shapes"
msgid "Multiple"
msgstr "Wielokrotny"

#: includes/controls/groups/background.php:738
msgid "Ken Burns Effect"
msgstr "Efekt Kena Burnsa"

#: includes/controls/groups/background.php:753
msgid "In"
msgstr "Wewnątrz"

#: includes/controls/groups/background.php:608
msgid "This cover image will replace the background video in case that the video could not be loaded."
msgstr "Ten obrazek okładki zastąpi tło filmu w przypadku, gdy nie uda się wczytać filmu."

#: includes/controls/groups/background.php:528
msgid "YouTube/Vimeo link, or link to video file (mp4 is recommended)."
msgstr "Odnośnik do YouTube/Vimeo lub odnośnik do pliku filmowego (zalecany jest plik mp4)."

#: includes/widgets/divider.php:667
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:119
msgid "Amount"
msgstr "Ilość"

#. translators: %s: Path to .htaccess file.
#: core/debug/classes/htaccess.php:31
msgid "File Path: %s"
msgstr "Ścieżka pliku: %s"

#: core/kits/views/panel.php:12 includes/editor-templates/panel.php:27
#: includes/editor-templates/panel.php:177
msgid "Need Help"
msgstr "Potrzebuję pomocy"

#: includes/controls/icons.php:91 includes/controls/icons.php:112
#: includes/controls/icons.php:198
msgid "Upload SVG"
msgstr "Prześlij SVG"

#: includes/controls/media.php:192
msgid "Choose Video"
msgstr "Wybierz film"

#: includes/admin-templates/beta-tester.php:32
msgid "Your Email"
msgstr "Twój adres e-mail"

#: includes/template-library/sources/local.php:618
msgid "Template not exist."
msgstr "Szablon nie istnieje."

#: includes/controls/media.php:283 includes/controls/media.php:285
#: assets/js/editor.js:6029
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:3
#: assets/js/packages/editor-controls/editor-controls.strings.js:34
msgid "Upload"
msgstr "Prześlij"

#: includes/admin-templates/beta-tester.php:37 assets/js/beta-tester.js:64
msgid "Sign Up"
msgstr "Zarejestruj się"

#: core/editor/editor.php:201
msgid "Document not found."
msgstr "Nie znaleziono dokumentu."

#: core/debug/classes/theme-missing.php:22
msgid "Some of your theme files are missing."
msgstr "Brakuje niektórych plików motywu."

#: includes/settings/settings.php:361
msgid "We recommend you only enable this feature if you understand the security risks involved."
msgstr "Zalecamy włączenie tej funkcji tylko wtedy, gdy rozumiesz związane z tym zagrożenia bezpieczeństwa."

#: includes/managers/icons.php:488
msgid "Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome."
msgstr "Należy pamiętać, że proces aktualizacji może spowodować, że niektóre z wcześniej używanych ikon Font Awesome 4 wyglądają nieco inaczej ze względu na drobne zmiany projektowe dokonane przez Font Awesome."

#: includes/controls/groups/background.php:573
msgid "Play Once"
msgstr "Odtwórz raz"

#: includes/managers/icons.php:464
msgid "Load Font Awesome 4 Support"
msgstr "Wczytaj obsługę Font Awesome 4"

#: includes/admin-templates/beta-tester.php:29
msgid "Get Beta Updates"
msgstr "Otrzymaj aktualizacje beta"

#: includes/managers/icons.php:551
msgid "Hurray! The upgrade process to Font Awesome 5 was completed successfully."
msgstr "Hura! Aktualizacja do Font Awesome 5 zakończyła się pomyślnie."

#: includes/settings/settings.php:361
msgid "Please note! Allowing uploads of any files (SVG & JSON included) is a potential security risk."
msgstr "Uwaga! Zezwolenie na przesyłanie dowolnych plików (w tym SVG i JSON) to potencjalne ryzyko bezpieczeństwa."

#: includes/admin-templates/beta-tester.php:30
msgid "As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email"
msgstr "Jako tester wersji beta otrzymasz aktualizację zawierającą testową wersję Elementora i jego zawartości bezpośrednio na e-mail"

#: includes/managers/icons.php:485
msgid "Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility."
msgstr "Uzyskaj dostęp do ponad 1500 niesamowitych czcionek Font Awesome 5 i ciesz się większą wydajnością i elastycznością."

#: includes/managers/icons.php:479 includes/managers/icons.php:483
#: includes/managers/icons.php:498
msgid "Font Awesome Upgrade"
msgstr "Aktualizacja Font Awesome"

#: includes/managers/icons.php:472
msgid "Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library."
msgstr "Skrypt wsparcia Font Awesome 4 (shim.js) to skrypt, który zapewnia, że wszystkie wcześniej wybrane ikony Font Awesome 4 są wyświetlane poprawnie podczas korzystania z biblioteki Font Awesome 5."

#: core/debug/classes/htaccess.php:12
msgid "Your site's .htaccess file appears to be missing."
msgstr "Wygląda na to, że w witrynie brakuje pliku .htaccess."

#: includes/managers/icons.php:493
msgid "This action is not reversible and cannot be undone by rolling back to previous versions."
msgstr "Ta akcja jest nieodwracalna i nie można jej cofnąć wracając do poprzednich wersji."

#: core/experiments/manager.php:659 includes/base/widget-base.php:1019
msgid "Deprecated"
msgstr "Przestarzałe"

#: includes/settings/settings.php:361
msgid "Elementor will try to sanitize the unfiltered files, removing potential malicious code and scripts."
msgstr "Elementor spróbuje oczyścić pliki, usuwając potencjalnie szkodliwy kod i skrypty."

#: core/files/file-types/svg.php:73 core/files/uploads-manager.php:590
msgid "This file is not allowed for security reasons."
msgstr "Ten plik jest niedozwolony ze względów bezpieczeństwa."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/elements/column.php:934 includes/elements/container.php:1895
#: includes/elements/section.php:1401 includes/widgets/common-base.php:1324
msgid "Responsive visibility will take effect only on %1$s preview mode %2$s or live page, and not while editing in Elementor."
msgstr "Widoczność responsywna będzie działać tylko w trybie podglądu %1$s %2$s lub na stronie na żywo, a nie podczas edycji w Elementorze."

#: includes/managers/icons.php:486
msgid "By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon."
msgstr "Po uaktualnieniu, za każdym razem, gdy edytujesz stronę zawierającą ikonkę Font Awesome 4, Elementor przekonwertuje ją na nową ikonkę Font Awesome 5."

#: includes/managers/icons.php:132
msgid "Font Awesome - Regular"
msgstr "Font Awesome - Regularna"

#: includes/managers/icons.php:144
msgid "Font Awesome - Solid"
msgstr "Font Awesome - Jednolita"

#: includes/managers/icons.php:156
msgid "Font Awesome - Brands"
msgstr "Font Awesome - Marki"

#: includes/managers/icons.php:248
msgid "All Icons"
msgstr "Wszystkie ikonki"

#: includes/controls/icons.php:90 includes/controls/icons.php:116
#: includes/controls/icons.php:202 assets/js/editor.js:6610
msgid "Icon Library"
msgstr "Biblioteka ikonek"

#: includes/managers/icons.php:505
msgid "Upgrade To Font Awesome 5"
msgstr "Ulepsz do Font Awesome 5"

#: includes/elements/container.php:1658 includes/widgets/common-base.php:675
msgid "Vertical Orientation"
msgstr "Orientacja pionowa"

#: includes/elements/container.php:1557 includes/widgets/common-base.php:574
msgid "Horizontal Orientation"
msgstr "Orientacja pozioma"

#: includes/settings/admin-menu-items/get-help-menu-item.php:23
msgid "Get Help"
msgstr "Uzyskaj pomoc"

#: includes/elements/container.php:562 includes/elements/section.php:453
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:130
msgid "Hidden"
msgstr "Ukryty"

#: includes/elements/column.php:869 includes/elements/container.php:1815
#: includes/elements/section.php:1310 includes/widgets/common-base.php:830
msgid "Motion Effects"
msgstr "Efekty ruchu"

#: includes/elements/section.php:420 includes/widgets/common-base.php:514
#: includes/widgets/image-carousel.php:743
msgid "Vertical Align"
msgstr "Wyrównanie pionowe"

#: includes/controls/groups/flex-item.php:30
#: includes/widgets/common-base.php:359
msgid "Custom Width"
msgstr "Własna szerokość"

#: includes/settings/controls.php:236
msgid "Super Admin"
msgstr "Superadministrator"

#: includes/elements/container.php:557 includes/elements/section.php:448
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:128
msgid "Overflow"
msgstr "Przepływ"

#: includes/elements/container.php:1538 includes/widgets/common-base.php:560
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:209
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:145
msgid "Fixed"
msgstr "Określone"

#: includes/base/element-base.php:1010 includes/elements/container.php:1582
#: includes/elements/container.php:1620 includes/elements/container.php:1682
#: includes/elements/container.php:1719 includes/widgets/common-base.php:599
#: includes/widgets/common-base.php:637 includes/widgets/common-base.php:699
#: includes/widgets/common-base.php:736
#: modules/floating-buttons/base/widget-contact-button-base.php:2961
#: modules/floating-buttons/base/widget-contact-button-base.php:3015
msgid "Offset"
msgstr "Przesunięcie"

#: includes/elements/container.php:1537 includes/widgets/common-base.php:559
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:144
msgid "Absolute"
msgstr "Absolutnie"

#: includes/elements/container.php:1520 includes/widgets/common-base.php:543
msgid "Custom positioning is not considered best practice for responsive web design and should not be used too frequently."
msgstr "Pozycjonowanie własne nie jest uważane za najlepszą praktykę w przypadku projektowania responsywnych stron internetowych i nie powinno być stosowane zbyt często."

#: includes/controls/groups/flex-container.php:117
#: includes/controls/groups/flex-container.php:230
#: includes/controls/groups/grid-container.php:212
#: includes/controls/groups/grid-container.php:252
#: includes/elements/column.php:193 includes/elements/column.php:221
#: includes/elements/section.php:430
msgid "Space Evenly"
msgstr "Równomiernie rozmieszczaj"

#: includes/controls/groups/flex-container.php:113
#: includes/controls/groups/flex-container.php:226
#: includes/controls/groups/grid-container.php:208
#: includes/controls/groups/grid-container.php:248
#: includes/elements/column.php:192 includes/elements/column.php:220
#: includes/elements/section.php:429
msgid "Space Around"
msgstr "Odstęp wokół"

#: includes/elements/container.php:1519 includes/widgets/common-base.php:542
msgid "Please note!"
msgstr "Uwaga!"

#: modules/safe-mode/module.php:256
msgid "The issue was probably caused by one of your plugins or theme."
msgstr "Problem był prawdopodobnie spowodowany przez jedną z wtyczek lub motyw."

#. translators: %s: Accepted chars.
#: includes/widgets/menu-anchor.php:136
msgid "Note: The ID link ONLY accepts these chars: %s"
msgstr "Uwaga: odnośnik identyfikatora akceptuje TYLKO następujące znaki: %s"

#: modules/safe-mode/module.php:358
msgid "Having problems loading Elementor? Please enable Safe Mode to troubleshoot."
msgstr "Masz problemy z wczytaniem Elementora? Włącz tryb awaryjny, aby rozwiązać problem."

#: includes/controls/media.php:198
#: modules/link-in-bio/base/widget-link-in-bio-base.php:389
msgid "Choose File"
msgstr "Wybierz plik"

#: includes/widgets/google-maps.php:154
#: modules/floating-buttons/base/widget-contact-button-base.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:958
#: modules/link-in-bio/base/widget-link-in-bio-base.php:502
#: modules/link-in-bio/base/widget-link-in-bio-base.php:753
msgid "Location"
msgstr "Lokalizacja"

#: includes/template-library/sources/local.php:322
msgctxt "Template Library"
msgid "Category"
msgstr "Kategoria"

#: includes/template-library/sources/local.php:321
msgctxt "Template Library"
msgid "Categories"
msgstr "Kategorie"

#: includes/template-library/sources/local.php:323
msgctxt "Template Library"
msgid "All Categories"
msgstr "Wszystkie kategorie"

#: core/admin/menu/main.php:34 core/admin/menu/main.php:35
#: includes/template-library/sources/local.php:239
msgctxt "Template Library"
msgid "Templates"
msgstr "Szablony"

#: modules/safe-mode/module.php:352 modules/safe-mode/module.php:364
msgid "Can't Edit?"
msgstr "Nie możesz edytować?"

#: modules/safe-mode/module.php:269
msgid "Still experiencing issues?"
msgstr "Nadal masz problemy?"

#: modules/safe-mode/module.php:253
msgid "Editor successfully loaded?"
msgstr "Czy edytor został pomyślnie załadowany?"

#: modules/safe-mode/module.php:246 modules/safe-mode/module.php:476
msgid "Disable Safe Mode"
msgstr "Wyłącz tryb awaryjny"

#: modules/safe-mode/module.php:102
msgid "Cannot enable Safe Mode"
msgstr "Nie można włączyć trybu awaryjnego"

#. translators: %d: Interval in minutes.
#: core/base/background-process/wp-background-process.php:439
#: core/base/background-task.php:316
msgid "Every %d minutes"
msgstr "Co %d minut"

#. translators: %s: Current post name.
#: includes/frontend.php:1522
msgid "Continue reading %s"
msgstr "Czytaj dalej %s"

#: includes/frontend.php:1515
msgid "(more&hellip;)"
msgstr "(więcej&hellip;)"

#: includes/widgets/read-more.php:95
msgid "Continue reading"
msgstr "Czytaj dalej"

#: includes/widgets/video.php:245
msgid "External URL"
msgstr "Zewnętrzny adres URL"

#: modules/safe-mode/module.php:354
msgid "Enable Safe Mode"
msgstr "Włącz tryb bezpieczny"

#: modules/safe-mode/module.php:51
msgid "Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin."
msgstr "Tryb awaryjny umożliwia rozwiązywanie problemów przez ładowanie TYLKO edytora, bez ładowania motywu lub innych wtyczek."

#: modules/safe-mode/module.php:42
msgid "Safe Mode"
msgstr "Tryb awaryjny"

#: modules/promotions/admin-menu-items/popups-promotion-item.php:19
msgid "The Popup Builder lets you take advantage of all the amazing features in Elementor, so you can build beautiful & highly converting popups. Get Elementor Pro and start designing your popups today."
msgstr "Kreator wyskakujących okienek pozwala korzystać ze wszystkich niesamowitych funkcji Elementora, dzięki czemu można tworzyć piękne i personalizowane wyskakujące okienka. Bądź pro i zacznij projektować pop-upy już dziś."

#: core/base/db-upgrades-manager.php:130
msgid "The database update process is now complete. Thank you for updating to the latest version!"
msgstr "Proces aktualizacji bazy danych został zakończony. Dziękujemy za aktualizację do najnowszej wersji!"

#: core/base/db-upgrades-manager.php:93
msgid "Your site database needs to be updated to the latest version."
msgstr "Baza danych musi zostać zaktualizowana do najnowszej wersji."

#: modules/safe-mode/module.php:244
msgid "Safe Mode ON"
msgstr "Tryb awaryjny WŁĄCZONY"

#: includes/widgets/read-more.php:40 includes/widgets/read-more.php:91
msgid "Read More"
msgstr "Dowiedz się więcej"

#: includes/template-library/sources/local.php:1733
#: modules/promotions/admin-menu-items/popups-promotion-item.php:41
#: modules/promotions/admin-menu-items/popups-promotion-item.php:45
#: assets/js/app.js:10364 assets/js/app.js:15436
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:2541
msgid "Popups"
msgstr "Wyskakujące okienka"

#: includes/widgets/read-more.php:124
msgid "Read More Text"
msgstr "Tekst Dowiedz się więcej"

#: includes/template-library/sources/local.php:1455
msgctxt "Template Library"
msgid "Filter by category"
msgstr "Filtruj wg kategorii"

#: includes/widgets/image-gallery.php:263
msgid "Order By"
msgstr "Kolejność wg"

#: modules/promotions/admin-menu-items/popups-promotion-item.php:18
msgid "Get Popup Builder"
msgstr "Pobierz kreator wyskakujących okienek"

#: app/admin-menu-items/theme-builder-menu-item.php:22
#: app/modules/site-editor/module.php:31
#: core/common/modules/finder/categories/general.php:72
#: includes/template-library/sources/local.php:1732
#: assets/js/app-packages.js:4809 assets/js/editor.js:47658
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:7
msgid "Theme Builder"
msgstr "Kreator motywu"

#: core/upgrade/manager.php:51
msgid "Elementor Data Updater"
msgstr "Aktualizator danych Elementora"

#. translators: %s: The `the_content` function.
#: includes/widgets/read-more.php:115
msgid "Note: This widget only affects themes that use `%s` in archive pages."
msgstr "Uwaga: Ten widżet dotyczy tylko motywów, które używają `%s` na stronach archiwalnych."

#: modules/library/documents/not-supported.php:56
msgid "Not Supported"
msgstr "Nieobsługiwane"

#: core/common/modules/finder/categories/site.php:82
msgid "Users"
msgstr "Użytkownicy"

#: core/common/modules/finder/categories/site.php:76 assets/js/app.js:5818
#: assets/js/app.js:7291 assets/js/app.js:10383 assets/js/app.js:15956
msgid "Plugins"
msgstr "Wtyczki"

#: includes/widgets/star-rating.php:318
msgid "Stars"
msgstr "Gwiazdki"

#: includes/widgets/rating.php:22 includes/widgets/rating.php:140
#: includes/widgets/rating.php:164 includes/widgets/star-rating.php:153
msgid "Rating"
msgstr "Ocena"

#: includes/editor-templates/hotkeys.php:21 assets/js/ai-admin.js:12592
#: assets/js/ai-admin.js:13678 assets/js/ai-gutenberg.js:12810
#: assets/js/ai-gutenberg.js:13896 assets/js/ai-media-library.js:12592
#: assets/js/ai-media-library.js:13678
#: assets/js/ai-unify-product-images.js:12592
#: assets/js/ai-unify-product-images.js:13678 assets/js/ai.js:14144
#: assets/js/ai.js:15230 assets/js/editor.js:9508
#: assets/js/kit-elements-defaults-editor.js:232
msgid "Undo"
msgstr "Cofnij"

#: includes/editor-templates/hotkeys.php:160
msgid "Go To"
msgstr "Przejdź do"

#: core/common/modules/finder/categories/site.php:58 assets/js/app.js:7259
msgid "Menus"
msgstr "Menu"

#: core/common/modules/finder/categories/site.php:52
msgid "Dashboard"
msgstr "Kokpit"

#: modules/pro-install/pro-install-menu-item.php:76
msgid "Connect to Elementor"
msgstr "Połącz z Elementorem"

#: core/common/modules/connect/apps/base-app.php:87
#: modules/pro-install/pro-install-menu-item.php:120
msgid "Disconnect"
msgstr "Rozłącz"

#: core/common/modules/connect/apps/base-app.php:228
#: core/common/modules/connect/rest/rest-api.php:117
msgid "Disconnected successfully."
msgstr "Rozłączenie udane."

#: core/common/modules/connect/apps/base-app.php:216
#: core/common/modules/connect/rest/rest-api.php:87 assets/js/editor.js:9907
#: assets/js/editor.js:9972 assets/js/editor.js:10954
msgid "Connected successfully."
msgstr "Połączenie udane."

#: includes/widgets/image-gallery.php:175 includes/widgets/image.php:162
msgid "Attachment Caption"
msgstr "Podpis załącznika"

#: includes/widgets/star-rating.php:192
msgid "Outline"
msgstr "Kontur"

#: includes/widgets/rating.php:147 includes/widgets/star-rating.php:140
msgid "Rating Scale"
msgstr "Skala oceny"

#: includes/widgets/video.php:517
msgid "Any Video"
msgstr "Dowolny film"

#: includes/editor-templates/hotkeys.php:201
msgid "Quit"
msgstr "Opuść"

#: core/common/modules/finder/template.php:13
msgid "Type to find anything in Elementor"
msgstr "Wpisz, aby wyszukać cokolwiek w Elementorze"

#: includes/widgets/video.php:516
msgid "Current Video Channel"
msgstr "Aktualny kanał filmowy"

#: core/common/modules/connect/apps/connect.php:11
#: core/common/modules/connect/connect-menu-item.php:24
#: core/common/modules/connect/connect-menu-item.php:28
#: core/utils/hints.php:461 includes/editor-templates/templates.php:501
#: modules/cloud-library/module.php:147 assets/js/ai-admin.js:1073
#: assets/js/ai-admin.js:6392 assets/js/ai-gutenberg.js:1211
#: assets/js/ai-gutenberg.js:6610 assets/js/ai-layout.js:771
#: assets/js/ai-layout.js:2498 assets/js/ai-media-library.js:1073
#: assets/js/ai-media-library.js:6392 assets/js/ai-unify-product-images.js:1073
#: assets/js/ai-unify-product-images.js:6392 assets/js/ai.js:1861
#: assets/js/ai.js:7853 assets/js/ai.js:7944 assets/js/editor.js:9857
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:3148
msgid "Connect"
msgstr "Połącz"

#: includes/widgets/image.php:163 includes/widgets/image.php:175
msgid "Custom Caption"
msgstr "Własny podpis"

#: includes/editor-templates/hotkeys.php:96 assets/js/admin-top-bar.js:187
#: assets/js/common.js:2886 assets/js/editor.js:38647
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:18
msgid "Finder"
msgstr "Wyszukiwarka"

#: core/common/modules/finder/categories/create.php:27 assets/js/editor.js:8884
#: assets/js/editor.js:46256
#: assets/js/packages/editor-components/editor-components.js:2
#: assets/js/packages/editor-components/editor-components.strings.js:8
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:30
msgid "Create"
msgstr "Utwórz"

#: core/base/document.php:1987
msgid "Future"
msgstr "Przyszły"

#: includes/editor-templates/hotkeys.php:193 assets/js/editor.js:5340
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:16
msgid "Keyboard Shortcuts"
msgstr "Skróty klawiaturowe"

#: core/common/modules/finder/categories/site.php:46
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:12
msgid "Homepage"
msgstr "Strona domowa"

#: includes/widgets/rating.php:122 includes/widgets/star-rating.php:388
msgid "Unmarked Color"
msgstr "Kolor nieoznakowany"

#: includes/widgets/star-rating.php:184
msgid "Unmarked Style"
msgstr "Styl nieoznakowany"

#: core/common/modules/finder/categories/site.php:70
msgid "Customizer"
msgstr "Konfigurator"

#: includes/editor-templates/hotkeys.php:29 assets/js/ai-admin.js:12603
#: assets/js/ai-admin.js:13689 assets/js/ai-gutenberg.js:12821
#: assets/js/ai-gutenberg.js:13907 assets/js/ai-media-library.js:12603
#: assets/js/ai-media-library.js:13689
#: assets/js/ai-unify-product-images.js:12603
#: assets/js/ai-unify-product-images.js:13689 assets/js/ai.js:14155
#: assets/js/ai.js:15241
msgid "Redo"
msgstr "Powtórz"

#: includes/widgets/star-rating.php:45 includes/widgets/star-rating.php:122
msgid "Star Rating"
msgstr "Ocena gwiazdkowa"

#: includes/editor-templates/hotkeys.php:104
msgid "Show / Hide Panel"
msgstr "Pokaż / ukryj panel"

#: includes/widgets/video.php:619
msgid "Poster"
msgstr "Plakat"

#: includes/widgets/inner-section.php:35 assets/js/editor.js:28043
msgid "Inner Section"
msgstr "Sekcja wewnętrzna"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:36
msgid "Create Your First Page"
msgstr "Stwórz swoją pierwszą stronę"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:61
msgid "Welcome to Elementor"
msgstr "Witamy w Elementorze"

#: core/admin/admin-notices.php:328
msgid "Hide Notification"
msgstr "Ukryj powiadomienia"

#: core/admin/admin-notices.php:318
msgid "Congrats!"
msgstr "Gratulacje!"

#: includes/editor-templates/library-layout.php:13
#: includes/settings/admin-menu-items/getting-started-menu-item.php:56
#: modules/announcements/module.php:124 assets/js/app-packages.js:1365
#: assets/js/app.js:1830
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:1305
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:1397
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:1648
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:1821
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:2141
msgid "Skip"
msgstr "Pomiń"

#: modules/floating-buttons/base/widget-contact-button-base.php:951
#: modules/floating-buttons/base/widget-floating-bars-base.php:166
#: modules/floating-buttons/base/widget-floating-bars-base.php:352
#: modules/link-in-bio/base/widget-link-in-bio-base.php:271
#: modules/shapes/widgets/text-path.php:164
msgid "Paste URL or type"
msgstr "Wklej lub napisz adres URL"

#: includes/widgets/accordion.php:171 includes/widgets/accordion.php:175
#: includes/widgets/icon-box.php:186 includes/widgets/image-box.php:161
#: includes/widgets/tabs.php:170 includes/widgets/tabs.php:174
#: includes/widgets/testimonial.php:145 includes/widgets/text-editor.php:142
#: includes/widgets/toggle.php:174 includes/widgets/toggle.php:178
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."

#: includes/editor-templates/navigator.php:96
msgid "Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget."
msgstr "Gdy wypełnisz stronę treścią, to okno da ci podgląd wszystkich elementów strony. W ten sposób możesz łatwo przemieszczać różne sekcje, kolumny i widgety."

#: core/admin/admin-notices.php:322
msgid "Happy To Help"
msgstr "Chętnie pomogę"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:23
#: includes/settings/admin-menu-items/getting-started-menu-item.php:27
#: includes/settings/admin-menu-items/getting-started-menu-item.php:52
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:577
msgid "Getting Started"
msgstr "Pierwsze kroki"

#: core/debug/inspector.php:49
msgid "Debug Bar"
msgstr "Pasek debugowania"

#: includes/editor-templates/navigator.php:90
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:69
msgid "Empty"
msgstr "Puste"

#: core/admin/admin-notices.php:319
msgid "You created over 10 pages with Elementor. Great job! If you can spare a minute, please help us by leaving a five star review on WordPress.org."
msgstr "Z pomocą Elementora utworzyłęś ponad 10 stron. Dobra robota! Jeśli możesz poświęcić chwilę, pomóż nam, zostawiając pięciogwiazdkową recenzję na WordPress.org."

#: includes/widgets/video.php:479
msgid "Lazy Load"
msgstr "Leniwe wczytywanie"

#: includes/widgets/video.php:181 includes/widgets/video.php:206
#: includes/widgets/video.php:230 includes/widgets/video.php:290
msgid "Enter your URL"
msgstr "Wpisz swój adres URL"

#: includes/controls/groups/css-filter.php:129
msgctxt "Filter Control"
msgid "Hue"
msgstr "Odcień"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:39
msgid "Create Your First Post"
msgstr "Utwórz swój pierwszy wpis"

#: includes/editor-templates/navigator.php:95
msgid "Easy Navigation is Here!"
msgstr "Łatwa nawigacja już dostępna!"

#: core/kits/documents/tabs/settings-lightbox.php:76 includes/frontend.php:1383
#: includes/widgets/google-maps.php:174 assets/js/ai-admin.js:10220
#: assets/js/ai-admin.js:10223 assets/js/ai-gutenberg.js:10438
#: assets/js/ai-gutenberg.js:10441 assets/js/ai-media-library.js:10220
#: assets/js/ai-media-library.js:10223
#: assets/js/ai-unify-product-images.js:10220
#: assets/js/ai-unify-product-images.js:10223 assets/js/ai.js:11772
#: assets/js/ai.js:11775
msgid "Zoom"
msgstr "Powiększ"

#: core/debug/inspector.php:57
msgid "Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed."
msgstr "Inspektor dodaje menu do paska administracyjnego, które zawiera listę wszystkich szablonów używanych na wyświetlanej stronie."

#: core/document-types/page-base.php:47
msgid "Single"
msgstr "Pojedyncze"

#: includes/widgets/video.php:451
msgid "Logo"
msgstr "Logo"

#: includes/widgets/video.php:424
msgid "Video Info"
msgstr "Informacje o filmie"

#: includes/widgets/video.php:161
msgid "Dailymotion"
msgstr "Dailymotion"

#: includes/widgets/video.php:155
msgid "Source"
msgstr "Źródło"

#: includes/managers/elements.php:317
msgid "WooCommerce"
msgstr "WooCommerce"

#: includes/editor-templates/templates.php:121
#: includes/editor-templates/templates.php:620
#: includes/managers/elements.php:292
#: modules/promotions/widgets/pro-widget-promotion.php:71
#: assets/js/ai-admin.js:7987 assets/js/ai-gutenberg.js:8205
#: assets/js/ai-layout.js:3475 assets/js/ai-media-library.js:7987
#: assets/js/ai-unify-product-images.js:7987 assets/js/ai.js:9539
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:3596
msgid "Pro"
msgstr "Pro"

#: includes/editor-templates/hotkeys.php:38
#: includes/editor-templates/templates.php:160 assets/js/editor.js:8582
#: assets/js/editor.js:8596 assets/js/editor.js:30696
msgid "Copy"
msgstr "Kopiuj"

#: includes/controls/groups/css-filter.php:97
msgctxt "Filter Control"
msgid "Contrast"
msgstr "Kontrast"

#: includes/widgets/video.php:163
msgid "Self Hosted"
msgstr "Własne hostowanie"

#: includes/controls/groups/css-filter.php:81
msgctxt "Filter Control"
msgid "Brightness"
msgstr "Jasność"

#: includes/controls/groups/background.php:563 includes/widgets/video.php:341
msgid "Specify an end time (in seconds)"
msgstr "Określ czas zakończenia (w sekundach)"

#: includes/controls/groups/background.php:551 includes/widgets/video.php:330
msgid "Specify a start time (in seconds)"
msgstr "Określ czas rozpoczęcia (w sekundach)"

#: core/admin/feedback.php:107
msgid "Wait! Don't deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work."
msgstr "Poczekaj! Nie wyłączaj Elementora. Musisz aktywować zarówno Elementor, jak i Elementor Pro, aby wtyczka działała."

#: core/admin/feedback.php:105
msgid "I have Elementor Pro"
msgstr "Mam Elementor Pro"

#: core/debug/inspector.php:115
msgid "Elementor Debugger"
msgstr "Debugger Elementora"

#: core/base/providers/social-network-provider.php:216
#: includes/widgets/video.php:277 includes/widgets/video.php:301
msgid "URL"
msgstr "Adres URL"

#: app/modules/site-editor/module.php:32
#: core/common/modules/finder/categories/site.php:26 core/kits/manager.php:437
#: includes/managers/elements.php:310 modules/admin-bar/module.php:149
msgid "Site"
msgstr "Witryna"

#: includes/controls/groups/background.php:549 includes/widgets/video.php:328
msgid "Start Time"
msgstr "Czas rozpoczęcia"

#: includes/controls/groups/background.php:561 includes/widgets/video.php:339
msgid "End Time"
msgstr "Czas zakończenia"

#: includes/controls/groups/css-filter.php:62
msgctxt "Filter Control"
msgid "Blur"
msgstr "Rozmycie"

#: includes/widgets/traits/button-trait.php:205
msgid "Button ID"
msgstr "Identyfikator przycisku"

#: includes/controls/groups/css-filter.php:113
msgctxt "Filter Control"
msgid "Saturation"
msgstr "Nasycenie"

#. translators: 1: `<code>` opening tag, 2: `</code>` closing tag.
#: includes/widgets/traits/button-trait.php:217
msgid "Please make sure the ID is unique and not used elsewhere on the page. This field allows %1$sA-z 0-9%2$s & underscore chars without spaces."
msgstr "Upewnij się, że identyfikator jest unikatowy i nie jest używany nigdzie indziej na stronie. To pole pozwala na użycie znaków %1$sA-z 0-9%2$s i podkreślenia bez spacji."

#: includes/widgets/audio.php:193
msgid "Artwork"
msgstr "Dzieło sztuki"

#: includes/elements/column.php:447 includes/elements/container.php:858
#: includes/elements/section.php:715 includes/widgets/heading.php:324
msgid "Blend Mode"
msgstr "Tryb mieszania"

#: includes/editor-templates/global.php:49
#: assets/js/152f977e0c1304a3b0db.bundle.js:128
msgid "Drag widget here"
msgstr "Przeciągnij widżet tutaj"

#: core/admin/admin.php:219 assets/js/admin.js:2096 assets/js/gutenberg.js:147
msgid "Back to WordPress Editor"
msgstr "Powrót do edytora WordPress"

#. translators: %s: Document title.
#: core/documents-manager.php:388
msgid "Elementor %s"
msgstr "Elementor %s"

#. translators: %s: Document title.
#. translators: %s: Post type singular name.
#. translators: %s: Singular label.
#. translators: %s: Template type label.
#: core/base/document.php:270
#: core/common/modules/finder/categories/create.php:88
#: core/document-types/page-base.php:189
#: includes/template-library/sources/local.php:1416
msgid "Add New %s"
msgstr "Utwórz %s"

#: core/kits/documents/tabs/theme-style-images.php:95
#: core/kits/documents/tabs/theme-style-images.php:166
#: includes/elements/column.php:416 includes/elements/column.php:490
#: includes/elements/container.php:812 includes/elements/container.php:926
#: includes/elements/section.php:669 includes/elements/section.php:773
#: includes/widgets/image-box.php:504 includes/widgets/image-box.php:539
#: includes/widgets/image.php:441 includes/widgets/image.php:475
#: modules/floating-buttons/base/widget-floating-bars-base.php:1010
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1502
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:214
msgid "Opacity"
msgstr "Krycie"

#: includes/widgets/image.php:319
msgid "Max Width"
msgstr "Maksymalna szerokość"

#: includes/template-library/sources/local.php:232
#: assets/js/new-template.js:147
msgid "New Template"
msgstr "Nowy szablon"

#: includes/controls/groups/background.php:265
#: includes/controls/groups/background.php:313
#: includes/controls/groups/box-shadow.php:69
#: includes/elements/container.php:1532 includes/widgets/common-base.php:554
#: includes/widgets/common-base.php:1185 includes/widgets/divider.php:766
#: includes/widgets/divider.php:932 includes/widgets/image-carousel.php:583
#: includes/widgets/image-carousel.php:647 includes/widgets/tabs.php:184
#: includes/widgets/traits/button-trait.php:252
#: modules/floating-buttons/base/widget-floating-bars-base.php:448
#: modules/link-in-bio/base/widget-link-in-bio-base.php:943
#: modules/link-in-bio/base/widget-link-in-bio-base.php:998
#: modules/nested-accordion/widgets/nested-accordion.php:218
#: modules/nested-tabs/widgets/nested-tabs.php:862
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:46
#: assets/js/packages/editor-controls/editor-controls.strings.js:208
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:12
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:139
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:141
msgid "Position"
msgstr "Pozycja"

#. translators: 1: Elementor, 2: Link to plugin review
#: core/admin/admin.php:408
msgid "Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!"
msgstr "Podoba się%1$s? Proszę zostaw %2$s ocenę. My naprawdę doceniamy każde wsparcie!"

#: core/common/modules/finder/categories/general.php:67
msgid "Knowledge Base"
msgstr "Baza wiedzy"

#: modules/page-templates/module.php:297
msgid "Page Layout"
msgstr "Układ strony"

#: modules/page-templates/module.php:340
msgid "No header, no footer, just Elementor"
msgstr "Bez nagłówka, bez stopki, tylko Elementor"

#: modules/page-templates/module.php:328
msgid "Default Page Template from your theme."
msgstr "Domyślny szablon strony z twojego motywu."

#: modules/page-templates/module.php:352
msgid "This template includes the header, full-width content and footer"
msgstr "Ten szablon zawiera nagłówek, pełną treść i stopkę"

#: core/document-types/page.php:72 modules/library/documents/page.php:61
#: assets/js/app.js:16255 assets/js/editor.js:7964
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:2513
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:1
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:3
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:7
msgid "Pages"
msgstr "Strony"

#: includes/template-library/sources/local.php:227
msgctxt "Template Library"
msgid "My Templates"
msgstr "Moje szablony"

#: core/document-types/page-base.php:262
msgid "Featured Image"
msgstr "Obrazek wyróżniający"

#: includes/admin-templates/new-floating-elements.php:47
#: includes/admin-templates/new-template.php:112
msgid "Enter template name (optional)"
msgstr "Wprowadź nazwę szablonu (opcjonalnie)"

#: includes/frontend.php:1385 includes/widgets/video.php:999
msgid "Play Video"
msgstr "Odtwórz wideo"

#: includes/admin-templates/new-floating-elements.php:44
#: includes/admin-templates/new-template.php:109
msgid "Name your template"
msgstr "Nazwa własnego szablonu"

#: includes/editor-templates/templates.php:148
msgid "Search Templates:"
msgstr "Wyszukaj szablony:"

#: core/role-manager/role-manager.php:157
msgid "No access to editor"
msgstr "Brak dostępu do edytora"

#: core/role-manager/role-manager.php:239
msgid "Want to give access only to content?"
msgstr "Chcesz przyznać dostęp tylko do treści?"

#: core/common/modules/finder/categories/general.php:61
#: core/role-manager/role-manager-menu-item.php:28
#: core/role-manager/role-manager-menu-item.php:32
#: core/role-manager/role-manager.php:50
msgid "Role Manager"
msgstr "Menedżer ról"

#: core/role-manager/role-manager.php:150
msgid "Role Excluded"
msgstr "Rola wykluczona"

#: includes/template-library/sources/local.php:1376
msgid "Add templates and reuse them across your website. Easily export and import them to any other project, for an optimized workflow."
msgstr "Dodaj szablony i wykorzystaj je ponownie w swojej witrynie. Łatwo eksportuj i importuj je do dowolnego innego projektu, aby zoptymalizować sobie pracę."

#: includes/admin-templates/new-template.php:57
msgid "Use templates to create the different pieces of your site, and reuse them with one click whenever needed."
msgstr "Korzystaj z szablonów, aby tworzyć różne elementy witryny i ponownie je wykorzystuj za pomocą jednego kliknięcia, gdy zajdzie taka potrzeba."

#: core/base/document.php:260
msgid "Document"
msgstr "Dokument"

#: core/common/modules/ajax/module.php:165
msgid "Action not found."
msgstr "Nie znaleziono akcji."

#. translators: %s: Template type label.
#: includes/template-library/sources/local.php:1409
msgid "Create Your First %s"
msgstr "Stwórz swój pierwszy %s"

#: includes/widgets/counter.php:210
msgid "Separator"
msgstr "Separator"

#: core/role-manager/role-manager.php:114
msgid "Manage What Your Users Can Edit In Elementor"
msgstr "Zarządzaj tym, co Twoi użytkownicy mogą edytować w Elementorze"

#: includes/widgets/common-base.php:343 includes/widgets/icon-list.php:126
#: includes/widgets/icon-list.php:217
msgid "Inline"
msgstr "W treści"

#: core/kits/documents/tabs/global-typography.php:182
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:19
#: assets/js/app.js:10375
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:252
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:235
msgid "Custom Fonts"
msgstr "Własne kroje pisma"

#: core/common/modules/ajax/module.php:131
msgid "Token Expired."
msgstr "Token wygasł."

#: includes/template-library/sources/local.php:1281 assets/js/app.js:8215
msgid "All"
msgstr "Wszystko"

#: includes/editor-templates/templates.php:300
#: includes/editor-templates/templates.php:341
#: includes/editor-templates/templates.php:386
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:54
msgid "More actions"
msgstr "Więcej działań"

#: includes/admin-templates/new-template.php:115
msgid "Create Template"
msgstr "Utwórz szablon"

#: core/document-types/page-base.php:186
#: includes/template-library/sources/admin-menu-items/add-new-template-menu-item.php:23
#: modules/landing-pages/module.php:296 assets/js/app-packages.js:3227
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:8
msgid "Add New"
msgstr "Utwórz"

#: includes/editor-templates/global.php:120
msgid "This tag has no settings."
msgstr "Ten znacznik nie ma ustawień."

#. translators: %s: Document title.
#. translators: %s: Post type label.
#: core/base/document.php:1222 core/settings/page/model.php:127
#: includes/editor-templates/panel.php:80
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:32
msgid "%s Settings"
msgstr "Ustawienia %s"

#: core/dynamic-tags/tag.php:115 includes/settings/settings.php:390
msgid "Fallback"
msgstr "Zapasowy"

#: core/document-types/page-base.php:124
msgid "Body Style"
msgstr "Styl ciała"

#: includes/admin-templates/new-template.php:64
msgid "Choose Template Type"
msgstr "Wybierz rodzaj szablonu"

#: includes/admin-templates/new-template.php:66
msgid "Select the type of template you want to work on"
msgstr "Wybierz rodzaj szablonu, nad którym chcesz pracować"

#: includes/widgets/image-carousel.php:204
msgid "Set how many slides are scrolled per swipe."
msgstr "Ustaw liczbę przewijanych slajdów na jednym przesunięciu."

#: includes/controls/groups/background.php:448
msgid "Note: Attachment Fixed works only on desktop."
msgstr "Uwaga: Poprawka dotycząca załącznika działa tylko na komputerze."

#: core/admin/admin.php:477
msgid "Create New Post"
msgstr "Utwórz wpis"

#: includes/fonts.php:77
msgid "Google (Early Access)"
msgstr "Google (wczesny dostęp)"

#: modules/history/revisions-manager.php:157
msgid "Current Version"
msgstr "Aktualna wersja"

#: includes/controls/groups/background.php:597 includes/widgets/video.php:466
msgid "Privacy Mode"
msgstr "Tryb prywatny"

#: core/kits/documents/kit.php:155
#: modules/history/views/revisions-panel-template.php:64
msgid "Published"
msgstr "Opublikowano"

#: includes/editor-templates/templates.php:248
msgid "Favorite"
msgstr "Ulubione"

#: includes/editor-templates/templates.php:200
msgid "Creation Date"
msgstr "Data utworzenia"

#: includes/editor-templates/templates.php:196
msgid "Created By"
msgstr "Utworzone przez"

#: includes/editor-templates/templates.php:149
#: assets/js/packages/editor-controls/editor-controls.js:68
#: assets/js/packages/editor-controls/editor-controls.strings.js:71
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:67
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:15
msgid "Search"
msgstr "Szukaj"

#: includes/editor-templates/templates.php:103
msgid "My Favorites"
msgstr "Moje ulubione"

#: includes/editor-templates/templates.php:81
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:4659
msgid "Popular"
msgstr "Popularne"

#: includes/editor-templates/templates.php:79
msgid "Trend"
msgstr "Modne"

#: includes/editor-templates/templates.php:14
msgid "Import Template"
msgstr "Importuj szablon"

#: includes/editor-templates/panel.php:127
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:24
msgid "Save Draft"
msgstr "Zapisz szkic"

#: core/admin/admin.php:428
msgid "Elementor Overview"
msgstr "Przegląd Elementora"

#: includes/widgets/alert.php:150 includes/widgets/icon-box.php:187
#: includes/widgets/image-box.php:162
msgid "Enter your description"
msgstr "Wprowadź swój opis"

#: includes/widgets/heading.php:193
msgid "Add Your Heading Text Here"
msgstr "Dodaj tu swój tekst nagłówka"

#. translators: 1: Saving date, 2: Author display name.
#: core/base/document.php:1539
msgid "Draft saved on %1$s by %2$s"
msgstr "Szkic zapisany %1$s przez %2$s"

#. translators: 1: Editing date, 2: Author display name.
#: core/base/document.php:1546
msgid "Last edited on %1$s by %2$s"
msgstr "Ostatnio edytowany %1$s przez %2$s"

#: core/kits/views/panel.php:40 includes/controls/icons.php:83
#: includes/controls/icons.php:85 includes/controls/media.php:215
#: includes/controls/media.php:217 includes/controls/media.php:279
#: includes/controls/media.php:281 includes/editor-templates/repeater.php:23
#: modules/promotions/widgets/pro-widget-promotion.php:79
#: assets/js/ai-admin.js:2309 assets/js/ai-admin.js:7478
#: assets/js/ai-gutenberg.js:2447 assets/js/ai-gutenberg.js:7696
#: assets/js/ai-layout.js:2966 assets/js/ai-media-library.js:2309
#: assets/js/ai-media-library.js:7478 assets/js/ai-unify-product-images.js:2309
#: assets/js/ai-unify-product-images.js:7478 assets/js/ai.js:3097
#: assets/js/ai.js:9030
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:588
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:184
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:70
msgid "Remove"
msgstr "Usuń"

#: includes/editor-templates/templates.php:545
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:1993
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:2335
msgid "or"
msgstr "lub"

#: includes/editor-templates/templates.php:544
msgid "Drag & drop your .JSON or .zip template file"
msgstr "Przeciągnij i upuść swój plik .JSON lub .zip z szablonem"

#: includes/widgets/traits/button-trait.php:57
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:50
#: assets/js/app.js:12874
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:2347
msgid "Click here"
msgstr "Kliknij tutaj"

#: core/admin/admin.php:524
msgid "Recently Edited"
msgstr "Ostatnio edytowany"

#: core/admin/admin.php:617
msgid "Blog"
msgstr "Blog"

#: includes/controls/groups/typography.php:190
msgctxt "Typography Control"
msgid "Underline"
msgstr "Podkreślenie"

#: includes/controls/groups/typography.php:185
msgctxt "Typography Control"
msgid "Decoration"
msgstr "Dekoracja"

#: includes/controls/dimensions.php:148
msgid "Unlinked values"
msgstr "Odłączone wartości"

#: includes/controls/groups/typography.php:192
msgctxt "Typography Control"
msgid "Line Through"
msgstr "Przekreślenie"

#: includes/widgets/alert.php:137
msgid "This is an Alert"
msgstr "To jest alert"

#: includes/editor-templates/templates.php:546 assets/js/app-packages.js:1034
#: assets/js/app.js:1499
msgid "Select File"
msgstr "Wybierz plik"

#: includes/editor-templates/panel.php:113
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:26
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:27
msgid "Save Options"
msgstr "Zapisz opcje"

#: includes/editor-templates/hotkeys.php:70
#: includes/editor-templates/repeater.php:18 assets/js/editor.js:30681
#: assets/js/editor.js:51404
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:66
#: assets/js/packages/editor-controls/editor-controls.strings.js:185
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:20
msgid "Duplicate"
msgstr "Powiel"

#: core/base/document.php:174 includes/editor-templates/panel.php:108
#: assets/js/editor.js:25710
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:28
msgid "Publish"
msgstr "Opublikuj"

#: core/document-types/page-base.php:247
msgid "Excerpt"
msgstr "Zajawka"

#: includes/editor-templates/panel.php:96
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:30
msgid "Preview Changes"
msgstr "Podejrzyj zmiany"

#: core/common/modules/finder/template.php:19 assets/js/editor.js:11018
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:1978
msgid "No Results Found"
msgstr "Nie znaleziono żadnego wyniku"

#: includes/editor-templates/panel.php:131
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:23
msgid "Save as Template"
msgstr "Zapisz jako szablon"

#: core/admin/admin.php:532
msgctxt "Dashboard Overview Widget Recently Date"
msgid "M jS"
msgstr "d M"

#: core/admin/admin.php:597
msgid "(opens in a new window)"
msgstr "(otwiera się w nowym oknie)"

#: includes/widgets/video.php:468
msgid "When you turn on privacy mode, YouTube/Vimeo won't store information about visitors on your website unless they play the video."
msgstr "Po włączeniu trybu prywatności, YouTube/Vimeo nie będzie zapisywał informacji o użytkownikach odwiedzających twoją stronę dopóki nie odtworzą filmu."

#: includes/editor-templates/repeater.php:12
msgid "Drag & Drop"
msgstr "Przeciągnij i upuść"

#: includes/editor-templates/panel.php:145
#: includes/editor-templates/panel.php:147 assets/js/editor.js:36943
msgid "Hide Panel"
msgstr "Ukryj panel"

#: includes/widgets/shortcode.php:110
msgid "Enter your shortcode"
msgstr "Wpisz swój krótki kod"

#: includes/widgets/image.php:178
msgid "Enter your image caption"
msgstr "Wpisz swój podpis obrazka"

#: includes/widgets/html.php:107
msgid "Enter your code"
msgstr "Wpisz swój kod"

#: includes/editor-templates/panel-elements.php:74
msgid "Search Widget:"
msgstr "Widżet wyszukiwania:"

#: core/experiments/manager.php:542 includes/controls/popover-toggle.php:71
#: includes/controls/popover-toggle.php:73
msgid "Back to default"
msgstr "Powrót do domyślnych"

#: includes/controls/groups/typography.php:191
msgctxt "Typography Control"
msgid "Overline"
msgstr "Nad kreślenie"

#: core/admin/admin.php:565
msgid "News & Updates"
msgstr "Aktualności i aktualizacje"

#: includes/editor-templates/templates.php:77
#: includes/editor-templates/templates.php:120
#: includes/editor-templates/templates.php:619
#: assets/js/atomic-widgets-editor.js:675
#: assets/js/atomic-widgets-editor.js:685 assets/js/editor.js:33535
#: assets/js/editor.js:34032
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:4655
msgid "New"
msgstr "Nowy"

#: includes/editor-templates/templates.php:543
msgid "Import Template to Your Library"
msgstr "Importuj szablon do swojej biblioteki"

#: includes/widgets/accordion.php:213 includes/widgets/toggle.php:216
#: modules/nested-tabs/widgets/nested-tabs.php:142
msgid "Active Icon"
msgstr "Aktywna ikonka"

#. translators: %s: Document title.
#: core/base/document.php:201
msgid "Hurray! Your %s is live."
msgstr "Hurra! Twój %s jest aktywny."

#: core/admin/admin.php:474
msgid "Create New Page"
msgstr "Utwórz nową stronę"

#: core/base/document.php:1533
msgctxt "revision date format"
msgid "M j, H:i"
msgstr "M d, G:m"

#: includes/template-library/sources/local.php:496
#: includes/template-library/sources/local.php:612
#: includes/template-library/sources/local.php:762
#: includes/template-library/sources/local.php:771
msgid "Access denied."
msgstr "Brak dostępu."

#: includes/settings/settings.php:288
msgid "Disable Default Fonts"
msgstr "Wyłącz domyślne kroje pisma"

#: includes/controls/groups/flex-container.php:105
#: includes/controls/groups/flex-container.php:141
#: includes/controls/groups/flex-container.php:218
#: includes/controls/groups/flex-item.php:63
#: includes/controls/groups/flex-item.php:89
#: includes/controls/groups/grid-container.php:143
#: includes/controls/groups/grid-container.php:171
#: includes/controls/groups/grid-container.php:200
#: includes/controls/groups/grid-container.php:240
#: includes/elements/column.php:218 includes/widgets/accordion.php:427
#: includes/widgets/common-base.php:526 includes/widgets/counter.php:292
#: includes/widgets/counter.php:326 includes/widgets/counter.php:400
#: includes/widgets/counter.php:436 includes/widgets/icon-list.php:584
#: includes/widgets/image-carousel.php:755 includes/widgets/rating.php:211
#: includes/widgets/tabs.php:217 includes/widgets/tabs.php:247
#: includes/widgets/toggle.php:451 includes/widgets/traits/button-trait.php:151
#: includes/widgets/traits/button-trait.php:296
#: modules/floating-buttons/base/widget-floating-bars-base.php:586
#: modules/floating-buttons/base/widget-floating-bars-base.php:1197
#: modules/floating-buttons/base/widget-floating-bars-base.php:1296
#: modules/nested-accordion/widgets/nested-accordion.php:186
#: modules/nested-accordion/widgets/nested-accordion.php:226
#: modules/nested-tabs/widgets/nested-tabs.php:247
#: modules/nested-tabs/widgets/nested-tabs.php:289
#: modules/nested-tabs/widgets/nested-tabs.php:359
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:97
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:164
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:200
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:205
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:210
msgid "End"
msgstr "Zakończenie"

#: includes/controls/groups/flex-container.php:97
#: includes/controls/groups/flex-container.php:133
#: includes/controls/groups/flex-container.php:210
#: includes/controls/groups/flex-item.php:55
#: includes/controls/groups/flex-item.php:85
#: includes/controls/groups/grid-container.php:135
#: includes/controls/groups/grid-container.php:163
#: includes/controls/groups/grid-container.php:192
#: includes/controls/groups/grid-container.php:232
#: includes/elements/column.php:216 includes/widgets/accordion.php:423
#: includes/widgets/common-base.php:518 includes/widgets/counter.php:288
#: includes/widgets/counter.php:318 includes/widgets/counter.php:392
#: includes/widgets/counter.php:428 includes/widgets/icon-list.php:576
#: includes/widgets/image-carousel.php:747 includes/widgets/rating.php:203
#: includes/widgets/tabs.php:209 includes/widgets/tabs.php:239
#: includes/widgets/toggle.php:447 includes/widgets/traits/button-trait.php:147
#: includes/widgets/traits/button-trait.php:288
#: modules/floating-buttons/base/widget-floating-bars-base.php:582
#: modules/floating-buttons/base/widget-floating-bars-base.php:1189
#: modules/floating-buttons/base/widget-floating-bars-base.php:1292
#: modules/nested-accordion/widgets/nested-accordion.php:178
#: modules/nested-accordion/widgets/nested-accordion.php:222
#: modules/nested-tabs/widgets/nested-tabs.php:239
#: modules/nested-tabs/widgets/nested-tabs.php:281
#: modules/nested-tabs/widgets/nested-tabs.php:351
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:95
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:162
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:198
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:203
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:208
msgid "Start"
msgstr "Początek"

#: core/debug/loading-inspection-manager.php:43
msgid "We’re sorry, but something went wrong. Click on 'Learn more' and follow each of the steps to quickly solve it."
msgstr "Przepraszamy, ale coś poszło nie tak. Kliknij „Dowiedz się więcej” i wykonaj wszystkie kroki, aby szybko rozwiązać problem."

#: core/debug/classes/inspection-base.php:25
#: core/debug/loading-inspection-manager.php:44
msgid "The preview could not be loaded"
msgstr "Nie można wczytać podglądu"

#: core/admin/admin-notices.php:147 core/admin/admin-notices.php:182
msgid "Update Notification"
msgstr "Powiadomienie o aktualizacji"

#. Plugin URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"

#. Author URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"

#: includes/editor-templates/hotkeys.php:137
#: includes/editor-templates/panel.php:90 assets/js/ai-admin.js:2058
#: assets/js/ai-gutenberg.js:2196 assets/js/ai-media-library.js:2058
#: assets/js/ai-unify-product-images.js:2058 assets/js/ai.js:2846
#: assets/js/editor.js:51726
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:17
msgid "History"
msgstr "Historia"

#: core/kits/documents/tabs/settings-lightbox.php:141
#: includes/widgets/video.php:905
msgid "UI Color"
msgstr "Kolor UI"

#: modules/history/views/history-panel-template.php:10
#: assets/js/editor.js:51134
msgid "Revisions"
msgstr "Wersje"

#: includes/editor-templates/hotkeys.php:16
#: includes/editor-templates/templates.php:203
#: modules/history/views/history-panel-template.php:9 assets/js/editor.js:51131
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:1196
msgid "Actions"
msgstr "Działania"

#: modules/history/views/history-panel-template.php:24
msgid "No History Yet"
msgstr "Jeszcze nie ma historii"

#: modules/history/views/history-panel-template.php:17
msgid "Switch to Revisions tab for older versions"
msgstr "Przejdź do karty Wersje, aby zobaczyć starsze wersje"

#: core/kits/documents/tabs/settings-lightbox.php:152
#: includes/widgets/video.php:917
msgid "UI Hover Color"
msgstr "Kolor najechania UI"

#: modules/history/views/history-panel-template.php:25
msgid "Once you start working, you'll be able to redo / undo any action you make in the editor."
msgstr "Po rozpoczęciu pracy będziesz mieć możliwość ponowienia/cofnięcia dowolnej czynności wykonanej w edytorze."

#: includes/widgets/video.php:388
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:81
msgid "Mute"
msgstr "Wycisz"

#: includes/template-library/sources/local.php:1018
msgid "Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library."
msgstr "Wybierz plik JSON lub archiwum .zip z motywem Elementora i dodaj go do listy motywów dostępnych w Twojej bibliotece."

#: core/kits/documents/tabs/settings-lightbox.php:48
msgid "Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file."
msgstr "Otwieraj wszystkie podlinkowane zdjęcia z efektem wyskakującego okna lightbox. Lightbox zostanie automatycznie uruchomiony w każdym linku prowadzącym do zdjęcia."

#: core/kits/documents/tabs/settings-lightbox.php:45
msgid "Image Lightbox"
msgstr "Lightbox obrazka"

#. translators: %s: Singular label.
#. Translators: %s: Element Name.
#. Translators: %s: Element name.
#. translators: %s: Element type title.
#: core/document-types/page-base.php:194 assets/js/atomic-widgets-editor.js:852
#: assets/js/editor.js:30475 assets/js/editor.js:30668
#: assets/js/editor.js:33108 assets/js/editor.js:33583
#: assets/js/editor.js:33684 assets/js/editor.js:34010
#: assets/js/editor.js:37536
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:19
msgid "Edit %s"
msgstr "Edytuj %s"

#: includes/settings/tools.php:420
msgid "Please Note: We do not recommend updating to a beta version on production sites."
msgstr "Uwaga: Nie rekomendujemy aktualizacji do wersji beta na działających stronach."

#: includes/settings/tools.php:389
msgid "Warning: Please backup your database before making the rollback."
msgstr "Ostrzeżenie: przed wczytaniem poprzedniej wersji zrób kopię bazy danych."

#: core/common/modules/finder/categories/settings.php:54
#: includes/settings/settings.php:304
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:5
msgid "Integrations"
msgstr "Integracje"

#: includes/elements/column.php:797 includes/elements/container.php:1756
#: includes/elements/section.php:1258 includes/widgets/common-base.php:773
msgid "Z-Index"
msgstr "Z-Index"

#: includes/controls/url.php:116
msgid "Add nofollow"
msgstr "Dodaj nofollow"

#: includes/controls/url.php:112
msgid "Open in new window"
msgstr "Otwórz w nowym oknie"

#. translators: %s: Elementor version.
#: includes/settings/tools.php:372
msgid "Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared."
msgstr "Pojawił się jakiś problem w Elementorze w wersji %s? Wróć do wersji wcześniejszej sprzed wystąpienia problemu."

#: core/kits/documents/tabs/theme-style-form-fields.php:137
#: core/kits/documents/tabs/theme-style-images.php:203
#: includes/base/element-base.php:1264
#: includes/controls/groups/background.php:673 includes/elements/column.php:361
#: includes/elements/column.php:521 includes/elements/column.php:629
#: includes/elements/container.php:735 includes/elements/container.php:949
#: includes/elements/container.php:1109 includes/elements/section.php:617
#: includes/elements/section.php:804 includes/elements/section.php:911
#: includes/widgets/alert.php:446 includes/widgets/common-base.php:933
#: includes/widgets/common-base.php:1048 includes/widgets/google-maps.php:252
#: includes/widgets/heading.php:400 includes/widgets/icon-box.php:486
#: includes/widgets/icon-box.php:721 includes/widgets/icon-list.php:470
#: includes/widgets/icon-list.php:697 includes/widgets/image-box.php:557
#: includes/widgets/image-box.php:685 includes/widgets/image.php:501
#: includes/widgets/text-editor.php:389
#: includes/widgets/traits/button-trait.php:451
#: modules/floating-buttons/base/widget-contact-button-base.php:1494
#: modules/floating-buttons/base/widget-contact-button-base.php:2233
#: modules/nested-tabs/widgets/nested-tabs.php:614
#: modules/shapes/widgets/text-path.php:460
#: modules/shapes/widgets/text-path.php:638
msgid "Transition Duration"
msgstr "Czas trwania przejścia"

#: includes/controls/groups/box-shadow.php:72
msgctxt "Box Shadow Control"
msgid "Outline"
msgstr "Kontur"

#: includes/settings/tools.php:381
msgid "Rollback Version"
msgstr "Przywrócenie wersji"

#: core/common/modules/finder/categories/tools.php:77
#: includes/settings/tools.php:365
msgid "Version Control"
msgstr "Kontrola wersji"

#: includes/elements/column.php:906 includes/elements/container.php:1852
#: includes/elements/section.php:1347 includes/widgets/common-base.php:867
#: modules/floating-buttons/base/widget-contact-button-base.php:1417
#: modules/floating-buttons/base/widget-floating-bars-base.php:916
msgid "Animation Delay"
msgstr "Opóźnienie animacji"

#: includes/controls/url.php:103
msgid "Link Options"
msgstr "Opcje odnośnika"

#: includes/settings/tools.php:396
msgid "Become a Beta Tester"
msgstr "Zostań beta testerem"

#: includes/settings/tools.php:399
msgid "Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it."
msgstr "Włącz Beta Testy, aby otrzymywać powiadomienia o nowych wersjach beta Elementora lub Elementora Pro, kiedy tylko będą dostępne. Wersja Beta nie zainstaluje się automatycznie. Zawsze będziesz możesz ją pominąć."

#: includes/rollback.php:165 includes/settings/tools.php:193
#: includes/settings/tools.php:368 assets/js/admin.js:2290
msgid "Rollback to Previous Version"
msgstr "Przywróć poprzednią wersję"

#: core/kits/documents/tabs/settings-layout.php:118
msgid "Sets the default space between widgets (Default: 20px)"
msgstr "Ustaw domyślne odstępy pomiędzy widżetami (Domyślny: 20px)"

#: includes/settings/tools.php:412
msgid "Beta Tester"
msgstr "Tester wersji beta"

#: includes/settings/settings.php:341
msgid "Switch Editor Loader Method"
msgstr "Przełącz metodę wczytywania edytora"

#: core/kits/documents/tabs/settings-layout.php:101
#: includes/elements/column.php:237
msgid "Widgets Space"
msgstr "Odstępy widżetów"

#: includes/settings/settings.php:413
msgid "CSS Print Method"
msgstr "Sposób osadzenia CSS"

#: includes/settings/settings.php:420
msgid "Internal Embedding"
msgstr "Osadzenie wewnętrzne"

#: includes/settings/settings.php:419
msgid "External File"
msgstr "Plik zewnętrzny"

#: core/debug/inspector.php:55 includes/settings/settings.php:347
#: includes/settings/settings.php:359 includes/settings/settings.php:370
#: includes/settings/settings.php:435 includes/settings/settings.php:452
#: includes/settings/settings.php:464 includes/settings/settings.php:476
#: includes/settings/tools.php:418 modules/generator-tag/module.php:81
#: modules/nested-tabs/widgets/nested-tabs.php:387
#: modules/safe-mode/module.php:48 assets/js/admin.js:294
#: assets/js/ai-admin.js:64 assets/js/ai-gutenberg.js:64
#: assets/js/ai-media-library.js:64 assets/js/ai-unify-product-images.js:64
#: assets/js/ai.js:64 assets/js/app-packages.js:1361 assets/js/app.js:1826
#: assets/js/common.js:64 assets/js/editor.js:40443
#: assets/js/packages/editor-controls/editor-controls.js:58
#: assets/js/packages/editor-controls/editor-controls.strings.js:91
msgid "Enable"
msgstr "Włącz"

#: core/debug/inspector.php:54 includes/settings/settings.php:346
#: includes/settings/settings.php:358 includes/settings/settings.php:371
#: includes/settings/settings.php:436 includes/settings/settings.php:453
#: includes/settings/settings.php:465 includes/settings/settings.php:477
#: includes/settings/tools.php:417 modules/element-cache/module.php:130
#: modules/generator-tag/module.php:82
#: modules/nested-tabs/widgets/nested-tabs.php:386
#: modules/safe-mode/module.php:47
msgid "Disable"
msgstr "Wyłącz"

#: core/base/document.php:1993 modules/ai/preferences.php:67
#: modules/pro-install/pro-install-menu-item.php:92
#: assets/js/element-manager-admin.js:655
#: assets/js/element-manager-admin.js:732
msgid "Status"
msgstr "Status"

#: includes/settings/settings.php:349
msgid "For troubleshooting server configuration conflicts."
msgstr "Przy rozwiązywaniu problemów z konfliktami konfiguracji serwera."

#: includes/maintenance-mode.php:206
msgid "Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched."
msgstr "Przełącz całą stronę w tryb PRZERWY TECHNICZNEJ. Oznaczać to będzie, że cała strona z przyczyn technicznych jest tymczasowo offline, lub ustaw tryb WRACAMY WKRÓTCE, oznaczający, że strona jest offline do czasu, kiedy będzie gotowa do uruchomienia."

#: includes/maintenance-mode.php:243
msgid "Roles"
msgstr "Role"

#: includes/maintenance-mode.php:231
msgid "Who Can Access"
msgstr "Kto ma dostęp"

#: includes/widgets/common-base.php:1290 includes/widgets/spacer.php:130
#: includes/widgets/text-editor.php:503
msgid "Space"
msgstr "Odstęp"

#: core/kits/documents/tabs/settings-layout.php:143
msgid "Elementor lets you hide the page title. This works for themes that have \"h1.entry-title\" selector. If your theme's selector is different, please enter it above."
msgstr "Elementor pozwala na ukrycie tytułu strony. Działa to w motywach, które stosują selektor \"h1.entry-title\". Jeśli selektor w Twoim motywie jest inny, wpisz go powyżej."

#: includes/admin-templates/new-template.php:75
#: includes/settings/controls.php:155
msgid "Select"
msgstr "Wybierz"

#: core/common/modules/finder/categories/edit.php:118 assets/js/editor.js:10659
#: assets/js/editor.js:19358
msgid "Template"
msgstr "Szablon"

#: includes/widgets/text-editor.php:148 includes/widgets/text-editor.php:410
msgid "Drop Cap"
msgstr "Inicjał"

#: core/kits/documents/tabs/settings-layout.php:139
msgid "Page Title Selector"
msgstr "Selektor tytułu strony"

#: core/document-types/page-base.php:96
msgid "Hide Title"
msgstr "Ukryj tytuł"

#: includes/maintenance-mode.php:210
msgid "Choose Mode"
msgstr "Wybierz tryb"

#: core/kits/documents/kit.php:154 includes/maintenance-mode.php:215
#: assets/js/editor.js:51403
msgid "Disabled"
msgstr "Wyłączono"

#: includes/maintenance-mode.php:217
msgid "Maintenance"
msgstr "Konserwacja"

#: includes/maintenance-mode.php:292 includes/maintenance-mode.php:369
#: includes/template-library/sources/local.php:231 assets/js/app.js:14669
msgid "Edit Template"
msgstr "Edytuj szablon"

#: includes/maintenance-mode.php:216
msgid "Coming Soon"
msgstr "Wkrótce"

#: includes/maintenance-mode.php:237
msgid "Logged In"
msgstr "Zalogowano"

#: core/common/modules/finder/categories/tools.php:62
#: includes/maintenance-mode.php:201 includes/maintenance-mode.php:205
msgid "Maintenance Mode"
msgstr "Tryb konserwacji"

#: includes/maintenance-mode.php:226
msgid "Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed."
msgstr "Opcja Wkrótce zwróci kod HTTP 200, co oznacza, że ​​witryna jest gotowa do indeksowania."

#: includes/maintenance-mode.php:223
msgid "Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days."
msgstr "Tryb konserwacji zwraca kod HTTP 503, aby wyszukiwarki wiedziały, że mają wrócić za chwilę. Nie zaleca się korzystania z tego trybu dłużej niż kilka dni."

#: includes/maintenance-mode.php:220
msgid "Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code)."
msgstr "Wybierz tryb Wkrótce (zwracanie kodu HTTP 200) lub tryb konserwacji (zwracanie kodu HTTP 503)."

#: includes/editor-templates/hotkeys.php:54
msgid "Paste Style"
msgstr "Wklej styl"

#: core/settings/editor-preferences/model.php:107
msgid "Canvas"
msgstr "Płótno"

#: includes/maintenance-mode.php:283
msgid "Maintenance Mode ON"
msgstr "Tryb konserwacji WŁĄCZONY"

#: includes/maintenance-mode.php:251
msgid "Choose Template"
msgstr "Wybierz szablon"

#: includes/maintenance-mode.php:370
msgid "To enable maintenance mode you have to set a template for the maintenance mode page."
msgstr "Aby włączyć tryb konserwacji, musisz ustawić szablon dla strony trybu konserwacji."

#: includes/elements/container.php:1328 includes/elements/section.php:1109
msgid "Bring to Front"
msgstr "Przenieś na wierzch"

#: includes/shapes.php:137
msgctxt "Shapes"
msgid "Mountains"
msgstr "Góry"

#: includes/shapes.php:149
msgctxt "Shapes"
msgid "Clouds"
msgstr "Chmury"

#: includes/shapes.php:156 includes/widgets/divider.php:189
#: includes/widgets/divider.php:319
msgctxt "Shapes"
msgid "Zigzag"
msgstr "Zygzak"

#: includes/shapes.php:166
msgctxt "Shapes"
msgid "Triangle"
msgstr "Trójkąt"

#: includes/shapes.php:192
msgctxt "Shapes"
msgid "Curve"
msgstr "Krzywa"

#: core/kits/documents/tabs/settings-lightbox.php:18
#: includes/widgets/image-carousel.php:387
#: includes/widgets/image-gallery.php:200 includes/widgets/image.php:225
#: includes/widgets/video.php:733 includes/widgets/video.php:881
msgid "Lightbox"
msgstr "Lightbox"

#: includes/widgets/icon-list.php:230
msgid "List"
msgstr "Lista"

#: includes/shapes.php:219
msgctxt "Shapes"
msgid "Book"
msgstr "Książka"

#: includes/shapes.php:229
msgctxt "Shapes"
msgid "Arrow"
msgstr "Strzałka"

#: includes/shapes.php:203
msgctxt "Shapes"
msgid "Waves"
msgstr "Fale"

#: includes/elements/container.php:1315 includes/elements/section.php:1096
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:131
msgid "Invert"
msgstr "Odwróć"

#: includes/shapes.php:142
msgctxt "Shapes"
msgid "Drops"
msgstr "Krople"

#: includes/elements/container.php:1301 includes/elements/section.php:1082
msgid "Flip"
msgstr "Odwróć"

#: includes/shapes.php:197
msgctxt "Shapes"
msgid "Curve Asymmetrical"
msgstr "Krzywa asymetryczna"

#: includes/shapes.php:171
msgctxt "Shapes"
msgid "Triangle Asymmetrical"
msgstr "Trójkąt asymetryczny"

#: includes/shapes.php:160
msgctxt "Shapes"
msgid "Pyramids"
msgstr "Ostrosłupy"

#: includes/shapes.php:183
msgctxt "Shapes"
msgid "Tilt Opacity"
msgstr "Krycie pochylenia"

#: includes/controls/groups/flex-container.php:109
#: includes/controls/groups/flex-container.php:222
#: includes/controls/groups/grid-container.php:204
#: includes/controls/groups/grid-container.php:244
#: includes/elements/column.php:191 includes/elements/column.php:219
#: includes/elements/section.php:428 includes/widgets/icon-list.php:238
#: includes/widgets/toggle.php:317
msgid "Space Between"
msgstr "Odstęp pomiędzy"

#: includes/shapes.php:188
msgctxt "Shapes"
msgid "Fan Opacity"
msgstr "Krycie wachlarza"

#: includes/elements/container.php:1175 includes/elements/section.php:956
msgid "Shape Divider"
msgstr "Kształt rozdzielacza"

#: includes/shapes.php:214
msgctxt "Shapes"
msgid "Waves Pattern"
msgstr "Wzorzec fal"

#: includes/shapes.php:209
msgctxt "Shapes"
msgid "Waves Brush"
msgstr "Pędzel fal"

#: includes/shapes.php:177
msgctxt "Shapes"
msgid "Tilt"
msgstr "Pochylenie"

#: includes/shapes.php:224
msgctxt "Shapes"
msgid "Split"
msgstr "Podział"

#: includes/widgets/tabs.php:275
msgid "Navigation Width"
msgstr "Szerokość nawigacji"

#: includes/elements/column.php:818 includes/elements/container.php:1777
#: includes/elements/section.php:1279 includes/widgets/common-base.php:793
#: includes/widgets/traits/button-trait.php:214
#: modules/floating-buttons/base/widget-contact-button-base.php:3099
#: modules/floating-buttons/base/widget-floating-bars-base.php:1505
#: modules/nested-accordion/widgets/nested-accordion.php:143
#: modules/nested-tabs/widgets/nested-tabs.php:165
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr "Dodaj własny identyfikator BEZ klucza Pound. Np. my-id"

#: includes/elements/column.php:809 includes/elements/container.php:1768
#: includes/elements/section.php:1270 includes/widgets/common-base.php:784
#: modules/floating-buttons/base/widget-contact-button-base.php:3090
#: modules/floating-buttons/base/widget-floating-bars-base.php:1496
#: modules/nested-accordion/widgets/nested-accordion.php:134
#: modules/nested-tabs/widgets/nested-tabs.php:156
msgid "CSS ID"
msgstr "Identyfikator CSS"

#: core/kits/documents/tabs/theme-style-buttons.php:166
#: core/kits/documents/tabs/theme-style-images.php:134
#: core/kits/documents/tabs/theme-style-typography.php:148
#: includes/base/element-base.php:883 includes/elements/column.php:346
#: includes/elements/column.php:475 includes/elements/column.php:594
#: includes/elements/container.php:720 includes/elements/container.php:901
#: includes/elements/container.php:1062 includes/elements/section.php:602
#: includes/elements/section.php:758 includes/elements/section.php:876
#: includes/widgets/alert.php:429 includes/widgets/common-base.php:918
#: includes/widgets/common-base.php:1013 includes/widgets/google-maps.php:237
#: includes/widgets/heading.php:382 includes/widgets/icon-box.php:444
#: includes/widgets/icon-box.php:698 includes/widgets/icon-list.php:450
#: includes/widgets/icon-list.php:678 includes/widgets/icon.php:256
#: includes/widgets/image-box.php:524 includes/widgets/image-box.php:662
#: includes/widgets/image.php:468 includes/widgets/text-editor.php:371
#: includes/widgets/traits/button-trait.php:393
#: modules/floating-buttons/base/widget-contact-button-base.php:1253
#: modules/floating-buttons/base/widget-contact-button-base.php:2012
#: modules/floating-buttons/base/widget-contact-button-base.php:2494
#: modules/floating-buttons/base/widget-contact-button-base.php:2674
#: modules/floating-buttons/base/widget-floating-bars-base.php:690
#: modules/floating-buttons/base/widget-floating-bars-base.php:1394
#: modules/nested-accordion/widgets/nested-accordion.php:668
#: modules/nested-accordion/widgets/nested-accordion.php:731
#: modules/nested-tabs/widgets/nested-tabs.php:550
#: modules/nested-tabs/widgets/nested-tabs.php:778
#: modules/nested-tabs/widgets/nested-tabs.php:959
#: modules/shapes/widgets/text-path.php:433
#: modules/shapes/widgets/text-path.php:572
msgid "Hover"
msgstr "Najechanie"

#: includes/controls/groups/background.php:230
msgctxt "Background Control"
msgid "Type"
msgstr "Rodzaj"

#: includes/controls/groups/background.php:184
#: includes/controls/groups/background.php:213
msgctxt "Background Control"
msgid "Location"
msgstr "Lokalizacja"

#: core/admin/admin.php:377
msgid "Docs & FAQs"
msgstr "Dokumentacja i FAQ"

#: core/admin/admin.php:377
msgid "View Elementor Documentation"
msgstr "Przejdź do dokumentacji Elementora"

#: includes/settings/settings.php:292
msgid "Checking this box will disable Elementor's Default Fonts, and make Elementor inherit the fonts from your theme."
msgstr "Zaznaczenie tego pola spowoduje wyłączenie domyślnych krojów pisma Elementora i spowoduje, że Elementor odziedziczy kroje pisma z Twojego motywu."

#: includes/settings/settings.php:284
msgid "Checking this box will disable Elementor's Default Colors, and make Elementor inherit the colors from your theme."
msgstr "Zaznaczenie tego pola spowoduje wyłączenie domyślnych kolorów Elementora i spowoduje, że Elementor odziedziczy kolory z Twojego motywu."

#: core/admin/admin.php:378
msgid "View Elementor Video Tutorials"
msgstr "Zobacz poradniki filmowe Elementora"

#: core/admin/admin.php:378
msgid "Video Tutorials"
msgstr "Poradniki filmowe"

#. translators: 1: Human readable time difference, 2: Date.
#: modules/history/revisions-manager.php:179
msgid "%1$s ago (%2$s)"
msgstr "%1$s temu (%2$s)"

#: includes/settings/tools.php:352
msgid "Update Site Address (URL)"
msgstr "Uaktualnij adres witryny (URL)"

#: core/common/modules/finder/categories/tools.php:56
#: includes/settings/tools.php:334 includes/settings/tools.php:338
#: includes/settings/tools.php:355
msgid "Replace URL"
msgstr "Zastąp adres URL"

#: modules/history/revisions-manager.php:163
msgid "Revision"
msgstr "Wersja"

#: modules/history/views/revisions-panel-template.php:39
msgid "No Revisions Saved Yet"
msgstr "Nie zapisano jeszcze żadnej wersji"

#: modules/history/views/revisions-panel-template.php:27
msgid "Start designing your page and you will be able to see the entire revision history here."
msgstr "Rozpocznij projektowanie swojej strony, a zobaczysz tutaj całą historię wersji."

#: modules/history/views/revisions-panel-template.php:28
msgid "It looks like the post revision feature is unavailable in your website."
msgstr "Wygląda na to, że funkcja wersji wpisów nie jest dostępna w witrynie."

#: modules/history/views/revisions-panel-template.php:26
msgid "Revision history lets you save your previous versions of your work, and restore them any time."
msgstr "Historia wersji pozwala na zapisanie wcześniejszych wersji twojej pracy i przywrócenie ich w dowolnym momencie."

#. translators: 1: Minimum recommended_memory, 2: Preferred memory, 3:
#. WordPress wp-config memory documentation.
#: modules/system-info/reporters/server.php:170
msgid "We recommend setting memory to at least %1$s. (%2$s or higher is preferred) For more information, read about <a href=\"%3$s\">how to increase memory allocated to PHP</a>."
msgstr "Zalecamy ustawienie pamięci na co najmniej %1$s. (%2$s lub więcej jest zalecane) By uzyskać więcej informacji, przeczytaj <a href=\"%3$s\">\"jak zwiększyć pamięć przydzieloną dla PHP\"</a>."

#: modules/apps/admin-apps-page.php:172
#: modules/history/views/revisions-panel-template.php:55
msgid "By"
msgstr "Autor"

#: includes/settings/tools.php:356
msgid "Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to 'HTTPS')."
msgstr "Wpisz stary i nowy adres URL instalacji WordPress, aby zaktualizować wszystkie dane Elementora (dotyczy transferu domen lub przejścia na protokół „HTTPS”)."

#: modules/history/revisions-manager.php:160
msgid "Autosave"
msgstr "Automatyczne zapisywanie"

#: modules/history/revisions-manager.php:151
msgctxt "revision date format"
msgid "M j @ H:i"
msgstr "M r @ G:m"

#: includes/widgets/counter.php:199
msgid "Thousand Separator"
msgstr "Separator tysięczny"

#: includes/managers/controls.php:1081
msgid "Custom CSS lets you add CSS code to any widget, and see it render live right in the editor."
msgstr "Własny CSS pozwala na dodanie kodu CSS do każdego widget'a i sprawdzenie ich na żywo w edytorze."

#: includes/editor-templates/panel-elements.php:102
msgid "With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place."
msgstr "Z tą opcją możesz zapisać widget jako globalny i dodawać go w wielu obszarach. Wszystkie te obszary będą dostępne do edycji z jednego miejsca."

#: core/kits/documents/tabs/settings-custom-css.php:17
#: includes/managers/controls.php:1075
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:17
msgid "Custom CSS"
msgstr "Własny CSS"

#: includes/managers/controls.php:1093
msgid "Meet Our Custom CSS"
msgstr "Poznaj nasz własny CSS"

#: includes/base/element-base.php:927 includes/base/element-base.php:1091
#: includes/widgets/common-base.php:1092 includes/widgets/icon-list.php:284
#: includes/widgets/icon.php:329 includes/widgets/text-editor.php:150
#: includes/widgets/video.php:736 modules/shapes/widgets/text-path.php:220
msgid "Off"
msgstr "Wyłącz"

#: includes/base/element-base.php:926 includes/base/element-base.php:1090
#: includes/widgets/common-base.php:1091 includes/widgets/icon-list.php:285
#: includes/widgets/icon.php:330 includes/widgets/text-editor.php:151
#: includes/widgets/video.php:737 modules/shapes/widgets/text-path.php:219
msgid "On"
msgstr "Włącz"

#: includes/editor-templates/panel-elements.php:101
msgid "Meet Our Global Widget"
msgstr "Poznaj nasz globalny widżet"

#: modules/promotions/widgets/pro-widget-promotion.php:80
#: assets/js/ai-admin.js:7918 assets/js/ai-gutenberg.js:8136
#: assets/js/ai-layout.js:3406 assets/js/ai-media-library.js:7918
#: assets/js/ai-unify-product-images.js:7918 assets/js/ai.js:9470
msgid "Go Pro"
msgstr "Przejdź na Pro"

#: includes/editor-templates/panel-elements.php:28
msgid "Get more with Elementor Pro"
msgstr "Zyskaj więcej dzięki Elementor Pro"

#: includes/base/widget-base.php:312 includes/base/widget-base.php:321
msgid "Skin"
msgstr "Skórka"

#: includes/widgets/traits/button-trait.php:38
msgid "Extra Large"
msgstr "Bardzo duży"

#: includes/widgets/traits/button-trait.php:34
msgid "Extra Small"
msgstr "Bardzo mały"

#: includes/settings/settings.php:298
msgid "Improve Elementor"
msgstr "Ulepsz Elementora"

#: includes/frontend.php:1228
msgid "Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one."
msgstr "Nieprawidłowe dane: Identyfikator szablonu nie może być taki sam jak identyfikator aktualnie edytowanego szablonu. Wybierz inny."

#: includes/editor-templates/panel.php:167
msgid "Update changes to page"
msgstr "Aktualizuj zmiany na stronie"

#: includes/editor-templates/panel.php:203
msgid "%s are disabled"
msgstr "%s są wyłączone"

#: core/admin/admin-notices.php:243
msgid "No thanks"
msgstr "Nie, dziękuję"

#: core/kits/documents/tabs/settings-layout.php:161
msgid "Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width."
msgstr "Wpisz selektor elementu nadrzędnego, do którego będą pasować rozciągnięte sekcje (np. #primary / .wrapper / main itp.). Pozostaw puste, aby dopasować do szerokości strony."

#: core/kits/documents/tabs/settings-layout.php:158
msgid "Stretched Section Fit To"
msgstr "Rozciągnięta sekcja dopasowana do"

#: core/kits/documents/tabs/settings-layout.php:74
msgid "Sets the default width of the content area (Default: 1140px)"
msgstr "Ustawia domyślną szerokość obszaru treści (domyślnie: 1140 px)"

#: includes/elements/section.php:472
msgid "Stretch the section to the full width of the page using JS."
msgstr "Rozciągnij sekcję na całą szerokość strony za pomocą JS."

#: includes/elements/section.php:464
msgid "Stretch Section"
msgstr "Sekcja rozciągania"

#: core/admin/admin-notices.php:231
msgid "Learn more."
msgstr "Dowiedz się więcej."

#: includes/elements/section.php:1378
msgid "Reverse Columns"
msgstr "Odwróć kolumny"

#: includes/controls/dimensions.php:141 includes/controls/dimensions.php:144
msgid "Link values together"
msgstr "Połącz wartości odnośników"

#: includes/widgets/shortcode.php:42 includes/widgets/shortcode.php:103
msgid "Shortcode"
msgstr "Krótki kod"

#: core/common/modules/connect/apps/library.php:16 assets/js/editor.js:9660
#: assets/js/import-export-admin.js:300
msgid "Library"
msgstr "Biblioteka"

#: core/base/document.php:173 includes/editor-templates/global.php:21
#: includes/editor-templates/responsive-bar.php:65 includes/frontend.php:1388
#: assets/js/152f977e0c1304a3b0db.bundle.js:208 assets/js/ai-admin.js:586
#: assets/js/ai-gutenberg.js:724 assets/js/ai-layout.js:418
#: assets/js/ai-media-library.js:586 assets/js/ai-unify-product-images.js:586
#: assets/js/ai.js:1374 assets/js/app-packages.js:526
#: assets/js/app-packages.js:2579 assets/js/app-packages.js:3138
#: assets/js/app.js:697 assets/js/app.js:2956 assets/js/app.js:3379
#: assets/js/app.js:7319 assets/js/app.js:9804 assets/js/app.js:16368
#: assets/js/editor.js:47942 assets/js/import-export-admin.js:313
msgid "Close"
msgstr "Zamknij"

#: core/document-types/page.php:58 modules/library/documents/page.php:57
#: assets/js/editor.js:8495
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:62
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:2
msgid "Page"
msgstr "Strona"

#: includes/editor-templates/templates.php:295
#: includes/editor-templates/templates.php:377
#: includes/editor-templates/templates.php:419
#: includes/editor-templates/templates.php:433 assets/js/ai-admin.js:6712
#: assets/js/ai-gutenberg.js:6930 assets/js/ai-media-library.js:6712
#: assets/js/ai-unify-product-images.js:6712 assets/js/ai.js:8264
#: assets/js/editor.js:6430
msgid "Insert"
msgstr "Wstaw"

#: includes/template-library/sources/local.php:228
msgctxt "Template Library"
msgid "Template"
msgstr "Szablon"

#: includes/template-library/sources/local.php:209
msgid "Local"
msgstr "Lokalne"

#: core/common/modules/finder/categories/tools.php:28
#: core/common/modules/finder/categories/tools.php:50
#: includes/settings/admin-menu-items/tools-menu-item.php:29
#: includes/settings/admin-menu-items/tools-menu-item.php:33
#: includes/settings/tools.php:32 includes/settings/tools.php:33
#: includes/settings/tools.php:453
msgid "Tools"
msgstr "Narzędzia"

#: includes/template-library/sources/remote.php:51
msgid "Remote"
msgstr "Zdalne"

#: includes/template-library/sources/local.php:1025
msgid "Import Now"
msgstr "Importuj"

#: includes/template-library/sources/cloud.php:106
#: includes/template-library/sources/cloud.php:327
#: includes/template-library/sources/local.php:500
msgid "(no title)"
msgstr "(brak tytułu)"

#: includes/template-library/sources/local.php:280
msgctxt "Template Library"
msgid "Type"
msgstr "Rodzaj"

#: core/common/modules/finder/categories/general.php:49
#: includes/template-library/sources/admin-menu-items/saved-templates-menu-item.php:23
#: includes/template-library/sources/local.php:1731 assets/js/app.js:10363
#: assets/js/app.js:15435
msgid "Saved Templates"
msgstr "Zapisane szablony"

#: includes/editor-templates/hotkeys.php:174
#: includes/editor-templates/templates.php:534
#: includes/editor-templates/templates.php:550
#: includes/editor-templates/templates.php:564
msgid "Template Library"
msgstr "Biblioteka szablonów"

#: includes/editor-templates/templates.php:213
msgid "Stay tuned! More awesome templates coming real soon."
msgstr "Zachowaj czujność! Wkrótce pojawi się więcej niesamowitych szablonów."

#: includes/editor-templates/global.php:45
msgid "Add Template"
msgstr "Dodaj szablon"

#: includes/editor-templates/templates.php:464
msgid "Enter Template Name"
msgstr "Wpisz nazwę szablonu"

#: app/modules/import-export-customization/module.php:140
#: app/modules/import-export/module.php:145
#: includes/editor-templates/templates.php:316
#: includes/editor-templates/templates.php:347
#: includes/editor-templates/templates.php:400
#: includes/template-library/sources/local.php:1203 assets/js/app.js:5717
#: assets/js/app.js:5891 assets/js/app.js:5966 assets/js/app.js:9767
#: assets/js/app.js:16528
msgid "Export"
msgstr "Eksportuj"

#: includes/editor-templates/templates.php:41
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:1525
msgid "Back to Library"
msgstr "Wróć do biblioteki"

#: includes/template-library/sources/local.php:1016
msgid "Import Templates"
msgstr "Importuj szablony"

#: includes/template-library/sources/local.php:988
msgid "Export Template"
msgstr "Eksportuj szablon"

#: includes/settings/tools.php:326
msgid "Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button."
msgstr "Biblioteka Elementora aktualizuje się automatycznie każdego dnia. Możesz ją również zaktualizować ręcznie, klikając przycisk synchronizacji."

#: includes/editor-templates/templates.php:19 includes/settings/tools.php:322
#: includes/settings/tools.php:325
msgid "Sync Library"
msgstr "Synchronizuj bibliotekę"

#. translators: %s: WordPress child themes documentation.
#: modules/system-info/reporters/theme.php:207
msgid "If you want to modify the source code of your theme, we recommend using a <a href=\"%s\">child theme</a>."
msgstr "Jeśli chcesz zmodyfikować kod źródłowy swojego motywu, zalecamy użycie <a href=\"%s\">motywu potomnego</a>."

#: core/admin/admin-notices.php:371 modules/apps/admin-apps-page.php:182
#: modules/safe-mode/module.php:359 modules/safe-mode/module.php:368
#: modules/safe-mode/mu-plugin/elementor-safe-mode.php:105
#: assets/js/app-packages.js:1232 assets/js/app-packages.js:4571
#: assets/js/app-packages.js:4679 assets/js/app.js:1697 assets/js/app.js:4807
#: assets/js/app.js:6253 assets/js/app.js:11919 assets/js/app.js:12240
#: assets/js/app.js:13287 assets/js/app.js:14824 assets/js/app.js:15154
#: assets/js/app.js:15200 assets/js/app.js:16367 assets/js/editor.js:15056
#: assets/js/editor.js:28573 assets/js/editor.js:28604
#: assets/js/editor.js:41162 assets/js/element-manager-admin.js:542
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:3915
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:4690
#: assets/js/packages/editor-controls/editor-controls.js:68
#: assets/js/packages/editor-controls/editor-controls.strings.js:57
msgid "Learn More"
msgstr "Dowiedz się więcej"

#: core/kits/documents/tabs/global-typography.php:28
#: core/kits/documents/tabs/global-typography.php:47 assets/js/app.js:10375
#: assets/js/app.js:15453 assets/js/editor.js:47693
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:222
msgid "Global Fonts"
msgstr "Globalne kroje pisma"

#: modules/floating-buttons/base/widget-contact-button-base.php:2216
#: assets/js/ai-admin.js:3600 assets/js/ai-gutenberg.js:3738
#: assets/js/ai-media-library.js:3600 assets/js/ai-unify-product-images.js:3600
#: assets/js/ai.js:4421
msgid "Animation"
msgstr "Animacja"

#: core/base/traits/shared-widget-controls-trait.php:289
#: includes/widgets/icon-box.php:501 includes/widgets/icon.php:294
#: includes/widgets/image-box.php:578 includes/widgets/image.php:519
#: includes/widgets/social-icons.php:577
#: includes/widgets/traits/button-trait.php:466
#: modules/floating-buttons/base/widget-contact-button-base.php:1476
#: modules/floating-buttons/base/widget-contact-button-base.php:2512
#: modules/floating-buttons/base/widget-floating-bars-base.php:748
#: modules/nested-tabs/widgets/nested-tabs.php:606
#: modules/shapes/widgets/text-path.php:452
msgid "Hover Animation"
msgstr "Animacja najechania"

#: includes/elements/column.php:892 includes/elements/container.php:1838
#: includes/elements/section.php:1333 includes/widgets/common-base.php:853
#: modules/floating-buttons/base/widget-contact-button-base.php:1406
#: modules/floating-buttons/base/widget-contact-button-base.php:2789
#: modules/floating-buttons/base/widget-floating-bars-base.php:887
msgid "Slow"
msgstr "Powoli"

#: includes/elements/column.php:894 includes/elements/container.php:1840
#: includes/elements/section.php:1335 includes/widgets/common-base.php:855
#: modules/floating-buttons/base/widget-contact-button-base.php:1408
#: modules/floating-buttons/base/widget-contact-button-base.php:2791
#: modules/floating-buttons/base/widget-floating-bars-base.php:889
msgid "Fast"
msgstr "Szybko"

#: includes/controls/groups/box-shadow.php:73
msgctxt "Box Shadow Control"
msgid "Inset"
msgstr "Wewnętrzny"

#: includes/elements/column.php:879 includes/elements/container.php:1825
#: includes/elements/section.php:1320 includes/widgets/common-base.php:840
#: includes/widgets/video.php:929
#: modules/floating-buttons/base/widget-contact-button-base.php:1392
#: modules/floating-buttons/base/widget-floating-bars-base.php:873
msgid "Entrance Animation"
msgstr "Animacja wejścia"

#: includes/controls/box-shadow.php:73 includes/controls/text-shadow.php:76
#: includes/widgets/tabs.php:189
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:50
msgid "Vertical"
msgstr "Pionowo"

#: includes/controls/box-shadow.php:78 includes/controls/text-shadow.php:66
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:51
#: assets/js/packages/editor-controls/editor-controls.strings.js:123
#: assets/js/packages/editor-controls/editor-controls.strings.js:169
msgid "Blur"
msgstr "Rozmycie"

#: includes/controls/box-shadow.php:68 includes/controls/text-shadow.php:71
#: includes/widgets/tabs.php:193
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:49
msgid "Horizontal"
msgstr "Poziomo"

#: includes/controls/box-shadow.php:83
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:52
msgid "Spread"
msgstr "Rozpiętość"

#: includes/settings/settings.php:280
msgid "Disable Default Colors"
msgstr "Wyłącz domyślne kolory"

#: includes/widgets/social-icons.php:212 includes/widgets/social-icons.php:365
msgid "Official Color"
msgstr "Oficjalny kolor"

#: includes/widgets/testimonial.php:46 includes/widgets/testimonial.php:132
msgid "Testimonial"
msgstr "Referencja"

#: includes/widgets/testimonial.php:222
msgid "Aside"
msgstr "Notatka na marginesie"

#: includes/widgets/icon-box.php:152 includes/widgets/icon.php:150
#: includes/widgets/social-icons.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:2086
#: modules/floating-buttons/base/widget-contact-button-base.php:2177
#: modules/floating-buttons/base/widget-contact-button-base.php:2870
#: modules/floating-buttons/base/widget-floating-bars-base.php:845
#: modules/link-in-bio/base/widget-link-in-bio-base.php:119
msgid "Rounded"
msgstr "Zaokrąglone"

#: core/kits/documents/tabs/global-colors.php:123
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:185
msgid "Custom Colors"
msgstr "Własne kolory"

#: includes/widgets/social-icons.php:42 includes/widgets/social-icons.php:107
#: includes/widgets/social-icons.php:250
msgid "Social Icons"
msgstr "Ikonki społecznościowe"

#: includes/widgets/progress.php:123
msgid "My Skill"
msgstr "Moja umiejętność"

#: core/admin/feedback.php:110
msgid "Other"
msgstr "Inne"

#. translators: %s: Elementor version.
#: core/admin/admin-notices.php:138
msgid "View Elementor version %s details"
msgstr "Zobacz szczegóły wersji %s Elementora"

#. translators: 1: Details URL, 2: Accessibility text, 3: Version number, 4:
#. Update URL, 5: Accessibility text.
#: core/admin/admin-notices.php:134
msgid "There is a new version of Elementor Page Builder available. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">update now</a>."
msgstr "Dostępna jest już nowa wersja Elementora. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">Zobacz szczegóły wersji %3$s</a> lub <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">aktualizuj</a>."

#: includes/widgets/audio.php:240
#: modules/floating-buttons/base/widget-contact-button-base.php:358
#: modules/floating-buttons/base/widget-contact-button-base.php:911
#: modules/link-in-bio/base/widget-link-in-bio-base.php:523
#: modules/link-in-bio/base/widget-link-in-bio-base.php:777
msgid "Username"
msgstr "Nazwa użytkownika"

#: includes/widgets/audio.php:218
msgid "Comments"
msgstr "Komentarze"

#: core/base/providers/social-network-provider.php:168
#: includes/widgets/audio.php:53 includes/widgets/audio.php:104
msgid "SoundCloud"
msgstr "SoundCloud"

#: core/admin/feedback.php:127
msgid "If you have a moment, please share why you are deactivating Elementor:"
msgstr "Jeśli masz chwilkę, napisz, dlaczego wyłączasz Elementora:"

#: includes/widgets/audio.php:130
msgid "Visual Player"
msgstr "Odtwarzacz wizualny"

#: includes/elements/column.php:388 includes/elements/container.php:774
#: includes/elements/section.php:644
#: modules/floating-buttons/base/widget-floating-bars-base.php:981
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1483
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:176
msgid "Background Overlay"
msgstr "Nakładka tła"

#: core/admin/admin-notices.php:143 core/admin/admin-notices.php:151
#: core/base/db-upgrades-manager.php:97
msgid "Update Now"
msgstr "Aktualizuj teraz"

#: core/admin/feedback.php:101
msgid "It's a temporary deactivation"
msgstr "Wyłączono tylko na chwilę"

#: core/admin/feedback.php:93
msgid "I found a better plugin"
msgstr "Mam lepszą wtyczkę"

#: core/admin/feedback.php:111
msgid "Please share the reason"
msgstr "Proszę podać powód"

#: core/admin/feedback.php:89
msgid "I no longer need the plugin"
msgstr "Wtyczka nie jest już mi dłużej potrzebna"

#: includes/widgets/audio.php:182 includes/widgets/video.php:583
msgid "Download Button"
msgstr "Przycisk pobierania"

#: includes/widgets/audio.php:207
msgid "Share Button"
msgstr "Przycisk udostępnij"

#: includes/widgets/audio.php:171
msgid "Like Button"
msgstr "Przycisk Lubię to"

#: includes/widgets/audio.php:160
msgid "Buy Button"
msgstr "Przycisk Kup"

#: includes/elements/section.php:290
msgid "Extended"
msgstr "Rozszerzony"

#: core/admin/feedback.php:119
msgid "Quick Feedback"
msgstr "Szybkie uwagi"

#: core/admin/feedback.php:97
msgid "I couldn't get the plugin to work"
msgstr "Nie udało mi się uruchomić wtyczki"

#: core/admin/feedback.php:94
msgid "Please share which plugin"
msgstr "Udostępnij tę wtyczkę"

#: includes/widgets/audio.php:229
msgid "Play Counts"
msgstr "Liczba odtworzeń"

#: core/kits/documents/tabs/settings-background.php:18
#: includes/elements/column.php:276 includes/elements/container.php:645
#: includes/elements/section.php:533 includes/widgets/accordion.php:326
#: includes/widgets/accordion.php:496 includes/widgets/common-base.php:891
#: includes/widgets/toggle.php:358 includes/widgets/toggle.php:520
#: modules/floating-buttons/base/widget-contact-button-base.php:1678
#: modules/floating-buttons/base/widget-floating-bars-base.php:952
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1464
#: assets/js/ai-admin.js:11315 assets/js/ai-gutenberg.js:11533
#: assets/js/ai-media-library.js:11315
#: assets/js/ai-unify-product-images.js:11315 assets/js/ai.js:12867
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:14
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:250
msgid "Background"
msgstr "Tło"

#: core/common/modules/finder/categories/general.php:29
#: core/role-manager/role-manager.php:69 includes/managers/elements.php:302
#: includes/settings/settings.php:252 includes/settings/settings.php:255
#: includes/settings/tools.php:309
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:20
msgid "General"
msgstr "Ogólne"

#: includes/elements/section.php:292
msgid "Wider"
msgstr "Szerzej"

#: includes/editor-templates/hotkeys.php:46 assets/js/editor.js:30708
#: assets/js/editor.js:33037 assets/js/editor.js:42640
#: assets/js/editor.js:43669
msgid "Paste"
msgstr "Wklej"

#: includes/widgets/image-carousel.php:364 includes/widgets/image.php:198
msgid "Custom URL"
msgstr "Własny adres URL"

#: includes/controls/groups/image-size.php:303
msgid "You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio."
msgstr "Możesz przyciąć oryginalny rozmiar obrazka do dowolnych wymiarów. Możesz również ustawić pojedynczą wartość dla wysokości lub szerokości, by zachować oryginalne proporcje obrazu."

#: includes/controls/groups/background.php:749
#: includes/controls/groups/flex-container.php:29
#: includes/widgets/image-carousel.php:545
#: modules/nested-tabs/widgets/nested-tabs.php:196
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:85
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:181
msgid "Direction"
msgstr "Kierunek"

#: includes/widgets/image-carousel.php:587
#: includes/widgets/image-carousel.php:652
msgid "Inside"
msgstr "Wewnątrz"

#: includes/widgets/image-carousel.php:588
#: includes/widgets/image-carousel.php:651
msgid "Outside"
msgstr "Na zewnątrz"

#: includes/widgets/accordion.php:463 includes/widgets/divider.php:790
#: includes/widgets/divider.php:957 includes/widgets/image-carousel.php:771
#: includes/widgets/image-carousel.php:905
#: includes/widgets/image-gallery.php:440 includes/widgets/image.php:652
#: includes/widgets/rating.php:85 includes/widgets/social-icons.php:457
#: includes/widgets/star-rating.php:351 includes/widgets/toggle.php:487
#: modules/nested-accordion/widgets/nested-accordion.php:632
#: modules/nested-tabs/widgets/nested-tabs.php:920
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:10
msgid "Spacing"
msgstr "Odstęp"

#: includes/widgets/image-carousel.php:46
#: includes/widgets/image-carousel.php:146
#: includes/widgets/image-carousel.php:155
msgid "Image Carousel"
msgstr "Karuzela obrazków"

#: includes/widgets/image-carousel.php:236
msgid "Arrows and Dots"
msgstr "Strzałki i kropki"

#: includes/elements/container.php:549 includes/widgets/audio.php:143
#: includes/widgets/image-carousel.php:427
msgid "Additional Options"
msgstr "Opcje dodatkowe"

#: includes/widgets/image-carousel.php:534
msgid "Animation Speed"
msgstr "Szybkość animacji"

#: includes/widgets/image-carousel.php:219
msgid "Image Stretch"
msgstr "Rozciąganie obrazka"

#: includes/elements/column.php:888 includes/elements/container.php:1834
#: includes/elements/section.php:1329 includes/widgets/common-base.php:849
#: includes/widgets/counter.php:188
#: modules/floating-buttons/base/widget-contact-button-base.php:1402
#: modules/floating-buttons/base/widget-contact-button-base.php:2785
#: modules/floating-buttons/base/widget-floating-bars-base.php:883
#: modules/nested-accordion/widgets/nested-accordion.php:372
msgid "Animation Duration"
msgstr "Czas trwania animacji"

#: includes/widgets/video.php:557
msgid "Intro Byline"
msgstr "Wprowadzenie"

#: includes/controls/groups/background.php:103 includes/widgets/video.php:45
#: includes/widgets/video.php:148 includes/widgets/video.php:751
msgid "Video"
msgstr "Film"

#: core/base/providers/social-network-provider.php:186
#: includes/widgets/video.php:160
msgid "Vimeo"
msgstr "Vimeo"

#: includes/widgets/video.php:352
msgid "Video Options"
msgstr "Opcje filmu"

#: includes/widgets/video.php:397
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:82
msgid "Loop"
msgstr "Pętla"

#: includes/controls/groups/background.php:525
msgid "Video Link"
msgstr "Odnośnik filmu"

#: includes/controls/image-dimensions.php:81
msgid "The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing."
msgstr "Na serwerze nie zainstalowano ani nie włączono bibliotek ImageMagick ani GD! Każda z tych bibliotek jest wymagana do zmiany rozmiaru obrazków w WordPressie. Przed kontynuowaniem skontaktuj się z administratorem serwera, aby włączyć tę funkcję."

#: includes/widgets/audio.php:251 includes/widgets/video.php:571
msgid "Controls Color"
msgstr "Kolor sterowania"

#: includes/widgets/video.php:543
msgid "Intro Portrait"
msgstr "Portret wprowadzający"

#: includes/widgets/video.php:529
msgid "Intro Title"
msgstr "Tytuł wprowadzający"

#: includes/widgets/image-carousel.php:517
msgid "Effect"
msgstr "Efekt"

#: includes/controls/gallery.php:92
msgid "Edit gallery"
msgstr "Edytuj galerię"

#: includes/widgets/image-carousel.php:522
msgid "Fade"
msgstr "Zanikanie"

#: includes/controls/media.php:362
msgctxt "Image Size Control"
msgid "Full"
msgstr "Pełny"

#: includes/widgets/image-carousel.php:238
msgid "Dots"
msgstr "Kropki"

#: includes/controls/groups/background.php:107
msgid "Slideshow"
msgstr "Pokaz slajdów"

#: includes/elements/column.php:183 includes/widgets/icon-box.php:269
#: includes/widgets/icon-list.php:572 includes/widgets/image-box.php:240
msgid "Vertical Alignment"
msgstr "Wyrównanie pionowe"

#: includes/controls/groups/background.php:634
#: includes/widgets/image-carousel.php:504
msgid "Infinite Loop"
msgstr "Pętla nieskończona"

#: includes/controls/groups/typography.php:321
msgctxt "Typography Control"
msgid "Width"
msgstr "Szerokość"

#: includes/elements/column.php:837 includes/elements/container.php:1796
#: includes/elements/section.php:1298 includes/widgets/common-base.php:811
#: modules/floating-buttons/base/widget-contact-button-base.php:3116
#: modules/floating-buttons/base/widget-floating-bars-base.php:1522
msgid "Add your custom class WITHOUT the dot. e.g: my-class"
msgstr "Dodaj własną klasę BEZ KROPEK, np. moja-klasa"

#: includes/widgets/icon-box.php:172 includes/widgets/image-box.php:147
msgid "This is the heading"
msgstr "To jest nagłówek"

#: includes/widgets/accordion.php:243 includes/widgets/counter.php:242
#: includes/widgets/icon-box.php:207 includes/widgets/image-box.php:182
#: includes/widgets/progress.php:131 includes/widgets/toggle.php:246
#: modules/nested-accordion/widgets/nested-accordion.php:275
msgid "Title HTML Tag"
msgstr "Znacznik HTML tytułu"

#: core/kits/documents/tabs/global-typography.php:200
msgid "The list of fonts used if the chosen font is not available."
msgstr "Lista krojów pisma używanych w przypadku, gdy wybrany krój pisma nie jest dostępny."

#: includes/editor-templates/panel-elements.php:14 assets/js/editor.js:19994
#: assets/js/editor.js:21891 assets/js/editor.js:22299
#: assets/js/editor.js:37698
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:19
#: assets/js/packages/editor-canvas/editor-canvas.js:2
#: assets/js/packages/editor-canvas/editor-canvas.strings.js:6
msgid "Elements"
msgstr "Elementy"

#: includes/widgets/wordpress.php:242
msgid "Form"
msgstr "Formularz"

#: core/admin/admin.php:343 core/admin/menu/main.php:75
#: core/common/modules/finder/categories/settings.php:29
#: core/dynamic-tags/base-tag.php:171 includes/editor-templates/panel.php:77
#: includes/managers/controls.php:339
#: includes/settings/admin-menu-items/admin-menu-item.php:28
#: includes/settings/settings.php:216
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:70
#: modules/atomic-widgets/elements/atomic-divider/atomic-divider.php:60
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:76
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:71
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:70
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:64
#: modules/atomic-widgets/elements/atomic-tabs/atomic-tab-link.php:55
#: modules/atomic-widgets/elements/atomic-tabs/atomic-tab-list.php:54
#: modules/atomic-widgets/elements/atomic-tabs/atomic-tabs-content.php:55
#: modules/atomic-widgets/elements/atomic-tabs/atomic-tabs.php:51
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:90
#: modules/atomic-widgets/elements/div-block/div-block.php:68
#: modules/atomic-widgets/elements/flexbox/flexbox.php:68
#: modules/usage/settings-reporter.php:13 assets/js/editor.js:7485
#: assets/js/editor.js:38631 assets/js/editor.js:48059
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:46
msgid "Settings"
msgstr "Ustawienia"

#: includes/controls/groups/typography.php:179
msgctxt "Typography Control"
msgid "Italic"
msgstr "Kursywa"

#: includes/controls/groups/typography.php:167
msgctxt "Typography Control"
msgid "Capitalize"
msgstr "Kapitaliki"

#: includes/controls/groups/typography.php:165
msgctxt "Typography Control"
msgid "Uppercase"
msgstr "Wielkie litery"

#: includes/controls/groups/typography.php:180
msgctxt "Typography Control"
msgid "Oblique"
msgstr "Pochyła"

#: includes/controls/groups/typography.php:166
msgctxt "Typography Control"
msgid "Lowercase"
msgstr "Małe litery"

#. Author of the plugin
#: elementor.php
msgid "Elementor.com"
msgstr "Elementor.com"

#: core/base/providers/social-network-provider.php:138
#: includes/widgets/video.php:159
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:35
msgid "YouTube"
msgstr "YouTube"

#: includes/widgets/sidebar.php:91
msgid "No sidebars were found"
msgstr "Nie znaleziono paneli bocznych"

#: includes/widgets/image-carousel.php:237
#: includes/widgets/image-carousel.php:571
msgid "Arrows"
msgstr "Strzałki"

#: includes/widgets/common-base.php:1100 includes/widgets/icon-box.php:148
#: includes/widgets/icon.php:146 includes/widgets/social-icons.php:280
msgid "Shape"
msgstr "Kształt"

#: includes/widgets/common-base.php:136 includes/widgets/icon-box.php:153
#: includes/widgets/icon.php:151 includes/widgets/social-icons.php:286
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1678
#: modules/shapes/module.php:45
msgid "Circle"
msgstr "Koło"

#: includes/widgets/icon-box.php:151 includes/widgets/icon.php:149
#: includes/widgets/social-icons.php:284
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1679
#: assets/js/ai-admin.js:11368 assets/js/ai-gutenberg.js:11586
#: assets/js/ai-media-library.js:11368
#: assets/js/ai-unify-product-images.js:11368 assets/js/ai.js:12920
msgid "Square"
msgstr "Kwadrat"

#: includes/widgets/alert.php:136 includes/widgets/heading.php:192
#: includes/widgets/icon-box.php:173 includes/widgets/image-box.php:148
#: includes/widgets/progress.php:122
msgid "Enter your title"
msgstr "Wpisz swój tytuł"

#: includes/widgets/html.php:42
msgid "HTML"
msgstr "HTML"

#: includes/widgets/html.php:97 includes/widgets/html.php:104
msgid "HTML Code"
msgstr "Kod HTML"

#: core/kits/documents/tabs/settings-lightbox.php:101
#: core/kits/documents/tabs/settings-lightbox.php:118
#: includes/widgets/image-carousel.php:410
#: includes/widgets/image-carousel.php:416
#: includes/widgets/image-carousel.php:833
#: includes/widgets/image-gallery.php:170
#: includes/widgets/image-gallery.php:356 includes/widgets/image.php:158
#: includes/widgets/image.php:565
msgid "Caption"
msgstr "Podpis"

#: includes/widgets/google-maps.php:44 includes/widgets/google-maps.php:125
#: includes/widgets/google-maps.php:212
msgid "Google Maps"
msgstr "Mapy Google"

#: includes/widgets/heading.php:49 includes/widgets/heading.php:177
#: includes/widgets/heading.php:258
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:33
#: modules/link-in-bio/base/widget-link-in-bio-base.php:849
#: modules/link-in-bio/base/widget-link-in-bio-base.php:854
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1295
msgid "Heading"
msgstr "Nagłówek"

#: includes/widgets/image-carousel.php:185
msgid "Slides to Show"
msgstr "Slajdy do pokazania"

#: includes/widgets/audio.php:152 includes/widgets/image-carousel.php:443
#: includes/widgets/video.php:361
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:80
msgid "Autoplay"
msgstr "Automatyczne odtwarzanie"

#: core/kits/documents/tabs/settings-lightbox.php:56
#: includes/widgets/counter.php:45 includes/widgets/counter.php:127
#: includes/widgets/counter.php:267
msgid "Counter"
msgstr "Licznik"

#: includes/widgets/heading.php:221
msgid "XL"
msgstr "XL"

#: includes/widgets/heading.php:222
msgid "XXL"
msgstr "XXL"

#: core/dynamic-tags/tag.php:95 includes/widgets/counter.php:279
#: modules/nested-tabs/widgets/nested-tabs.php:212
#: modules/nested-tabs/widgets/nested-tabs.php:878
msgid "Before"
msgstr "Przed"

#: core/base/traits/shared-widget-controls-trait.php:156
#: includes/elements/column.php:547 includes/elements/container.php:993
#: includes/elements/section.php:831 includes/widgets/common-base.php:966
#: modules/floating-buttons/base/widget-floating-bars-base.php:761
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:15
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:248
msgid "Border"
msgstr "Obramowanie"

#: core/kits/documents/tabs/settings-lightbox.php:103
#: core/kits/documents/tabs/settings-lightbox.php:113
#: core/kits/documents/tabs/settings-lightbox.php:120
#: includes/compatibility.php:163 includes/widgets/alert.php:302
#: includes/widgets/icon-box.php:181 includes/widgets/icon-box.php:740
#: includes/widgets/image-box.php:156 includes/widgets/image-box.php:704
#: includes/widgets/image-carousel.php:417
#: modules/floating-buttons/base/widget-contact-button-base.php:2342
#: modules/link-in-bio/base/widget-link-in-bio-base.php:895
#: modules/link-in-bio/base/widget-link-in-bio-base.php:900
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1351
msgid "Description"
msgstr "Opis"

#: core/kits/documents/tabs/theme-style-form-fields.php:78
#: core/kits/documents/tabs/theme-style-typography.php:126
#: core/kits/documents/tabs/theme-style-typography.php:155
#: core/kits/documents/tabs/theme-style-typography.php:200
#: includes/controls/box-shadow.php:104
#: includes/controls/groups/background.php:170
#: includes/controls/text-shadow.php:97 includes/elements/column.php:458
#: includes/elements/container.php:869 includes/elements/container.php:1227
#: includes/elements/section.php:726 includes/elements/section.php:1008
#: includes/widgets/accordion.php:337 includes/widgets/accordion.php:439
#: includes/widgets/accordion.php:507 includes/widgets/alert.php:418
#: includes/widgets/alert.php:435 includes/widgets/divider.php:589
#: includes/widgets/divider.php:733 includes/widgets/heading.php:335
#: includes/widgets/icon-box.php:681 includes/widgets/icon-box.php:705
#: includes/widgets/icon-box.php:768 includes/widgets/icon-list.php:395
#: includes/widgets/icon-list.php:432 includes/widgets/icon-list.php:457
#: includes/widgets/icon-list.php:661 includes/widgets/icon-list.php:685
#: includes/widgets/image-box.php:645 includes/widgets/image-box.php:669
#: includes/widgets/image-box.php:732 includes/widgets/image-carousel.php:620
#: includes/widgets/image-carousel.php:704 includes/widgets/progress.php:306
#: includes/widgets/progress.php:367 includes/widgets/rating.php:110
#: includes/widgets/social-icons.php:208 includes/widgets/social-icons.php:361
#: includes/widgets/star-rating.php:376 includes/widgets/tabs.php:366
#: includes/widgets/tabs.php:459 includes/widgets/toggle.php:370
#: includes/widgets/toggle.php:463 includes/widgets/toggle.php:531
#: includes/widgets/video.php:821
#: modules/floating-buttons/base/widget-contact-button-base.php:1888
#: modules/floating-buttons/base/widget-contact-button-base.php:2287
#: modules/floating-buttons/base/widget-contact-button-base.php:2300
#: modules/floating-buttons/base/widget-contact-button-base.php:2538
#: modules/floating-buttons/base/widget-floating-bars-base.php:422
#: modules/floating-buttons/base/widget-floating-bars-base.php:540
#: modules/floating-buttons/base/widget-floating-bars-base.php:1074
#: modules/floating-buttons/base/widget-floating-bars-base.php:1275
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1207
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1392
#: modules/nested-accordion/widgets/nested-accordion.php:691
#: modules/nested-accordion/widgets/nested-accordion.php:754
#: modules/nested-tabs/widgets/nested-tabs.php:749
#: modules/nested-tabs/widgets/nested-tabs.php:785
#: modules/nested-tabs/widgets/nested-tabs.php:821
#: modules/nested-tabs/widgets/nested-tabs.php:947
#: modules/nested-tabs/widgets/nested-tabs.php:964
#: modules/nested-tabs/widgets/nested-tabs.php:981
#: modules/shapes/widgets/text-path.php:416
#: modules/shapes/widgets/text-path.php:440
#: modules/shapes/widgets/text-path.php:508
#: modules/shapes/widgets/text-path.php:528
#: modules/shapes/widgets/text-path.php:579
#: modules/shapes/widgets/text-path.php:599 assets/js/editor.js:48197
#: assets/js/editor.js:48240
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:45
#: assets/js/packages/editor-controls/editor-controls.strings.js:135
#: assets/js/packages/editor-controls/editor-controls.strings.js:170
#: assets/js/packages/editor-controls/editor-controls.strings.js:179
msgid "Color"
msgstr "Kolor"

#: includes/widgets/accordion.php:419 includes/widgets/divider.php:470
#: includes/widgets/heading.php:266 includes/widgets/icon-box.php:304
#: includes/widgets/icon-list.php:259 includes/widgets/icon.php:185
#: includes/widgets/image-box.php:268 includes/widgets/image-carousel.php:844
#: includes/widgets/image-gallery.php:367 includes/widgets/image.php:259
#: includes/widgets/image.php:577 includes/widgets/rating.php:199
#: includes/widgets/social-icons.php:324 includes/widgets/star-rating.php:215
#: includes/widgets/tabs.php:205 includes/widgets/tabs.php:235
#: includes/widgets/tabs.php:422 includes/widgets/testimonial.php:242
#: includes/widgets/text-editor.php:258 includes/widgets/toggle.php:443
#: includes/widgets/traits/button-trait.php:284
#: modules/shapes/widgets/text-path.php:172
msgid "Alignment"
msgstr "Wyrównanie"

#: includes/controls/groups/flex-item.php:125 includes/widgets/alert.php:353
#: includes/widgets/common-base.php:1135 includes/widgets/divider.php:639
#: includes/widgets/divider.php:847 includes/widgets/heading.php:214
#: includes/widgets/icon-box.php:513 includes/widgets/icon-list.php:491
#: includes/widgets/icon.php:306 includes/widgets/image-carousel.php:600
#: includes/widgets/image-carousel.php:684 includes/widgets/rating.php:60
#: includes/widgets/social-icons.php:403 includes/widgets/star-rating.php:326
#: includes/widgets/text-editor.php:474
#: includes/widgets/traits/button-trait.php:114 includes/widgets/video.php:837
#: modules/floating-buttons/base/widget-contact-button-base.php:1116
#: modules/floating-buttons/base/widget-contact-button-base.php:1541
#: modules/floating-buttons/base/widget-contact-button-base.php:1923
#: modules/floating-buttons/base/widget-contact-button-base.php:2273
#: modules/floating-buttons/base/widget-floating-bars-base.php:490
#: modules/floating-buttons/base/widget-floating-bars-base.php:1088
#: modules/floating-buttons/base/widget-floating-bars-base.php:1313
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1403
#: modules/nested-accordion/widgets/nested-accordion.php:608
#: modules/nested-tabs/widgets/nested-tabs.php:900
#: modules/shapes/widgets/text-path.php:251
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:192
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:11
msgid "Size"
msgstr "Rozmiar"

#: includes/widgets/button.php:48 includes/widgets/button.php:115
#: includes/widgets/button.php:126
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:33
#: modules/floating-buttons/base/widget-floating-bars-base.php:567
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1098
msgid "Button"
msgstr "Przycisk"

#: includes/widgets/heading.php:218 includes/widgets/traits/button-trait.php:35
#: modules/floating-buttons/base/widget-contact-button-base.php:1120
#: modules/floating-buttons/base/widget-contact-button-base.php:1545
#: modules/floating-buttons/base/widget-contact-button-base.php:1927
#: modules/floating-buttons/base/widget-contact-button-base.php:2277
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1407
msgid "Small"
msgstr "Mały"

#: includes/widgets/heading.php:219 includes/widgets/traits/button-trait.php:36
#: modules/floating-buttons/base/widget-contact-button-base.php:1121
#: modules/floating-buttons/base/widget-contact-button-base.php:1546
#: modules/floating-buttons/base/widget-contact-button-base.php:1928
#: modules/floating-buttons/base/widget-contact-button-base.php:2278
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1408
msgid "Medium"
msgstr "Średni"

#: includes/base/element-base.php:1399
#: includes/controls/groups/grid-container.php:34
#: includes/widgets/alert.php:164 includes/widgets/audio.php:162
#: includes/widgets/audio.php:173 includes/widgets/audio.php:184
#: includes/widgets/audio.php:195 includes/widgets/audio.php:209
#: includes/widgets/audio.php:220 includes/widgets/audio.php:231
#: includes/widgets/audio.php:242 includes/widgets/counter.php:203
#: includes/widgets/progress.php:157 includes/widgets/progress.php:207
#: includes/widgets/video.php:411 includes/widgets/video.php:426
#: includes/widgets/video.php:453 includes/widgets/video.php:531
#: includes/widgets/video.php:545 includes/widgets/video.php:559
#: includes/widgets/video.php:585 includes/widgets/video.php:644
#: includes/widgets/video.php:685
#: modules/floating-buttons/base/widget-contact-button-base.php:519
#: modules/floating-buttons/base/widget-contact-button-base.php:629
#: modules/floating-buttons/base/widget-contact-button-base.php:694
#: modules/floating-buttons/base/widget-contact-button-base.php:2528
#: modules/floating-buttons/base/widget-floating-bars-base.php:231
#: modules/floating-buttons/base/widget-floating-bars-base.php:298
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:68
#: assets/js/packages/editor-controls/editor-controls.strings.js:187
msgid "Hide"
msgstr "Ukryj"

#: core/base/document.php:1971
#: core/kits/documents/tabs/settings-lightbox.php:96
#: core/kits/documents/tabs/settings-lightbox.php:100
#: core/kits/documents/tabs/settings-lightbox.php:117
#: includes/elements/column.php:127 includes/elements/section.php:240
#: includes/widgets/accordion.php:132 includes/widgets/accordion.php:318
#: includes/widgets/alert.php:134 includes/widgets/alert.php:262
#: includes/widgets/common-base.php:301 includes/widgets/counter.php:228
#: includes/widgets/counter.php:542 includes/widgets/heading.php:184
#: includes/widgets/icon-box.php:167 includes/widgets/icon-box.php:636
#: includes/widgets/image-box.php:142 includes/widgets/image-box.php:600
#: includes/widgets/image-carousel.php:415 includes/widgets/progress.php:117
#: includes/widgets/progress.php:244 includes/widgets/star-rating.php:203
#: includes/widgets/star-rating.php:247 includes/widgets/tabs.php:127
#: includes/widgets/tabs.php:357 includes/widgets/testimonial.php:189
#: includes/widgets/testimonial.php:415 includes/widgets/toggle.php:132
#: includes/widgets/toggle.php:350
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:70
#: modules/floating-buttons/base/widget-contact-button-base.php:129
#: modules/floating-buttons/base/widget-contact-button-base.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:2314
#: modules/link-in-bio/base/widget-link-in-bio-base.php:869
#: modules/nested-accordion/widgets/nested-accordion.php:120
#: modules/nested-accordion/widgets/nested-accordion.php:568
#: modules/nested-tabs/widgets/nested-tabs.php:118
msgid "Title"
msgstr "Tytuł"

#: core/common/modules/finder/categories/edit.php:30
#: includes/controls/popover-toggle.php:68 assets/js/ai-admin.js:2332
#: assets/js/ai-admin.js:10193 assets/js/ai-admin.js:10200
#: assets/js/ai-gutenberg.js:2470 assets/js/ai-gutenberg.js:10411
#: assets/js/ai-gutenberg.js:10418 assets/js/ai-media-library.js:2332
#: assets/js/ai-media-library.js:10193 assets/js/ai-media-library.js:10200
#: assets/js/ai-unify-product-images.js:2332
#: assets/js/ai-unify-product-images.js:10193
#: assets/js/ai-unify-product-images.js:10200 assets/js/ai.js:3120
#: assets/js/ai.js:11745 assets/js/ai.js:11752 assets/js/app.js:8978
#: assets/js/app.js:8990 assets/js/element-manager-admin.js:873
#: assets/js/element-manager-admin.js:929
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:61
msgid "Edit"
msgstr "Edytuj"

#: includes/widgets/heading.php:220 includes/widgets/traits/button-trait.php:37
#: modules/floating-buttons/base/widget-contact-button-base.php:1122
#: modules/floating-buttons/base/widget-contact-button-base.php:1547
#: modules/floating-buttons/base/widget-contact-button-base.php:1929
#: modules/floating-buttons/base/widget-contact-button-base.php:2279
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1409
msgid "Large"
msgstr "Duży"

#: includes/widgets/alert.php:122 includes/widgets/progress.php:172
#: includes/widgets/traits/button-trait.php:72 assets/js/app-packages.js:4560
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:4306
msgid "Info"
msgstr "Informacja"

#: includes/widgets/alert.php:124 includes/widgets/progress.php:174
#: includes/widgets/traits/button-trait.php:74
msgid "Warning"
msgstr "Ostrzeżenie"

#: includes/widgets/alert.php:125 includes/widgets/progress.php:175
#: includes/widgets/traits/button-trait.php:75
msgid "Danger"
msgstr "Niebezpieczeństwo"

#: includes/widgets/alert.php:151
msgid "I am a description. Click the edit button to change this text."
msgstr "Jestem opisem. Kliknij przycisk, by edytować ten tekst."

#: modules/system-info/module.php:166
msgid "You can copy the below info as simple text with Ctrl+C / Ctrl+V:"
msgstr "Możesz skopiować poniższą informację jako prosty tekst korzystając z Ctrl+C / Ctrl+V:"

#: core/admin/admin-notices.php:238
msgid "Sure! I'd love to help"
msgstr "Pewnie! Chętnie pomogę"

#: core/settings/editor-preferences/model.php:195
#: includes/widgets/image-carousel.php:232
#: includes/widgets/image-carousel.php:560
msgid "Navigation"
msgstr "Nawigacja"

#: includes/managers/elements.php:351
msgid "WordPress"
msgstr "WordPress"

#: includes/base/element-base.php:1400
#: includes/controls/groups/grid-container.php:33
#: includes/widgets/alert.php:163 includes/widgets/audio.php:163
#: includes/widgets/audio.php:174 includes/widgets/audio.php:185
#: includes/widgets/audio.php:196 includes/widgets/audio.php:210
#: includes/widgets/audio.php:221 includes/widgets/audio.php:232
#: includes/widgets/audio.php:243 includes/widgets/counter.php:202
#: includes/widgets/progress.php:156 includes/widgets/progress.php:206
#: includes/widgets/video.php:412 includes/widgets/video.php:427
#: includes/widgets/video.php:454 includes/widgets/video.php:532
#: includes/widgets/video.php:546 includes/widgets/video.php:560
#: includes/widgets/video.php:586 includes/widgets/video.php:645
#: includes/widgets/video.php:686
#: modules/floating-buttons/base/widget-contact-button-base.php:518
#: modules/floating-buttons/base/widget-contact-button-base.php:628
#: modules/floating-buttons/base/widget-contact-button-base.php:693
#: modules/floating-buttons/base/widget-contact-button-base.php:2527
#: modules/floating-buttons/base/widget-floating-bars-base.php:230
#: modules/floating-buttons/base/widget-floating-bars-base.php:297
#: assets/js/element-manager-admin.js:449
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:67
#: assets/js/packages/editor-controls/editor-controls.strings.js:186
msgid "Show"
msgstr "Pokaż"

#: core/base/traits/shared-widget-controls-trait.php:158
#: core/settings/editor-preferences/model.php:118
#: core/settings/editor-preferences/model.php:130
#: core/settings/editor-preferences/model.php:141
#: core/settings/editor-preferences/model.php:163
#: core/settings/editor-preferences/model.php:186
#: includes/controls/switcher.php:74 includes/managers/icons.php:470
#: includes/widgets/audio.php:134 includes/widgets/image-carousel.php:224
#: includes/widgets/image-carousel.php:398
#: includes/widgets/image-carousel.php:445
#: includes/widgets/image-carousel.php:458
#: includes/widgets/image-carousel.php:475
#: includes/widgets/image-carousel.php:506
#: includes/widgets/image-gallery.php:211 includes/widgets/image.php:236
#: modules/floating-buttons/base/widget-contact-button-base.php:3047
#: modules/floating-buttons/base/widget-floating-bars-base.php:763
#: modules/floating-buttons/base/widget-floating-bars-base.php:1455
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1556
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1614
#: modules/nested-accordion/widgets/nested-accordion.php:314
#: modules/styleguide/module.php:132 assets/js/app.js:14012
msgid "Yes"
msgstr "Tak"

#: core/base/traits/shared-widget-controls-trait.php:159
#: core/settings/editor-preferences/model.php:119
#: core/settings/editor-preferences/model.php:131
#: core/settings/editor-preferences/model.php:142
#: core/settings/editor-preferences/model.php:164
#: core/settings/editor-preferences/model.php:187
#: includes/controls/switcher.php:73 includes/managers/icons.php:469
#: includes/widgets/audio.php:135 includes/widgets/image-carousel.php:223
#: includes/widgets/image-carousel.php:399
#: includes/widgets/image-carousel.php:446
#: includes/widgets/image-carousel.php:459
#: includes/widgets/image-carousel.php:476
#: includes/widgets/image-carousel.php:507
#: includes/widgets/image-gallery.php:212 includes/widgets/image.php:237
#: modules/floating-buttons/base/widget-contact-button-base.php:3048
#: modules/floating-buttons/base/widget-floating-bars-base.php:764
#: modules/floating-buttons/base/widget-floating-bars-base.php:1456
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1557
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1615
#: modules/nested-accordion/widgets/nested-accordion.php:315
#: modules/styleguide/module.php:131
msgid "No"
msgstr "Nie"

#: core/kits/documents/tabs/global-colors.php:87
#: core/kits/documents/tabs/global-typography.php:142
#: includes/widgets/divider.php:505 includes/widgets/divider.php:523
#: includes/widgets/divider.php:722 includes/widgets/icon-list.php:142
#: includes/widgets/icon-list.php:625
#: includes/widgets/traits/button-trait.php:58
#: modules/floating-buttons/base/widget-contact-button-base.php:145
#: modules/floating-buttons/base/widget-contact-button-base.php:1044
#: modules/floating-buttons/base/widget-floating-bars-base.php:63
#: modules/floating-buttons/base/widget-floating-bars-base.php:151
#: modules/floating-buttons/base/widget-floating-bars-base.php:337
#: modules/floating-buttons/base/widget-floating-bars-base.php:1353
#: modules/link-in-bio/base/widget-link-in-bio-base.php:328
#: modules/link-in-bio/base/widget-link-in-bio-base.php:585
#: modules/shapes/widgets/text-path.php:111
#: modules/shapes/widgets/text-path.php:304 assets/js/ai-admin.js:3596
#: assets/js/ai-gutenberg.js:3734 assets/js/ai-media-library.js:3596
#: assets/js/ai-unify-product-images.js:3596 assets/js/ai.js:4417
msgid "Text"
msgstr "Tekst"

#: core/admin/admin.php:223 core/admin/admin.php:231 core/base/document.php:652
#: modules/admin-bar/module.php:124 modules/gutenberg/module.php:101
#: modules/gutenberg/module.php:112 modules/gutenberg/module.php:134
#: assets/js/e-wc-product-editor.js:2678
msgid "Edit with Elementor"
msgstr "Edytuj w Elementorze"

#: includes/fonts.php:71
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:251
msgid "System"
msgstr "System"

#: includes/fonts.php:76
msgid "Google"
msgstr "Google"

#: core/kits/documents/tabs/settings-background.php:79
#: core/kits/documents/tabs/settings-lightbox.php:99
#: core/kits/documents/tabs/settings-lightbox.php:116
#: includes/controls/animation.php:155 includes/controls/groups/border.php:64
#: includes/controls/groups/flex-item.php:130
#: includes/controls/groups/typography.php:193
#: includes/controls/hover-animation.php:129 includes/controls/icons.php:108
#: includes/controls/icons.php:194 includes/widgets/divider.php:501
#: includes/widgets/image-carousel.php:239
#: includes/widgets/image-carousel.php:362
#: includes/widgets/image-carousel.php:414
#: includes/widgets/image-gallery.php:174
#: includes/widgets/image-gallery.php:192 includes/widgets/image.php:161
#: includes/widgets/image.php:196 includes/widgets/video.php:601
#: modules/nested-tabs/widgets/nested-tabs.php:411 assets/js/ai-admin.js:11288
#: assets/js/ai-admin.js:11294 assets/js/ai-admin.js:11306
#: assets/js/ai-admin.js:11317 assets/js/ai-admin.js:11328
#: assets/js/ai-admin.js:11339 assets/js/ai-admin.js:11355
#: assets/js/ai-gutenberg.js:11506 assets/js/ai-gutenberg.js:11512
#: assets/js/ai-gutenberg.js:11524 assets/js/ai-gutenberg.js:11535
#: assets/js/ai-gutenberg.js:11546 assets/js/ai-gutenberg.js:11557
#: assets/js/ai-gutenberg.js:11573 assets/js/ai-media-library.js:11288
#: assets/js/ai-media-library.js:11294 assets/js/ai-media-library.js:11306
#: assets/js/ai-media-library.js:11317 assets/js/ai-media-library.js:11328
#: assets/js/ai-media-library.js:11339 assets/js/ai-media-library.js:11355
#: assets/js/ai-unify-product-images.js:11288
#: assets/js/ai-unify-product-images.js:11294
#: assets/js/ai-unify-product-images.js:11306
#: assets/js/ai-unify-product-images.js:11317
#: assets/js/ai-unify-product-images.js:11328
#: assets/js/ai-unify-product-images.js:11339
#: assets/js/ai-unify-product-images.js:11355 assets/js/ai.js:12840
#: assets/js/ai.js:12846 assets/js/ai.js:12858 assets/js/ai.js:12869
#: assets/js/ai.js:12880 assets/js/ai.js:12891 assets/js/ai.js:12907
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:80
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:89
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:136
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:193
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:194
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:229
msgid "None"
msgstr "Brak"

#: includes/controls/groups/typography.php:113
msgctxt "Typography Control"
msgid "Size"
msgstr "Rozmiar"

#: includes/controls/groups/typography.php:106
msgctxt "Typography Control"
msgid "Family"
msgstr "Rodzina"

#: includes/controls/groups/typography.php:173
msgctxt "Typography Control"
msgid "Style"
msgstr "Styl"

#: core/kits/views/trash-kit-confirmation.php:30
#: includes/editor-templates/hotkeys.php:63
#: includes/editor-templates/templates.php:163
#: includes/editor-templates/templates.php:325
#: includes/editor-templates/templates.php:356
#: includes/editor-templates/templates.php:409 assets/js/editor.js:8938
#: assets/js/editor.js:8957 assets/js/editor.js:9118 assets/js/editor.js:28292
#: assets/js/editor.js:30773 assets/js/editor.js:48250
#: assets/js/import-export-admin.js:272
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:1015
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:1157
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:28
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:56
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:21
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:29
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:20
#: assets/js/packages/editor-variables/editor-variables.strings.js:33
#: assets/js/packages/editor-variables/editor-variables.strings.js:82
msgid "Delete"
msgstr "Usuń"

#. Plugin Name of the plugin
#: elementor.php app/view.php:23 core/admin/admin.php:292
#: core/admin/admin.php:409 core/admin/admin.php:487
#: core/admin/menu/main.php:17 core/admin/menu/main.php:18
#: core/documents-manager.php:384 core/upgrade/custom-tasks-manager.php:29
#: core/upgrade/manager.php:47 includes/editor-templates/navigator.php:94
#: includes/editor-templates/panel-elements.php:100
#: includes/editor-templates/panel.php:202
#: includes/editor-templates/templates.php:212 includes/plugin.php:835
#: includes/settings/admin-menu-items/admin-menu-item.php:29
#: includes/settings/settings.php:91 includes/settings/settings.php:92
#: includes/settings/settings.php:500 modules/compatibility-tag/module.php:36
#: modules/history/views/history-panel-template.php:23
#: modules/history/views/revisions-panel-template.php:38
#: assets/js/app-packages.js:320 assets/js/app.js:491
msgid "Elementor"
msgstr "Elementor"

#: includes/editor-templates/panel.php:66
#: includes/editor-templates/panel.php:67 assets/js/app.js:7259
msgid "Menu"
msgstr "Menu"

#: core/breakpoints/manager.php:334
msgid "Laptop"
msgstr "Laptop"

#: core/admin/admin.php:621 core/admin/menu/main.php:41
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:2
msgid "Help"
msgstr "Pomoc"

#: includes/editor-templates/hotkeys.php:78
#: includes/editor-templates/templates.php:23
#: includes/editor-templates/templates.php:457
#: includes/editor-templates/templates.php:469 assets/js/e-home-screen.js:244
#: assets/js/editor.js:8570 assets/js/editor.js:45532
#: assets/js/element-manager-admin.js:785
#: assets/js/kit-elements-defaults-editor.js:597
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:25
#: assets/js/packages/editor-variables/editor-variables.strings.js:69
msgid "Save"
msgstr "Zapisz"

#: core/editor/loader/v1/templates/editor-body-v1-view.php:35
#: core/editor/loader/v2/templates/editor-body-v2-view.php:37
#: includes/editor-templates/templates.php:286 assets/js/ai-admin.js:7565
#: assets/js/ai-gutenberg.js:7783 assets/js/ai-layout.js:3053
#: assets/js/ai-media-library.js:7565 assets/js/ai-unify-product-images.js:7565
#: assets/js/ai.js:9117 assets/js/editor.js:28231
msgid "Preview"
msgstr "Podgląd"

#: modules/history/views/revisions-panel-template.php:11
#: assets/js/editor.js:45533
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:34
msgid "Discard"
msgstr "Odrzuć"

#: includes/controls/image-dimensions.php:102
#: includes/editor-templates/panel.php:169
#: includes/editor-templates/templates.php:426
#: modules/history/views/revisions-panel-template.php:14
#: assets/js/editor.js:8182 assets/js/editor.js:38712
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:691
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:719
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:1219
msgid "Apply"
msgstr "Zastosuj"

#: modules/floating-buttons/base/widget-contact-button-base.php:1209
#: modules/floating-buttons/base/widget-contact-button-base.php:1261
#: modules/floating-buttons/base/widget-contact-button-base.php:1348
#: modules/floating-buttons/base/widget-contact-button-base.php:1557
#: modules/floating-buttons/base/widget-contact-button-base.php:1723
#: modules/floating-buttons/base/widget-contact-button-base.php:2613
#: modules/floating-buttons/base/widget-contact-button-base.php:2682
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:443
msgid "Colors"
msgstr "Kolory"

#: includes/controls/groups/grid-container.php:42
#: includes/widgets/image-gallery.php:160 includes/widgets/social-icons.php:295
#: includes/widgets/text-editor.php:177
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:117
msgid "Columns"
msgstr "Kolumny"

#: includes/managers/controls.php:335 includes/widgets/divider.php:385
#: includes/widgets/icon-list.php:296 assets/js/ai-admin.js:10498
#: assets/js/ai-gutenberg.js:10716 assets/js/ai-media-library.js:10498
#: assets/js/ai-unify-product-images.js:10498 assets/js/ai.js:12050
#: assets/js/editor.js:7488 assets/js/editor.js:37503
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:2
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:21
msgid "Style"
msgstr "Styl"

#: includes/managers/controls.php:334 includes/widgets/accordion.php:145
#: includes/widgets/accordion.php:488 includes/widgets/alert.php:148
#: includes/widgets/icon-box.php:628 includes/widgets/image-box.php:592
#: includes/widgets/tabs.php:141 includes/widgets/tabs.php:450
#: includes/widgets/testimonial.php:139 includes/widgets/testimonial.php:272
#: includes/widgets/toggle.php:145 includes/widgets/toggle.php:512
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:63
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:66
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:64
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:63
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:58
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:72
#: modules/nested-accordion/widgets/nested-accordion.php:497
#: modules/nested-tabs/widgets/nested-tabs.php:995 assets/js/app.js:5806
#: assets/js/app.js:7279 assets/js/app.js:10351 assets/js/app.js:15443
#: assets/js/app.js:15952 assets/js/editor.js:37500
msgid "Content"
msgstr "Treść"

#: core/common/modules/finder/categories/settings.php:59
#: core/dynamic-tags/tag.php:88 includes/elements/column.php:763
#: includes/elements/section.php:1219 includes/managers/controls.php:336
#: includes/settings/settings.php:329 includes/settings/settings.php:332
#: modules/floating-buttons/base/widget-contact-button-base.php:2921
#: modules/floating-buttons/base/widget-floating-bars-base.php:1419
#: modules/floating-buttons/module.php:79
#: modules/floating-buttons/module.php:84 assets/js/editor.js:7491
#: assets/js/editor.js:37506
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:3596
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:3600
#: assets/js/onboarding.b3b92ba51250858e05aa.bundle.js:1259
msgid "Advanced"
msgstr "Zaawansowane"

#: includes/elements/column.php:924 includes/elements/container.php:1877
#: includes/elements/section.php:1366 includes/managers/controls.php:337
#: includes/widgets/common-base.php:1314
#: modules/floating-buttons/base/widget-contact-button-base.php:3061
#: modules/floating-buttons/base/widget-floating-bars-base.php:1467
msgid "Responsive"
msgstr "Responsywne"

#: includes/editor-templates/hotkeys.php:120
#: includes/editor-templates/navigator.php:35
#: includes/editor-templates/panel.php:86 includes/elements/section.php:511
#: includes/elements/section.php:519 assets/js/editor.js:31958
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:8
msgid "Structure"
msgstr "Struktura"

#: includes/elements/section.php:78 modules/library/documents/section.php:43
#: assets/js/container-converter.js:95 assets/js/editor.js:8496
msgid "Section"
msgstr "Sekcja"

#: includes/controls/gaps.php:57
#: includes/controls/groups/grid-container.php:119
#: includes/elements/column.php:61
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:41
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:183
msgid "Column"
msgstr "Kolumna"

#: includes/controls/icons.php:80 includes/controls/media.php:238
msgid "Add"
msgstr "Dodaj"

#: core/kits/documents/tabs/theme-style-typography.php:18
#: core/kits/documents/tabs/theme-style-typography.php:37
#: includes/controls/groups/typography.php:436 includes/elements/column.php:674
#: includes/elements/section.php:1131 assets/js/editor-modules.js:1431
#: assets/js/editor.js:43341
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:13
msgid "Typography"
msgstr "Typografia"

#: core/document-types/page-base.php:132 includes/elements/column.php:772
#: includes/elements/container.php:1378 includes/elements/section.php:1227
#: includes/widgets/common-base.php:310
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:118
msgid "Margin"
msgstr "Margines"

#: includes/elements/column.php:827 includes/elements/container.php:1786
#: includes/elements/section.php:1288 includes/widgets/common-base.php:802
#: modules/floating-buttons/base/widget-contact-button-base.php:3107
#: modules/floating-buttons/base/widget-floating-bars-base.php:1513
msgid "CSS Classes"
msgstr "Klasy CSS"

#: includes/controls/image-dimensions.php:100
#: includes/elements/container.php:1275 includes/elements/section.php:321
#: includes/elements/section.php:361 includes/elements/section.php:1056
#: includes/widgets/google-maps.php:192 includes/widgets/icon-list.php:362
#: includes/widgets/image-box.php:390 includes/widgets/image.php:354
#: includes/widgets/progress.php:331
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:121
msgid "Height"
msgstr "Wysokość"

#: includes/widgets/image-carousel.php:521
msgid "Slide"
msgstr "Slajd"

#: core/kits/documents/tabs/global-colors.php:92
#: core/kits/documents/tabs/global-typography.php:149
msgid "Accent"
msgstr "Akcent"

#: core/settings/editor-preferences/model.php:90
#: includes/controls/groups/background.php:499
#: includes/controls/image-dimensions.php:95
#: includes/elements/container.php:406 includes/elements/container.php:1241
#: includes/elements/section.php:263 includes/elements/section.php:1022
#: includes/widgets/common-base.php:337 includes/widgets/divider.php:443
#: includes/widgets/icon-list.php:343 includes/widgets/image-box.php:362
#: includes/widgets/image.php:284
#: modules/floating-buttons/base/widget-contact-button-base.php:2842
#: modules/nested-tabs/widgets/nested-tabs.php:317
#: modules/shapes/widgets/text-path.php:540
#: modules/shapes/widgets/text-path.php:611
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:120
msgid "Width"
msgstr "Szerokość"

#: includes/controls/groups/flex-container.php:145
#: includes/controls/groups/flex-item.php:67
#: includes/controls/groups/grid-container.php:147
#: includes/controls/groups/grid-container.php:175
#: includes/elements/section.php:405 includes/widgets/counter.php:404
#: includes/widgets/tabs.php:221 includes/widgets/tabs.php:251
#: includes/widgets/traits/button-trait.php:268
#: modules/floating-buttons/base/widget-floating-bars-base.php:1201
#: modules/nested-accordion/widgets/nested-accordion.php:190
#: modules/nested-tabs/widgets/nested-tabs.php:251
#: modules/nested-tabs/widgets/nested-tabs.php:293
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:201
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:206
msgid "Stretch"
msgstr "Rozciągnij"

#: includes/editor-templates/templates.php:186
#: includes/widgets/testimonial.php:174 includes/widgets/testimonial.php:370
#: modules/floating-buttons/base/widget-contact-button-base.php:121
#: modules/floating-buttons/base/widget-contact-button-base.php:209
#: modules/floating-buttons/base/widget-contact-button-base.php:653
#: modules/floating-buttons/base/widget-contact-button-base.php:1737
#: assets/js/app.js:11812
#: assets/js/packages/editor-components/editor-components.js:2
#: assets/js/packages/editor-components/editor-components.strings.js:5
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:17
#: assets/js/packages/editor-variables/editor-variables.strings.js:23
#: assets/js/packages/editor-variables/editor-variables.strings.js:28
#: assets/js/packages/editor-variables/editor-variables.strings.js:31
msgid "Name"
msgstr "Nazwa"

#: includes/elements/container.php:1885 includes/elements/section.php:1390
msgid "Visibility"
msgstr "Widoczność"

#: includes/base/element-base.php:890 includes/base/element-base.php:902
#: includes/widgets/divider.php:981 includes/widgets/icon-box.php:573
#: includes/widgets/icon.php:371 modules/shapes/widgets/text-path.php:283
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:110
#: assets/js/packages/editor-controls/editor-controls.strings.js:115
msgid "Rotate"
msgstr "Obróć"

#: includes/widgets/video.php:409
msgid "Player Controls"
msgstr "Kontrolki odtwarzacza"

#: includes/widgets/video.php:513
msgid "Suggested Videos"
msgstr "Sugerowane filmy"

#: includes/widgets/video.php:635 includes/widgets/video.php:642
#: includes/widgets/video.php:797
msgid "Image Overlay"
msgstr "Nakładka obrazka"

#: includes/widgets/text-editor.php:46 includes/widgets/text-editor.php:133
#: includes/widgets/text-editor.php:250
msgid "Text Editor"
msgstr "Edytor tekstu"

#: includes/widgets/sidebar.php:93 includes/widgets/sidebar.php:113
msgid "Choose Sidebar"
msgstr "Wybierz panel boczny"

#: includes/widgets/sidebar.php:42 includes/widgets/sidebar.php:106
msgid "Sidebar"
msgstr "Panel boczny"

#: includes/widgets/progress.php:219 includes/widgets/progress.php:355
msgid "Inner Text"
msgstr "Tekst wewnętrzny"

#: includes/widgets/progress.php:45 includes/widgets/progress.php:110
#: includes/widgets/progress.php:236
msgid "Progress Bar"
msgstr "Pasek postępu"

#: core/kits/documents/tabs/theme-style-images.php:21
#: core/kits/documents/tabs/theme-style-images.php:51
#: includes/controls/groups/background.php:623
#: includes/widgets/image-gallery.php:278 assets/js/ai-admin.js:3597
#: assets/js/ai-gutenberg.js:3735 assets/js/ai-media-library.js:3597
#: assets/js/ai-unify-product-images.js:3597 assets/js/ai.js:4418
msgid "Images"
msgstr "Obrazki"

#: includes/widgets/image-carousel.php:363
#: includes/widgets/image-gallery.php:190 includes/widgets/image.php:197
msgid "Media File"
msgstr "Plik multimedialny"

#: includes/controls/gallery.php:94 includes/widgets/image-carousel.php:162
#: includes/widgets/image-gallery.php:137
msgid "Add Images"
msgstr "Dodaj obrazki"

#: includes/widgets/counter.php:235
msgid "Cool Number"
msgstr "Animowany licznik"

#: includes/controls/media.php:189 includes/widgets/image-box.php:117
#: includes/widgets/image.php:133 includes/widgets/testimonial.php:152
#: includes/widgets/video.php:653
#: modules/link-in-bio/base/widget-link-in-bio-base.php:252
#: modules/link-in-bio/base/widget-link-in-bio-base.php:343
#: modules/link-in-bio/base/widget-link-in-bio-base.php:932
#: modules/link-in-bio/base/widget-link-in-bio-base.php:987
msgid "Choose Image"
msgstr "Wybierz obrazek"

#: includes/widgets/image-carousel.php:202
msgid "Slides to Scroll"
msgstr "Slajdy do przewijania"

#: includes/widgets/accordion.php:352 includes/widgets/accordion.php:451
#: includes/widgets/image-carousel.php:719 includes/widgets/tabs.php:380
#: includes/widgets/toggle.php:385 includes/widgets/toggle.php:475
msgid "Active Color"
msgstr "Aktywny kolor"

#: includes/widgets/accordion.php:147
msgid "Accordion Content"
msgstr "Treść akordeonu"

#: includes/widgets/accordion.php:134
msgid "Accordion Title"
msgstr "Tytuł akordeonu"

#: core/base/traits/shared-widget-controls-trait.php:207
#: includes/controls/groups/border.php:90 includes/widgets/accordion.php:303
#: includes/widgets/social-icons.php:562 includes/widgets/tabs.php:334
#: includes/widgets/toggle.php:305 includes/widgets/traits/button-trait.php:430
#: modules/floating-buttons/base/widget-floating-bars-base.php:722
#: modules/floating-buttons/base/widget-floating-bars-base.php:814
#: modules/nested-accordion/widgets/nested-accordion.php:519
#: modules/nested-tabs/widgets/nested-tabs.php:527
#: modules/nested-tabs/widgets/nested-tabs.php:585
#: modules/nested-tabs/widgets/nested-tabs.php:669
#: modules/nested-tabs/widgets/nested-tabs.php:1021
msgid "Border Color"
msgstr "Kolor obramowania"

#: core/kits/documents/tabs/settings-lightbox.php:130
#: core/kits/documents/tabs/theme-style-form-fields.php:200
#: includes/controls/groups/background.php:174 includes/widgets/alert.php:218
#: includes/widgets/image.php:622 includes/widgets/progress.php:320
#: includes/widgets/tabs.php:345 includes/widgets/video.php:894
#: modules/floating-buttons/base/widget-contact-button-base.php:1237
#: modules/floating-buttons/base/widget-contact-button-base.php:1289
#: modules/floating-buttons/base/widget-contact-button-base.php:1330
#: modules/floating-buttons/base/widget-contact-button-base.php:1376
#: modules/floating-buttons/base/widget-contact-button-base.php:1692
#: modules/floating-buttons/base/widget-contact-button-base.php:1962
#: modules/floating-buttons/base/widget-contact-button-base.php:1998
#: modules/floating-buttons/base/widget-contact-button-base.php:2031
#: modules/floating-buttons/base/widget-contact-button-base.php:2135
#: modules/floating-buttons/base/widget-contact-button-base.php:2161
#: modules/floating-buttons/base/widget-contact-button-base.php:2370
#: modules/floating-buttons/base/widget-contact-button-base.php:2657
#: modules/floating-buttons/base/widget-contact-button-base.php:2726
#: modules/floating-buttons/base/widget-contact-button-base.php:2813
#: modules/floating-buttons/base/widget-contact-button-base.php:2827
#: modules/floating-buttons/base/widget-floating-bars-base.php:674
#: modules/floating-buttons/base/widget-floating-bars-base.php:708
#: modules/floating-buttons/base/widget-floating-bars-base.php:1110
#: modules/floating-buttons/base/widget-floating-bars-base.php:1155
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1128
#: modules/nested-tabs/widgets/nested-tabs.php:511
#: modules/nested-tabs/widgets/nested-tabs.php:569
#: modules/nested-tabs/widgets/nested-tabs.php:653
#: modules/nested-tabs/widgets/nested-tabs.php:1008 assets/js/ai-admin.js:14057
#: assets/js/ai-admin.js:14775 assets/js/ai-gutenberg.js:14275
#: assets/js/ai-gutenberg.js:14993 assets/js/ai-media-library.js:14057
#: assets/js/ai-media-library.js:14775
#: assets/js/ai-unify-product-images.js:14057
#: assets/js/ai-unify-product-images.js:14775 assets/js/ai.js:15609
#: assets/js/ai.js:16327
msgid "Background Color"
msgstr "Kolor tła"

#: includes/widgets/accordion.php:46 includes/widgets/accordion.php:123
#: includes/widgets/accordion.php:273
#: modules/nested-accordion/widgets/nested-accordion.php:39
#: modules/nested-accordion/widgets/nested-accordion.php:398
msgid "Accordion"
msgstr "Akordeon"

#: includes/elements/section.php:337 includes/elements/section.php:377
msgid "Minimum Height"
msgstr "Minimalna wysokość"

#: includes/elements/section.php:326 includes/elements/section.php:366
msgid "Fit To Screen"
msgstr "Dopasuj do ekranu"

#: includes/elements/section.php:288
msgid "No Gap"
msgstr "Bez odstępów"

#: includes/elements/section.php:401
msgid "Column Position"
msgstr "Pozycja kolumny"

#: includes/elements/container.php:485 includes/elements/section.php:327
#: includes/elements/section.php:367
msgid "Min Height"
msgstr "Minimalna wysokość"

#: includes/widgets/video.php:974
msgid "Content Position"
msgstr "Pozycja treści"

#: core/kits/documents/tabs/settings-layout.php:55
#: includes/elements/container.php:392 includes/elements/section.php:249
#: includes/widgets/video.php:954
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1590
msgid "Content Width"
msgstr "Szerokość treści"

#: includes/elements/container.php:397 includes/elements/section.php:254
#: includes/widgets/common-base.php:342 includes/widgets/icon-list.php:216
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1554
msgid "Full Width"
msgstr "Pełna szerokość"

#: includes/elements/column.php:161
msgid "Column Width"
msgstr "Szerokość kolumny"

#: includes/elements/column.php:731 includes/elements/section.php:1187
msgid "Text Align"
msgstr "Wyrównanie tekstu"

#: includes/elements/column.php:707 includes/elements/section.php:1163
#: includes/widgets/heading.php:389 includes/widgets/text-editor.php:358
#: includes/widgets/text-editor.php:378
msgid "Link Color"
msgstr "Kolor odnośnika"

#: includes/elements/column.php:683 includes/elements/section.php:1139
msgid "Heading Color"
msgstr "Kolor nagłówka"

#: includes/editor-templates/hotkeys.php:165
#: includes/editor-templates/panel.php:93
msgid "Responsive Mode"
msgstr "Tryb responsywny"

#: includes/controls/repeater.php:178
#: modules/floating-buttons/base/widget-contact-button-base.php:1007
#: modules/floating-buttons/base/widget-floating-bars-base.php:367
#: modules/nested-accordion/widgets/nested-accordion.php:166
msgid "Add Item"
msgstr "Dodaj element"

#: core/kits/documents/tabs/settings-lightbox.php:163
#: core/kits/documents/tabs/theme-style-buttons.php:98
#: core/kits/documents/tabs/theme-style-buttons.php:173
#: core/kits/documents/tabs/theme-style-form-fields.php:176
#: core/kits/documents/tabs/theme-style-typography.php:55
#: includes/elements/column.php:695 includes/elements/section.php:1151
#: includes/widgets/alert.php:270 includes/widgets/alert.php:310
#: includes/widgets/counter.php:499 includes/widgets/counter.php:553
#: includes/widgets/heading.php:366 includes/widgets/image-carousel.php:874
#: includes/widgets/image-gallery.php:400 includes/widgets/image.php:607
#: includes/widgets/progress.php:255 includes/widgets/star-rating.php:258
#: includes/widgets/testimonial.php:280 includes/widgets/testimonial.php:378
#: includes/widgets/testimonial.php:423 includes/widgets/text-editor.php:343
#: includes/widgets/traits/button-trait.php:348
#: includes/widgets/traits/button-trait.php:401
#: modules/floating-buttons/base/widget-contact-button-base.php:1583
#: modules/floating-buttons/base/widget-contact-button-base.php:1615
#: modules/floating-buttons/base/widget-contact-button-base.php:1746
#: modules/floating-buttons/base/widget-contact-button-base.php:1777
#: modules/floating-buttons/base/widget-contact-button-base.php:1808
#: modules/floating-buttons/base/widget-contact-button-base.php:2116
#: modules/floating-buttons/base/widget-contact-button-base.php:2323
#: modules/floating-buttons/base/widget-contact-button-base.php:2351
#: modules/floating-buttons/base/widget-contact-button-base.php:2644
#: modules/floating-buttons/base/widget-contact-button-base.php:2713
#: modules/floating-buttons/base/widget-floating-bars-base.php:663
#: modules/floating-buttons/base/widget-floating-bars-base.php:697
#: modules/floating-buttons/base/widget-floating-bars-base.php:1381
#: modules/floating-buttons/base/widget-floating-bars-base.php:1401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1108
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1304
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1332
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1360
msgid "Text Color"
msgstr "Kolor tekstu"

#: core/common/modules/finder/categories/site.php:64
msgid "Themes"
msgstr "Motywy"

#: core/common/modules/finder/categories/general.php:55
#: modules/system-info/module.php:157
#: modules/system-info/system-info-menu-item.php:29
#: modules/system-info/system-info-menu-item.php:33
msgid "System Info"
msgstr "Informacje systemowe"

#: modules/system-info/module.php:160 modules/system-info/module.php:184
msgid "Download System Info"
msgstr "Pobierz informacje systemowe"

#: includes/settings/settings.php:271
msgid "Post Types"
msgstr "Typy treści"

#: includes/widgets/toggle.php:134
msgid "Toggle Title"
msgstr "Tytuł przełącznika"

#: includes/widgets/menu-anchor.php:43 includes/widgets/menu-anchor.php:108
msgid "Menu Anchor"
msgstr "Kotwica menu"

#: core/document-types/page-base.php:144
#: core/kits/documents/tabs/theme-style-buttons.php:243
#: core/kits/documents/tabs/theme-style-form-fields.php:159
#: includes/elements/column.php:785 includes/elements/container.php:1390
#: includes/elements/section.php:1246 includes/widgets/accordion.php:394
#: includes/widgets/accordion.php:540 includes/widgets/common-base.php:322
#: includes/widgets/divider.php:870 includes/widgets/icon-box.php:531
#: includes/widgets/icon.php:343 includes/widgets/social-icons.php:422
#: includes/widgets/toggle.php:418 includes/widgets/toggle.php:564
#: includes/widgets/traits/button-trait.php:502
#: modules/floating-buttons/base/widget-contact-button-base.php:1461
#: modules/floating-buttons/base/widget-contact-button-base.php:2186
#: modules/floating-buttons/base/widget-contact-button-base.php:2201
#: modules/floating-buttons/base/widget-contact-button-base.php:2749
#: modules/floating-buttons/base/widget-contact-button-base.php:2889
#: modules/floating-buttons/base/widget-floating-bars-base.php:857
#: modules/floating-buttons/base/widget-floating-bars-base.php:1241
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1179
#: modules/nested-accordion/widgets/nested-accordion.php:479
#: modules/nested-accordion/widgets/nested-accordion.php:543
#: modules/nested-tabs/widgets/nested-tabs.php:706
#: modules/nested-tabs/widgets/nested-tabs.php:1056
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:119
msgid "Padding"
msgstr "Dopełnienie"

#: includes/widgets/image-gallery.php:267
msgid "Random"
msgstr "Losowo"

#: core/kits/documents/tabs/settings-layout.php:24
#: includes/elements/column.php:118 includes/elements/container.php:1370
#: includes/elements/section.php:231 includes/managers/controls.php:338
#: includes/managers/elements.php:284 includes/widgets/common-base.php:292
#: includes/widgets/icon-list.php:117
#: modules/floating-buttons/base/widget-contact-button-base.php:2928
#: modules/floating-buttons/base/widget-floating-bars-base.php:1425
#: modules/nested-accordion/widgets/nested-accordion.php:112
#: assets/js/editor.js:37509
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:9
msgid "Layout"
msgstr "Układ"

#: includes/controls/structure.php:65
msgid "Reset"
msgstr "Resetuj"

#: core/admin/admin.php:242
#: core/editor/loader/v1/templates/editor-body-v1-view.php:23
#: core/editor/loader/v2/templates/editor-body-v2-view.php:23
#: includes/editor-templates/templates.php:54 modules/gutenberg/module.php:123
#: assets/js/ai-admin.js:1739 assets/js/ai-gutenberg.js:1877
#: assets/js/ai-media-library.js:1739 assets/js/ai-unify-product-images.js:1739
#: assets/js/ai.js:2527 assets/js/app-packages.js:4172 assets/js/app.js:1219
msgid "Loading"
msgstr "Wczytywanie"

#: includes/widgets/divider.php:46 includes/widgets/divider.php:378
#: includes/widgets/divider.php:528 includes/widgets/divider.php:578
#: includes/widgets/icon-list.php:282
#: modules/atomic-widgets/elements/atomic-divider/atomic-divider.php:37
msgid "Divider"
msgstr "Rozdzielacz"

#: core/kits/manager.php:139 includes/controls/groups/background.php:329
#: includes/controls/groups/background.php:487
#: includes/controls/groups/flex-item.php:24
#: includes/controls/groups/flex-item.php:93
#: includes/controls/groups/flex-item.php:142
#: includes/controls/groups/image-size.php:383
#: includes/elements/container.php:1438 includes/elements/container.php:1482
#: includes/elements/section.php:293 includes/maintenance-mode.php:238
#: includes/widgets/common-base.php:344 includes/widgets/common-base.php:416
#: includes/widgets/common-base.php:460 includes/widgets/common-base.php:1140
#: includes/widgets/common-base.php:1197
#: includes/widgets/image-carousel.php:775
#: includes/widgets/image-gallery.php:290 includes/widgets/social-icons.php:213
#: includes/widgets/social-icons.php:366
#: modules/floating-buttons/base/widget-contact-button-base.php:1214
#: modules/floating-buttons/base/widget-contact-button-base.php:1266
#: modules/floating-buttons/base/widget-contact-button-base.php:1353
#: modules/floating-buttons/base/widget-contact-button-base.php:1562
#: modules/floating-buttons/base/widget-contact-button-base.php:1728
#: modules/floating-buttons/base/widget-contact-button-base.php:2292
#: modules/floating-buttons/base/widget-contact-button-base.php:2618
#: modules/floating-buttons/base/widget-contact-button-base.php:2687
#: modules/floating-buttons/base/widget-contact-button-base.php:2818
#: modules/shapes/module.php:52 assets/js/editor.js:46082
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:15
#: assets/js/packages/editor-controls/editor-controls.strings.js:54
#: assets/js/packages/editor-controls/editor-controls.strings.js:191
#: assets/js/packages/editor-controls/editor-controls.strings.js:207
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:172
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:179
msgid "Custom"
msgstr "Własne"

#: includes/base/element-base.php:1326 includes/base/element-base.php:1354
#: includes/controls/groups/flex-container.php:101
#: includes/controls/groups/flex-container.php:137
#: includes/controls/groups/flex-item.php:59
#: includes/controls/groups/grid-container.php:139
#: includes/controls/groups/grid-container.php:167
#: includes/elements/column.php:217 includes/elements/column.php:739
#: includes/elements/section.php:1195 includes/widgets/common-base.php:522
#: includes/widgets/counter.php:322 includes/widgets/counter.php:396
#: includes/widgets/counter.php:432 includes/widgets/divider.php:478
#: includes/widgets/divider.php:774 includes/widgets/divider.php:940
#: includes/widgets/heading.php:274 includes/widgets/icon-box.php:312
#: includes/widgets/icon-list.php:267 includes/widgets/icon-list.php:549
#: includes/widgets/icon-list.php:580 includes/widgets/icon.php:193
#: includes/widgets/image-box.php:276 includes/widgets/image-carousel.php:751
#: includes/widgets/image-carousel.php:852
#: includes/widgets/image-gallery.php:375 includes/widgets/image.php:267
#: includes/widgets/image.php:585 includes/widgets/rating.php:207
#: includes/widgets/social-icons.php:332 includes/widgets/star-rating.php:223
#: includes/widgets/tabs.php:213 includes/widgets/tabs.php:243
#: includes/widgets/tabs.php:430 includes/widgets/testimonial.php:251
#: includes/widgets/text-editor.php:266
#: includes/widgets/traits/button-trait.php:260
#: includes/widgets/traits/button-trait.php:292 includes/widgets/video.php:978
#: modules/floating-buttons/base/widget-contact-button-base.php:2944
#: modules/floating-buttons/base/widget-floating-bars-base.php:1193
#: modules/nested-accordion/widgets/nested-accordion.php:182
#: modules/nested-tabs/widgets/nested-tabs.php:243
#: modules/nested-tabs/widgets/nested-tabs.php:285
#: modules/nested-tabs/widgets/nested-tabs.php:355
#: modules/shapes/widgets/text-path.php:181
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:96
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:163
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:199
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:204
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:209
msgid "Center"
msgstr "Wyśrodkowanie"

#: core/base/traits/shared-widget-controls-trait.php:179
#: includes/controls/groups/border.php:77 includes/widgets/accordion.php:281
#: includes/widgets/divider.php:1002 includes/widgets/icon-box.php:595
#: includes/widgets/icon.php:392 includes/widgets/tabs.php:311
#: includes/widgets/text-editor.php:551 includes/widgets/toggle.php:284
#: modules/floating-buttons/base/widget-floating-bars-base.php:777
#: modules/nested-accordion/widgets/nested-accordion.php:522
#: modules/nested-tabs/widgets/nested-tabs.php:530
#: modules/nested-tabs/widgets/nested-tabs.php:588
#: modules/nested-tabs/widgets/nested-tabs.php:672
#: modules/nested-tabs/widgets/nested-tabs.php:1024
msgid "Border Width"
msgstr "Grubość obramowania"

#: includes/widgets/image-gallery.php:191
msgid "Attachment Page"
msgstr "Strona załącznika"

#: includes/widgets/divider.php:836 includes/widgets/icon-box.php:134
#: includes/widgets/icon.php:135 includes/widgets/text-editor.php:425
msgid "Stacked"
msgstr "Ułożone"

#: core/dynamic-tags/tag.php:105 includes/widgets/counter.php:283
#: modules/nested-tabs/widgets/nested-tabs.php:208
#: modules/nested-tabs/widgets/nested-tabs.php:870
msgid "After"
msgstr "Po"

#: includes/elements/container.php:396 includes/elements/section.php:253
msgid "Boxed"
msgstr "Opakowane"

#: includes/managers/elements.php:288
msgid "Basic"
msgstr "Podstawowe"

#: includes/elements/column.php:747 includes/elements/section.php:1203
#: includes/widgets/heading.php:282 includes/widgets/icon-box.php:320
#: includes/widgets/image-box.php:284 includes/widgets/image-carousel.php:860
#: includes/widgets/image-gallery.php:383 includes/widgets/image.php:593
#: includes/widgets/star-rating.php:231 includes/widgets/text-editor.php:274
msgid "Justified"
msgstr "Wyjustowane"

#: core/base/traits/shared-widget-controls-trait.php:270
#: includes/base/element-base.php:1386 includes/editor-templates/panel.php:277
#: assets/js/kit-library.ed7fc5d9656556af9353.bundle.js:5222
#: assets/js/packages/editor-responsive/editor-responsive.js:2
#: assets/js/packages/editor-responsive/editor-responsive.strings.js:1
msgid "Desktop"
msgstr "Komputer stacjonarny"

#: includes/widgets/image-box.php:212 includes/widgets/testimonial.php:217
msgid "Image Position"
msgstr "Pozycja obrazka"

#: includes/widgets/icon-list.php:145 includes/widgets/icon-list.php:146
msgid "List Item"
msgstr "Element listy"

#: includes/widgets/menu-anchor.php:115
msgid "The ID of Menu Anchor."
msgstr "Identyfikator kotwicy menu."

#: includes/widgets/tabs.php:46 includes/widgets/tabs.php:118
#: includes/widgets/tabs.php:267 modules/nested-tabs/widgets/nested-tabs.php:34
#: modules/nested-tabs/widgets/nested-tabs.php:112
#: modules/nested-tabs/widgets/nested-tabs.php:449
msgid "Tabs"
msgstr "Karty"

#: includes/widgets/divider.php:837 includes/widgets/icon-box.php:135
#: includes/widgets/icon.php:136 includes/widgets/text-editor.php:426
msgid "Framed"
msgstr "Obramowane"

#: core/document-types/post.php:65
msgid "Posts"
msgstr "Wpisy"

#: includes/elements/section.php:291
msgid "Wide"
msgstr "Szeroki"

#: includes/widgets/accordion.php:185 includes/widgets/accordion.php:408
#: includes/widgets/alert.php:173 includes/widgets/divider.php:509
#: includes/widgets/divider.php:561 includes/widgets/divider.php:821
#: includes/widgets/icon-box.php:117 includes/widgets/icon-box.php:390
#: includes/widgets/icon-list.php:156 includes/widgets/icon-list.php:415
#: includes/widgets/icon.php:44 includes/widgets/icon.php:111
#: includes/widgets/icon.php:118 includes/widgets/icon.php:177
#: includes/widgets/rating.php:52 includes/widgets/rating.php:177
#: includes/widgets/social-icons.php:116 includes/widgets/social-icons.php:353
#: includes/widgets/star-rating.php:168 includes/widgets/toggle.php:188
#: includes/widgets/toggle.php:432 includes/widgets/traits/button-trait.php:126
#: includes/widgets/video.php:698
#: modules/floating-buttons/base/widget-contact-button-base.php:495
#: modules/floating-buttons/base/widget-floating-bars-base.php:113
#: modules/floating-buttons/base/widget-floating-bars-base.php:181
#: modules/floating-buttons/base/widget-floating-bars-base.php:325
#: modules/floating-buttons/base/widget-floating-bars-base.php:399
#: modules/floating-buttons/base/widget-floating-bars-base.php:1267
#: modules/nested-accordion/widgets/nested-accordion.php:210
#: modules/nested-accordion/widgets/nested-accordion.php:600
#: modules/nested-tabs/widgets/nested-tabs.php:131
#: modules/nested-tabs/widgets/nested-tabs.php:852
msgid "Icon"
msgstr "Ikonka"

#: includes/widgets/counter.php:491
#: modules/floating-buttons/base/widget-contact-button-base.php:333
#: modules/floating-buttons/base/widget-contact-button-base.php:887
#: modules/link-in-bio/base/widget-link-in-bio-base.php:483
#: modules/link-in-bio/base/widget-link-in-bio-base.php:731
msgid "Number"
msgstr "Liczba"

#: includes/elements/section.php:289
msgid "Narrow"
msgstr "Wąsko"

#: includes/widgets/divider.php:915 includes/widgets/icon-box.php:426
#: includes/widgets/icon-box.php:468 includes/widgets/icon.php:237
#: includes/widgets/icon.php:277 includes/widgets/social-icons.php:235
#: includes/widgets/social-icons.php:388 includes/widgets/social-icons.php:546
#: includes/widgets/text-editor.php:451
msgid "Secondary Color"
msgstr "Kolor drugorzędny"

#: includes/widgets/alert.php:46 includes/widgets/alert.php:111
#: includes/widgets/alert.php:210
msgid "Alert"
msgstr "Ostrzeżenie"

#: core/base/document.php:1963
#: core/common/modules/finder/categories/settings.php:49
msgid "General Settings"
msgstr "Ustawienia ogólne"

#: modules/nested-tabs/widgets/nested-tabs.php:235
#: modules/nested-tabs/widgets/nested-tabs.php:277
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:98
msgid "Justify"
msgstr "Wyjustuj"

#: modules/floating-buttons/documents/floating-buttons.php:31
msgid "Go To Dashboard"
msgstr "Przejdź do kokpitu"

#: modules/floating-buttons/base/widget-contact-button-base.php:1846
msgid "Chat Background Color"
msgstr "Kolor tła czatu"

#: modules/system-info/module.php:202
msgid "You do not have permission to download this file."
msgstr "Nie masz uprawnień do pobrania tego pliku."

#: includes/base/element-base.php:1322 includes/controls/dimensions.php:85
#: includes/elements/column.php:735 includes/elements/container.php:1548
#: includes/elements/section.php:1191 includes/widgets/common-base.php:568
#: includes/widgets/common-base.php:569 includes/widgets/divider.php:474
#: includes/widgets/divider.php:770 includes/widgets/divider.php:936
#: includes/widgets/heading.php:270 includes/widgets/icon-box.php:243
#: includes/widgets/icon-box.php:308 includes/widgets/icon-list.php:263
#: includes/widgets/icon-list.php:545 includes/widgets/icon.php:189
#: includes/widgets/image-box.php:217 includes/widgets/image-box.php:272
#: includes/widgets/image-carousel.php:549
#: includes/widgets/image-carousel.php:848
#: includes/widgets/image-gallery.php:371 includes/widgets/image.php:263
#: includes/widgets/image.php:581 includes/widgets/social-icons.php:328
#: includes/widgets/star-rating.php:219 includes/widgets/tabs.php:426
#: includes/widgets/testimonial.php:247 includes/widgets/text-editor.php:262
#: includes/widgets/traits/button-trait.php:256
#: modules/floating-buttons/base/widget-contact-button-base.php:1136
#: modules/floating-buttons/base/widget-contact-button-base.php:2402
#: modules/floating-buttons/base/widget-contact-button-base.php:2940
#: modules/floating-buttons/base/widget-floating-bars-base.php:452
#: modules/floating-buttons/base/widget-floating-bars-base.php:1053
#: modules/shapes/widgets/text-path.php:177
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:22
#: assets/js/packages/editor-controls/editor-controls.strings.js:26
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:149
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:150
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:222
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:226
msgid "Left"
msgstr "Lewo"

#: includes/base/element-base.php:1358 includes/controls/dimensions.php:84
#: includes/elements/column.php:190 includes/elements/container.php:1184
#: includes/elements/container.php:1668 includes/elements/section.php:408
#: includes/elements/section.php:427 includes/elements/section.php:965
#: includes/widgets/common-base.php:685 includes/widgets/counter.php:355
#: includes/widgets/icon-box.php:255 includes/widgets/icon-box.php:281
#: includes/widgets/image-box.php:252
#: modules/floating-buttons/base/widget-contact-button-base.php:3002
#: modules/floating-buttons/base/widget-floating-bars-base.php:1441
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:24
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:153
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:224
msgid "Bottom"
msgstr "Dół"

#: includes/editor-templates/hotkeys.php:128
msgid "Page Settings"
msgstr "Ustawienia strony"

#: includes/elements/section.php:283 includes/widgets/text-editor.php:202
msgid "Columns Gap"
msgstr "Odstęp kolumn"

#: includes/elements/column.php:719 includes/elements/section.php:1175
msgid "Link Hover Color"
msgstr "Kolor najechania odnośnika"

#: includes/widgets/social-icons.php:523
msgid "Icon Hover"
msgstr "Najechanie ikonki"

#: includes/widgets/image-box.php:45 includes/widgets/image-box.php:110
msgid "Image Box"
msgstr "Pole obrazka"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1651
msgid "Image Size"
msgstr "Rozmiar obrazka"

#: includes/base/element-base.php:1330 includes/controls/dimensions.php:83
#: includes/elements/column.php:743 includes/elements/container.php:1549
#: includes/elements/section.php:1199 includes/widgets/common-base.php:568
#: includes/widgets/common-base.php:569 includes/widgets/divider.php:482
#: includes/widgets/divider.php:778 includes/widgets/divider.php:944
#: includes/widgets/heading.php:278 includes/widgets/icon-box.php:251
#: includes/widgets/icon-box.php:316 includes/widgets/icon-list.php:271
#: includes/widgets/icon-list.php:553 includes/widgets/icon.php:197
#: includes/widgets/image-box.php:225 includes/widgets/image-box.php:280
#: includes/widgets/image-carousel.php:550
#: includes/widgets/image-carousel.php:856
#: includes/widgets/image-gallery.php:379 includes/widgets/image.php:271
#: includes/widgets/image.php:589 includes/widgets/social-icons.php:336
#: includes/widgets/star-rating.php:227 includes/widgets/tabs.php:434
#: includes/widgets/testimonial.php:255 includes/widgets/text-editor.php:270
#: includes/widgets/traits/button-trait.php:264
#: modules/floating-buttons/base/widget-contact-button-base.php:1140
#: modules/floating-buttons/base/widget-contact-button-base.php:2406
#: modules/floating-buttons/base/widget-contact-button-base.php:2948
#: modules/floating-buttons/base/widget-floating-bars-base.php:456
#: modules/floating-buttons/base/widget-floating-bars-base.php:1057
#: modules/shapes/widgets/text-path.php:185
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:23
#: assets/js/packages/editor-controls/editor-controls.strings.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:148
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:151
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:223
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:225
msgid "Right"
msgstr "Prawo"

#: includes/base/element-base.php:1350 includes/controls/dimensions.php:82
#: includes/elements/column.php:188 includes/elements/container.php:1183
#: includes/elements/container.php:1664 includes/elements/section.php:406
#: includes/elements/section.php:425 includes/elements/section.php:964
#: includes/widgets/common-base.php:681 includes/widgets/counter.php:347
#: includes/widgets/icon-box.php:247 includes/widgets/icon-box.php:273
#: includes/widgets/image-box.php:221 includes/widgets/image-box.php:244
#: includes/widgets/testimonial.php:226 includes/widgets/video.php:979
#: modules/floating-buttons/base/widget-contact-button-base.php:2994
#: modules/floating-buttons/base/widget-floating-bars-base.php:1437
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:21
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:152
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:221
msgid "Top"
msgstr "Góra"

#: includes/widgets/image-carousel.php:456
msgid "Pause on Hover"
msgstr "Wstrzymaj po najechaniu"

#: includes/widgets/image-carousel.php:489
msgid "Autoplay Speed"
msgstr "Szybkość auto odtwarzania"

#: includes/widgets/tabs.php:142 includes/widgets/tabs.php:143
msgid "Tab Content"
msgstr "Treść karty"

#: includes/widgets/tabs.php:129 includes/widgets/tabs.php:130
#: modules/nested-tabs/widgets/nested-tabs.php:120
#: modules/nested-tabs/widgets/nested-tabs.php:121
msgid "Tab Title"
msgstr "Tytuł karty"

#: includes/widgets/toggle.php:147
msgid "Toggle Content"
msgstr "Przełącz treść"

#: includes/controls/groups/background.php:433
msgctxt "Background Control"
msgid "Scroll"
msgstr "Przewiń"

#: includes/controls/groups/background.php:434
msgctxt "Background Control"
msgid "Fixed"
msgstr "Stałe"

#: includes/controls/groups/background.php:457
msgctxt "Background Control"
msgid "Repeat"
msgstr "Powtórz"

#: includes/controls/groups/border.php:65 includes/widgets/divider.php:342
#: includes/widgets/icon-list.php:299 includes/widgets/star-rating.php:188
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:230
msgid "Solid"
msgstr "Jednolita"

#: includes/controls/groups/border.php:66 includes/widgets/divider.php:343
#: includes/widgets/icon-list.php:300
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:233
msgid "Double"
msgstr "Podwójna"

#: core/kits/documents/tabs/theme-style-typography.php:109
#: includes/elements/container.php:611 includes/widgets/audio.php:111
#: includes/widgets/heading.php:200 includes/widgets/icon-box.php:195
#: includes/widgets/icon-list.php:169 includes/widgets/icon.php:164
#: includes/widgets/image-box.php:170 includes/widgets/image-carousel.php:358
#: includes/widgets/image-carousel.php:372
#: includes/widgets/image-gallery.php:186 includes/widgets/image.php:192
#: includes/widgets/image.php:209 includes/widgets/social-icons.php:194
#: includes/widgets/testimonial.php:204
#: includes/widgets/traits/button-trait.php:99 includes/widgets/video.php:172
#: includes/widgets/video.php:197 includes/widgets/video.php:221
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:79
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:113
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:84
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:79
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:73
#: modules/atomic-widgets/elements/div-block/div-block.php:100
#: modules/atomic-widgets/elements/flexbox/flexbox.php:100
#: modules/floating-buttons/base/widget-contact-button-base.php:418
#: modules/floating-buttons/base/widget-contact-button-base.php:933
#: modules/floating-buttons/base/widget-contact-button-base.php:1061
#: modules/floating-buttons/base/widget-floating-bars-base.php:164
#: modules/floating-buttons/base/widget-floating-bars-base.php:350
#: modules/floating-buttons/base/widget-floating-bars-base.php:568
#: modules/link-in-bio/base/widget-link-in-bio-base.php:264
#: modules/link-in-bio/base/widget-link-in-bio-base.php:407
#: modules/link-in-bio/base/widget-link-in-bio-base.php:643
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1099
#: modules/shapes/widgets/text-path.php:158
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:27
msgid "Link"
msgstr "Połącz"

#: includes/controls/groups/background.php:428
msgctxt "Background Control"
msgid "Attachment"
msgstr "Przywiązanie"

#: includes/controls/groups/border.php:68 includes/widgets/divider.php:345
#: includes/widgets/icon-list.php:302
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:231
msgid "Dashed"
msgstr "Przerywana"

#: includes/controls/groups/border.php:67 includes/widgets/divider.php:344
#: includes/widgets/icon-list.php:301
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:232
msgid "Dotted"
msgstr "Kropkowana"

#: includes/controls/icon.php:876 includes/controls/icon.php:877
msgid "Select Icon"
msgstr "Wybierz ikonkę"

#: core/kits/documents/tabs/theme-style-buttons.php:91
#: core/kits/documents/tabs/theme-style-form-fields.php:117
#: core/kits/documents/tabs/theme-style-images.php:63
#: core/kits/documents/tabs/theme-style-typography.php:119
#: includes/base/element-base.php:883
#: includes/controls/groups/typography.php:152
#: includes/controls/groups/typography.php:168
#: includes/controls/groups/typography.php:178 includes/elements/column.php:286
#: includes/elements/column.php:401 includes/elements/column.php:450
#: includes/elements/column.php:557 includes/elements/column.php:893
#: includes/elements/container.php:658 includes/elements/container.php:787
#: includes/elements/container.php:861 includes/elements/container.php:1006
#: includes/elements/container.php:1839 includes/elements/section.php:543
#: includes/elements/section.php:654 includes/elements/section.php:718
#: includes/elements/section.php:841 includes/elements/section.php:1334
#: includes/widgets/alert.php:412 includes/widgets/common-base.php:854
#: includes/widgets/common-base.php:901 includes/widgets/common-base.php:976
#: includes/widgets/google-maps.php:221 includes/widgets/heading.php:327
#: includes/widgets/heading.php:359 includes/widgets/icon-box.php:403
#: includes/widgets/icon-box.php:674 includes/widgets/icon-list.php:425
#: includes/widgets/icon-list.php:654 includes/widgets/icon.php:213
#: includes/widgets/image-box.php:489 includes/widgets/image-box.php:638
#: includes/widgets/image.php:434 includes/widgets/text-editor.php:336
#: includes/widgets/traits/button-trait.php:340
#: modules/floating-buttons/base/widget-contact-button-base.php:1201
#: modules/floating-buttons/base/widget-contact-button-base.php:1407
#: modules/floating-buttons/base/widget-contact-button-base.php:1979
#: modules/floating-buttons/base/widget-contact-button-base.php:2474
#: modules/floating-buttons/base/widget-contact-button-base.php:2605
#: modules/floating-buttons/base/widget-contact-button-base.php:2790
#: modules/floating-buttons/base/widget-floating-bars-base.php:656
#: modules/floating-buttons/base/widget-floating-bars-base.php:888
#: modules/floating-buttons/base/widget-floating-bars-base.php:1374
#: modules/nested-accordion/widgets/nested-accordion.php:676
#: modules/nested-accordion/widgets/nested-accordion.php:726
#: modules/nested-tabs/widgets/nested-tabs.php:498
#: modules/nested-tabs/widgets/nested-tabs.php:742
#: modules/nested-tabs/widgets/nested-tabs.php:942
#: modules/shapes/widgets/text-path.php:409
#: modules/shapes/widgets/text-path.php:501
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:112
msgid "Normal"
msgstr "Normalna"

#: includes/controls/groups/typography.php:160
msgctxt "Typography Control"
msgid "Transform"
msgstr "Przekształć"

#: includes/controls/groups/typography.php:138
#: includes/controls/groups/typography.php:299
msgctxt "Typography Control"
msgid "Weight"
msgstr "Grubość"

#: core/breakpoints/manager.php:319
msgid "Mobile Landscape"
msgstr "Mobilny poziomy"

#: includes/editor-templates/panel.php:71
#: includes/editor-templates/panel.php:72
msgid "Widgets Panel"
msgstr "Panel widżetów"

#: includes/editor-templates/panel-elements.php:75
msgid "Search Widget..."
msgstr "Widżet wyszukiwania..."

#: includes/editor-templates/global.php:34
msgid "Add New Section"
msgstr "Utwórz sekcję"

#: core/breakpoints/manager.php:314
msgid "Mobile Portrait"
msgstr "Mobilny pionowy"

#: includes/controls/groups/flex-container.php:214
#: includes/controls/groups/grid-container.php:196
#: includes/controls/groups/grid-container.php:236
#: includes/elements/column.php:189 includes/elements/section.php:407
#: includes/elements/section.php:426 includes/widgets/counter.php:351
#: includes/widgets/icon-box.php:277 includes/widgets/image-box.php:248
#: modules/floating-buttons/base/widget-contact-button-base.php:2998
msgid "Middle"
msgstr "Środek"

#: core/experiments/manager.php:392 core/experiments/manager.php:682
#: modules/element-cache/module.php:108 assets/js/editor.js:28023
#: assets/js/element-manager-admin.js:597
msgid "Inactive"
msgstr "Wyłączony"

#: core/admin/admin-notices.php:658 includes/controls/gallery.php:123
#: includes/controls/media.php:319 includes/widgets/heading.php:477
msgid "Activate Plugin"
msgstr "Włącz wtyczkę"

#: core/kits/documents/tabs/global-colors.php:82
#: core/kits/documents/tabs/global-typography.php:135
msgid "Secondary"
msgstr "Drugorzędny"

#: includes/widgets/divider.php:832 includes/widgets/icon-box.php:130
#: includes/widgets/icon.php:131 includes/widgets/text-editor.php:421
#: assets/js/editor.js:10666
msgid "View"
msgstr "Widok"

#: includes/widgets/accordion.php:165
msgid "Accordion Items"
msgstr "Elementy akordeonu"

#: modules/system-info/module.php:164
msgid "Copy & Paste Info"
msgstr "Kopiuj i wklej informacje"

#: core/kits/documents/tabs/theme-style-buttons.php:152
#: core/kits/documents/tabs/theme-style-buttons.php:227
#: core/kits/documents/tabs/theme-style-form-fields.php:233
#: core/kits/documents/tabs/theme-style-images.php:83
#: core/kits/documents/tabs/theme-style-images.php:154
#: includes/elements/column.php:572 includes/elements/column.php:609
#: includes/elements/container.php:1038 includes/elements/container.php:1089
#: includes/elements/section.php:855 includes/elements/section.php:891
#: includes/widgets/common-base.php:991 includes/widgets/common-base.php:1028
#: includes/widgets/divider.php:1025 includes/widgets/icon-box.php:610
#: includes/widgets/icon.php:407 includes/widgets/image-box.php:459
#: includes/widgets/image-carousel.php:819
#: includes/widgets/image-gallery.php:342 includes/widgets/image.php:540
#: includes/widgets/progress.php:343 includes/widgets/social-icons.php:509
#: includes/widgets/testimonial.php:355 includes/widgets/text-editor.php:529
#: includes/widgets/traits/button-trait.php:489
#: modules/nested-accordion/widgets/nested-accordion.php:466
#: modules/nested-accordion/widgets/nested-accordion.php:531
#: modules/nested-tabs/widgets/nested-tabs.php:693
#: modules/nested-tabs/widgets/nested-tabs.php:1033
msgid "Border Radius"
msgstr "Promień obramowania"

#: includes/widgets/alert.php:123 includes/widgets/progress.php:173
#: includes/widgets/traits/button-trait.php:73
msgid "Success"
msgstr "Powodzenie"

#: includes/editor-templates/templates.php:190
#: includes/elements/container.php:1198 includes/elements/section.php:979
#: includes/template-library/sources/local.php:1702
#: includes/widgets/alert.php:118 includes/widgets/progress.php:168
#: includes/widgets/traits/button-trait.php:67
#: modules/floating-buttons/base/widget-floating-bars-base.php:563
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1094
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:95
msgid "Type"
msgstr "Rodzaj"

#: includes/controls/groups/background.php:292
#: includes/widgets/common-base.php:1116 includes/widgets/image-box.php:351
#: includes/widgets/image-carousel.php:735 includes/widgets/image.php:45
#: includes/widgets/image.php:126 includes/widgets/image.php:251
#: includes/widgets/testimonial.php:317
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:33
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:68
#: assets/js/packages/editor-controls/editor-controls.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:35
#: assets/js/packages/editor-controls/editor-controls.strings.js:177
msgid "Image"
msgstr "Obrazek"

#: includes/widgets/icon-box.php:237
#: includes/widgets/traits/button-trait.php:142
#: modules/floating-buttons/base/widget-contact-button-base.php:1132
#: modules/floating-buttons/base/widget-contact-button-base.php:2398
#: modules/floating-buttons/base/widget-floating-bars-base.php:576
#: modules/floating-buttons/base/widget-floating-bars-base.php:1286
msgid "Icon Position"
msgstr "Pozycja ikonki"

#: includes/widgets/divider.php:699 includes/widgets/icon-list.php:518
#: includes/widgets/image-gallery.php:286 includes/widgets/star-rating.php:291
msgid "Gap"
msgstr "Odstęp"

#: includes/widgets/divider.php:605 includes/widgets/icon-list.php:318
#: modules/floating-buttons/base/widget-contact-button-base.php:2552
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1218
msgid "Weight"
msgstr "Grubość"

#: modules/gutenberg/module.php:98
msgid "&#8592; Back to WordPress Editor"
msgstr "&#8592; Powrót do edytora WordPress"

#: core/kits/documents/tabs/global-colors.php:77
#: core/kits/documents/tabs/global-typography.php:128
msgid "Primary"
msgstr "Główny"

#: core/experiments/manager.php:391 core/experiments/manager.php:681
#: modules/element-cache/module.php:109
#: modules/floating-buttons/base/widget-contact-button-base.php:1312
#: modules/nested-accordion/widgets/nested-accordion.php:672
#: modules/nested-accordion/widgets/nested-accordion.php:735
#: modules/nested-tabs/widgets/nested-tabs.php:634
#: modules/nested-tabs/widgets/nested-tabs.php:814
#: modules/nested-tabs/widgets/nested-tabs.php:976 assets/js/editor.js:28025
#: assets/js/element-manager-admin.js:594
msgid "Active"
msgstr "Włączony"

#: core/experiments/manager.php:390
#: core/kits/documents/tabs/settings-background.php:78
#: includes/base/widget-base.php:297 includes/controls/animation.php:154
#: includes/controls/font.php:66 includes/controls/groups/background.php:319
#: includes/controls/groups/background.php:432
#: includes/controls/groups/background.php:462
#: includes/controls/groups/background.php:483
#: includes/controls/groups/background.php:688
#: includes/controls/groups/background.php:707
#: includes/controls/groups/border.php:63
#: includes/controls/groups/flex-item.php:23
#: includes/controls/groups/typography.php:151
#: includes/controls/groups/typography.php:164
#: includes/controls/groups/typography.php:177
#: includes/controls/groups/typography.php:189
#: includes/editor-templates/panel.php:248 includes/elements/column.php:187
#: includes/elements/column.php:215 includes/elements/column.php:258
#: includes/elements/container.php:561 includes/elements/container.php:584
#: includes/elements/container.php:1536 includes/elements/section.php:287
#: includes/elements/section.php:325 includes/elements/section.php:365
#: includes/elements/section.php:424 includes/elements/section.php:452
#: includes/elements/section.php:492 includes/settings/settings.php:387
#: includes/widgets/common-base.php:341 includes/widgets/common-base.php:558
#: includes/widgets/divider.php:835 includes/widgets/heading.php:217
#: includes/widgets/icon-box.php:133 includes/widgets/icon-list.php:122
#: includes/widgets/icon.php:134 includes/widgets/image-box.php:405
#: includes/widgets/image-carousel.php:188
#: includes/widgets/image-carousel.php:206
#: includes/widgets/image-carousel.php:397
#: includes/widgets/image-carousel.php:774
#: includes/widgets/image-gallery.php:210
#: includes/widgets/image-gallery.php:266
#: includes/widgets/image-gallery.php:289 includes/widgets/image.php:235
#: includes/widgets/image.php:382 includes/widgets/progress.php:171
#: includes/widgets/text-editor.php:181 includes/widgets/text-editor.php:424
#: includes/widgets/traits/button-trait.php:71
#: modules/element-cache/module.php:107
#: modules/floating-buttons/base/widget-contact-button-base.php:1213
#: modules/floating-buttons/base/widget-contact-button-base.php:1265
#: modules/floating-buttons/base/widget-contact-button-base.php:1352
#: modules/floating-buttons/base/widget-contact-button-base.php:1561
#: modules/floating-buttons/base/widget-contact-button-base.php:1727
#: modules/floating-buttons/base/widget-contact-button-base.php:2291
#: modules/floating-buttons/base/widget-contact-button-base.php:2617
#: modules/floating-buttons/base/widget-contact-button-base.php:2686
#: modules/floating-buttons/base/widget-contact-button-base.php:2817
#: modules/link-in-bio/base/widget-link-in-bio-base.php:179
#: modules/page-templates/module.php:301
#: modules/shapes/widgets/text-path.php:203 assets/js/editor.js:46074
#: assets/js/editor.js:46085
#: assets/js/packages/editor-controls/editor-controls.js:128
#: assets/js/packages/editor-controls/editor-controls.strings.js:103
msgid "Default"
msgstr "Domyślnie"

#: core/role-manager/role-manager.php:74
msgid "Exclude Roles"
msgstr "Wyklucz role"

#: includes/widgets/accordion.php:174
msgid "Accordion #2"
msgstr "Akordeon nr 2"

#: includes/widgets/accordion.php:170
msgid "Accordion #1"
msgstr "Akordeon nr 1"

#: includes/widgets/spacer.php:42 includes/widgets/spacer.php:123
msgid "Spacer"
msgstr "Odstępnik"

#: includes/widgets/counter.php:173
msgid "Number Suffix"
msgstr "Przyrostek liczby"

#: includes/widgets/counter.php:158
msgid "Number Prefix"
msgstr "Przedrostek liczby"

#: includes/widgets/counter.php:146
msgid "Ending Number"
msgstr "Liczba końcowa"

#: includes/widgets/counter.php:134
msgid "Starting Number"
msgstr "Liczba początkowa"

#: includes/widgets/icon-box.php:334
#: includes/widgets/traits/button-trait.php:175
#: modules/floating-buttons/base/widget-contact-button-base.php:1162
#: modules/floating-buttons/base/widget-contact-button-base.php:2418
#: modules/floating-buttons/base/widget-floating-bars-base.php:606
#: modules/floating-buttons/base/widget-floating-bars-base.php:1331
msgid "Icon Spacing"
msgstr "Odstęp ikonki"

#: includes/widgets/icon-box.php:45 includes/widgets/icon-box.php:110
msgid "Icon Box"
msgstr "Pole ikonki"

#: core/base/traits/shared-widget-controls-trait.php:23
#: includes/elements/column.php:264 includes/elements/container.php:590
#: includes/elements/section.php:498 includes/widgets/divider.php:538
#: includes/widgets/heading.php:234
#: modules/atomic-widgets/elements/div-block/div-block.php:98
#: modules/atomic-widgets/elements/flexbox/flexbox.php:98
msgid "HTML Tag"
msgstr "Znacznik HTML"

#: includes/widgets/google-maps.php:150
msgid "London Eye, London, United Kingdom"
msgstr "London Eye, Londyn, Wielka Brytania"

#: includes/widgets/divider.php:898 includes/widgets/icon-box.php:410
#: includes/widgets/icon-box.php:451 includes/widgets/icon.php:220
#: includes/widgets/icon.php:263 includes/widgets/social-icons.php:221
#: includes/widgets/social-icons.php:374 includes/widgets/social-icons.php:531
#: includes/widgets/text-editor.php:436
msgid "Primary Color"
msgstr "Kolor główny"

#: modules/floating-buttons/base/widget-contact-button-base.php:205
#: modules/floating-buttons/base/widget-contact-button-base.php:235
#: modules/floating-buttons/base/widget-contact-button-base.php:1319
#: modules/floating-buttons/base/widget-contact-button-base.php:2019
#: modules/floating-buttons/base/widget-contact-button-base.php:2628
#: modules/floating-buttons/base/widget-contact-button-base.php:2697
#: modules/floating-buttons/base/widget-floating-bars-base.php:1141
msgid "Icon Color"
msgstr "Kolor ikonki"

#: includes/widgets/icon-list.php:199
msgid "List Item #3"
msgstr "Element listy nr 3"

#: includes/widgets/icon-list.php:192
msgid "List Item #2"
msgstr "Element listy nr 2"

#: includes/widgets/icon-list.php:185
msgid "List Item #1"
msgstr "Element listy nr 1"

#: includes/widgets/icon-list.php:45 includes/widgets/icon-list.php:110
msgid "Icon List"
msgstr "Lista ikonki"

#: includes/widgets/progress.php:225
msgid "Web Designer"
msgstr "Projektant stron internetowych"

#: includes/widgets/progress.php:224
msgid "e.g. Web Designer"
msgstr "np. projektant stron internetowych"

#: includes/widgets/progress.php:204
msgid "Display Percentage"
msgstr "Wyświetl procent"

#: includes/widgets/progress.php:188 includes/widgets/progress.php:297
msgid "Percentage"
msgstr "Procentowo"

#: includes/widgets/menu-anchor.php:120
msgid "For Example: About"
msgstr "Na przykład: Informacje o"

#: includes/widgets/menu-anchor.php:121
msgid "This ID will be the CSS ID you will have to use in your own page, Without #."
msgstr "To identyfikator będzie identyfikatorem CSS, którego będziesz musiał używać na swojej stronie, bez znaku #."

#: includes/widgets/image-box.php:298 includes/widgets/image-carousel.php:787
msgid "Image Spacing"
msgstr "Odstępy obrazka"

#: includes/widgets/tabs.php:173
#: modules/nested-tabs/widgets/nested-tabs.php:180
msgid "Tab #2"
msgstr "Karta nr 2"

#: includes/widgets/tabs.php:169
#: modules/nested-tabs/widgets/nested-tabs.php:177
msgid "Tab #1"
msgstr "Karta nr 1"

#: includes/widgets/tabs.php:164
#: modules/nested-tabs/widgets/nested-tabs.php:172
msgid "Tabs Items"
msgstr "Elementy karty"

#: includes/widgets/video.php:682 includes/widgets/video.php:809
#: modules/floating-buttons/base/widget-floating-bars-base.php:267
msgid "Play Icon"
msgstr "Ikonka odtwarzania"

#: includes/widgets/video.php:759
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:126
msgid "Aspect Ratio"
msgstr "Proporcje obrazu"

#: includes/widgets/toggle.php:177
msgid "Toggle #2"
msgstr "Przełącznik nr 2"

#: includes/widgets/toggle.php:173
msgid "Toggle #1"
msgstr "Przełącznik nr 1"

#: includes/widgets/toggle.php:168
msgid "Toggle Items"
msgstr "Przełącz elementy"

#: includes/widgets/toggle.php:46 includes/widgets/toggle.php:123
#: includes/widgets/toggle.php:276
msgid "Toggle"
msgstr "Przełącznik"