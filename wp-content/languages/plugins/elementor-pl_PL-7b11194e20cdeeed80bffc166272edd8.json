{"translation-revision-date": "2025-09-17 12:40:44+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n == 1) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "pl"}, "Radial gradient": ["<PERSON><PERSON><PERSON> promieniowy"], "Linear gradient": ["Gradient liniowy"], "Local Class": ["<PERSON><PERSON><PERSON> lokalna"], "Transitions": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Backdrop filters": ["<PERSON><PERSON>r tła"], "Filters": ["Filtry"], "Flex wrap": ["Elastyczne owijanie"], "Flex Size": ["Rozmiar <PERSON>ast<PERSON>"], "Flex direction": ["Kierunek elastyczny"], "%s$1 %s$2 edited": ["%s$1 %s$2 edytowano"], "%s edited": ["%s edytowano"], "Has styles": ["Ma style"], "Has overridden styles": ["Ma nadpisane style"], "Has effective styles": ["Ma efektywne style"], "%s created": ["%s utworzono"], "Class": ["<PERSON><PERSON><PERSON>"], "With your current role,": ["Z twoimi aktualnymi uprawnieniami,"], "With your current role, you can only use existing states.": ["Z twoimi aktualnymi uprawnieniami, moż<PERSON>z używać tylko istniejących stanów."], "Inheritance item: %s": ["Element dziedziczenia: %s"], "Inherited from base styles": ["Dziedziczone ze stylów bazowych"], "Style origin": ["Pochodzenie stylu"], "Base": ["Baza"], "you can only use existing classes.": ["możesz używać tylko istniejących klas."], "class %s applied": ["zastosowano klasę %s"], "With your current role, you can use existing classes but can’t modify them.": ["<PERSON><PERSON><PERSON> obecną rolę, mo<PERSON><PERSON><PERSON> korzy<PERSON> z istniejących klas, ale nie możesz ich modyfikować."], "Column gap": ["Odstęp kolumny"], "class %s removed": ["usunięto klasę %s"], "Text color": ["<PERSON><PERSON>"], "Word spacing": ["Odstępy między wyrazami"], "Text transform": ["Przekształcenie tekstu"], "Font style": ["<PERSON><PERSON> kroju pisma"], "Font family": ["<PERSON><PERSON><PERSON> kro<PERSON> pisma"], "Max width": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Min width": ["<PERSON><PERSON><PERSON>"], "Object position": ["Pozycja obiektu"], "Object fit": ["Dopasowanie obiektu"], "Scale down": ["<PERSON><PERSON><PERSON><PERSON>"], "Z-index": ["Z-index"], "Anchor offset": ["Przesunięcie kotwicy"], "Text align": ["Wyrównanie tekstu"], "Classes": ["<PERSON><PERSON><PERSON>"], "States": ["<PERSON><PERSON>"], "Underline": ["Podkreślenie"], "Align content": ["Wyrówna<PERSON>"], "Border type": ["<PERSON><PERSON>j <PERSON>mowan<PERSON>"], "Border width": ["<PERSON><PERSON><PERSON><PERSON><PERSON> obramowania"], "Border color": ["<PERSON><PERSON>"], "Has style": ["Posiada styl"], "Border radius": ["Promień obramowania"], "This value is overridden by another style": ["<PERSON> war<PERSON> jest zastępowana przez inny styl"], "This is the final value": ["To jest war<PERSON><PERSON><PERSON> o<PERSON>"], "Type class name": ["Nazwa klasy rod<PERSON>"], "You’ve reached the limit of 50 classes. Please remove an existing one to create a new class.": ["Osiągnąłeś limit 50 klas. Usuń istniejące klasy, aby utworzyć nowe."], "Bottom right": ["<PERSON><PERSON><PERSON> prawo"], "Streamline your workflow with dynamic tags": ["Usprawnij swój przepływ pracy dzięki dynamicznym znacznikom"], "Text stroke": ["<PERSON><PERSON><PERSON><PERSON>"], "Line decoration": ["Dekoracja liniowa"], "Overline": ["Nad k<PERSON>"], "Line-through": ["Przekreślenie"], "Inline-flex": ["Elastyczność w linii"], "In-flx": ["In-flx"], "Inline-block": ["Blok liniowy"], "In-blk": ["In-blk"], "Adjust corners": ["Dostosuj narożniki"], "Adjust borders": ["<PERSON><PERSON><PERSON><PERSON>"], "This has value from another style": ["To ma warto<PERSON> z innego stylu"], "Try something else.": ["Spróbuj czegoś innego."], "Clear & try again": ["Wyczyść i spróbuj ponownie"], "You'll need Elementor Pro to use this feature.": ["<PERSON>by <PERSON> z tej funkcji, potrzebujesz Elementora Pro."], "Top left": ["<PERSON><PERSON><PERSON>"], "Top right": ["Góra prawo"], "Bottom left": ["<PERSON><PERSON><PERSON>"], "Dynamic tags": ["Dynamiczne znaczniki"], "600 - Semi bold": ["600 - p<PERSON><PERSON> pog<PERSON>"], "500 - Medium": ["500 - średnia"], "400 - Normal": ["400 - normalna"], "300 - Light": ["300 - lekka"], "200 - Extra light": ["200 - bardzo lekka"], "700 - Bold": ["700 - p<PERSON><PERSON><PERSON><PERSON>"], "800 - Extra bold": ["800 - bard<PERSON> p<PERSON>"], "900 - Black": ["900 - czarna"], "100 - Thin": ["100 - cienka"], "Line height": ["<PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>"], "Letter spacing": ["Odstępy między literami"], "Open CSS Class Menu": ["Otwórz menu klasy CSS"], "Align items": ["Wyrównanie elementów"], "First": ["<PERSON><PERSON><PERSON>"], "Last": ["Ostatni"], "Custom order": ["<PERSON><PERSON><PERSON><PERSON>"], "Block": ["Blok"], "Flex": ["Elastyczny"], "Flex child": ["Elastyczny potomek"], "No wrap": ["Bez zawijania"], "Reversed wrap": ["Odwrócone zawijanie"], "Space evenly": ["Rozmieść równomiernie"], "Basis": ["Podstawa"], "Reversed column": ["Odwrócona kolumna"], "Reversed row": ["Odwrócony w<PERSON>z"], "Align self": ["Wyrównanie własne"], "local": ["lokalny"], "Space around": ["Odstęp wokół"], "Justify content": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Ridge": ["<PERSON><PERSON><PERSON><PERSON>"], "Effects": ["Efekty"], "Relative": ["Względny"], "Static": ["Statyczny"], "Visible": ["Widoczny"], "Outset": ["Zewnętrzny"], "Inset": ["Wewnętrzny"], "Box shadow": ["Cień pola"], "Font weight": ["G<PERSON><PERSON><PERSON><PERSON> kroju pisma"], "Show more": ["Pokaż więcej"], "Show less": ["Pokaż mniej"], "Font size": ["<PERSON><PERSON><PERSON><PERSON><PERSON> liter"], "Remove dynamic value": ["<PERSON><PERSON><PERSON>"], "Sorry, nothing matched": ["<PERSON><PERSON><PERSON><PERSON><PERSON>, nic nie pasuje"], "Search dynamic tags…": ["Wyszukaj dynamiczne znaczniki…"], "Min height": ["<PERSON>"], "Max height": ["<PERSON>"], "Dimensions": ["Wymiary"], "Space between": ["Odstęp między"], "Sticky": ["Przypię<PERSON>"], "Wrap": ["Zawijanie"], "Shrink": ["<PERSON><PERSON><PERSON><PERSON>"], "Groove": ["Wgłębione"], "Grow": ["Wzrost"], "Rename": ["Zmień nazwę"], "Order": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Row": ["<PERSON><PERSON><PERSON>"], "Gaps": ["Odstępy"], "Google Fonts": ["K<PERSON>je p<PERSON>a Google"], "Transform": ["Przekształcenie"], "Fill": ["Wypełnienie"], "Contain": ["Zawiera"], "Cover": ["<PERSON><PERSON><PERSON><PERSON>"], "Auto": ["Automatycznie"], "Hidden": ["<PERSON>k<PERSON><PERSON>"], "Overflow": ["Przepływ"], "Fixed": ["<PERSON><PERSON><PERSON><PERSON>"], "Absolute": ["Absolutnie"], "Opacity": ["<PERSON><PERSON><PERSON>"], "Position": ["<PERSON><PERSON><PERSON>ja"], "Custom Fonts": ["Własne kroje pisma"], "Remove": ["Usuń"], "End": ["Zakończenie"], "Start": ["Początek"], "Edit %s": ["Edytuj %s"], "Custom CSS": ["Własny CSS"], "Background": ["Tło"], "General": ["Ogólne"], "Direction": ["<PERSON><PERSON><PERSON><PERSON>"], "Spacing": ["Odstęp"], "Left to right": ["Od lewej do prawej"], "Display": ["Wyświetlacz"], "Settings": ["Ustawienia"], "Border": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Size": ["Rozmiar"], "Clear": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "System": ["System"], "None": ["Brak"], "Columns": ["<PERSON><PERSON><PERSON>"], "Style": ["<PERSON><PERSON>"], "Column": ["<PERSON><PERSON><PERSON>"], "Typography": ["Typografia"], "Margin": ["Mar<PERSON><PERSON>"], "Height": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Width": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Stretch": ["Roz<PERSON>ą<PERSON><PERSON>"], "Padding": ["Dopełnienie"], "Layout": ["<PERSON><PERSON><PERSON><PERSON>"], "Custom": ["<PERSON><PERSON><PERSON>ne"], "Center": ["Wyśrodkowanie"], "Justify": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Lowercase": ["Małe litery"], "Uppercase": ["Wielkie litery"], "Capitalize": ["<PERSON><PERSON><PERSON><PERSON>"], "Italic": ["Ku<PERSON>ywa"], "Left": ["<PERSON><PERSON>"], "Bottom": ["<PERSON><PERSON><PERSON>"], "Right to left": ["Od prawej do lewej"], "Right": ["Prawo"], "Top": ["Góra"], "Solid": ["Jednolita"], "Double": ["Podwójna"], "Dashed": ["Przerywana"], "Dotted": ["Kropkowana"], "Normal": ["Normalna"], "Aspect Ratio": ["Propor<PERSON><PERSON> o<PERSON>zu"]}}, "comment": {"reference": "assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js"}}