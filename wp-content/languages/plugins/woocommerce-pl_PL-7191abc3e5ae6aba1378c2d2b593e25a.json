{"translation-revision-date": "2024-11-14 22:41+0000", "generator": "Loco https://localise.biz/", "source": "assets/client/admin/chunks/analytics-report-categories.js", "domain": "woocommerce", "locale_data": {"woocommerce": {"": {"domain": "", "lang": "pl_PL", "plural-forms": "nplurals=3; plural=(n == 1) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);"}, "%d categories": ["%d kategorii"], "%d products": ["%d produkty"], "A sentence describing filters for Categories. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ\u0004Categories match <select/> filters": ["Kategorie spełniające <select/> warunki filtrów"], "Advanced filters": ["Zaawansowane filtry"], "All categories": ["Wszystkie kategorie"], "Categories": ["<PERSON><PERSON><PERSON>"], "categories\u0004+%d more": ["+%d więcej"], "Category": ["Kategoria", "<PERSON><PERSON><PERSON>", "Kategorii"], "Check at least two categories below to compare": ["Zaznacz co najmniej dwie kategorie poniżej, a<PERSON> p<PERSON><PERSON>"], "Check at least two products below to compare": ["Zaznacz co najmniej dwa produkty poniżej, aby p<PERSON><PERSON>"], "Compare": ["Porównaj"], "Compare Categories": ["Porównaj kategorie"], "Comparison": ["Porównanie"], "Indication of a low quantity\u0004Low": ["<PERSON><PERSON>"], "Item sold": ["Sprzedany produkt", "Sprzedane produkty", "Sprzedanych produktów"], "Items sold": ["Sprzedane produkty"], "N/A": ["<PERSON><PERSON> da<PERSON>"], "Net sales": ["<PERSON><PERSON><PERSON><PERSON><PERSON> netto"], "Order": ["Zamówienie", "Zamówienia", "Zamówień"], "Orders": ["Zamówienia"], "Product": ["Produkt", "Produkty", "Produktów"], "Product title": ["Tytuł produktu"], "Products": ["Produkty"], "Search by category name": ["Szukaj według nazwy kategorii"], "Search by product name or SKU": ["Szukaj po nazwie produktu lub SKU"], "Search for categories to compare": ["Szukaj kategorii do porównania"], "Show": ["Po<PERSON><PERSON>"], "Single Category": ["Pojedyncza kategoria"], "Single category": ["Pojedyncza kategoria"], "SKU": ["SKU"], "Status": ["Status"], "Stock": ["<PERSON>"], "Type to search for a category": ["<PERSON><PERSON><PERSON>, aby w<PERSON><PERSON> kategori<PERSON>"], "Variations": ["Warianty"]}}}