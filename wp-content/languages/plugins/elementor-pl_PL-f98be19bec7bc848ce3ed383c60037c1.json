{"translation-revision-date": "2025-09-17 12:40:44+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n == 1) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "pl"}, "To upload them anyway, ask the site administrator to enable unfiltered file uploads.": ["<PERSON><PERSON><PERSON> mimo wszystko chcesz je prz<PERSON>, pop<PERSON><PERSON> <PERSON>a witryny o włączenie przesyłania plików bez filtrowania."], "This is because JSON files may pose a security risk.": ["Dzieje się tak, ponieważ pliki JSON mogą stwarzać zagrożenie bezpieczeństwa."], "Sorry, you can't upload that file yet": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nie możesz jeszcze przesłać tego pliku"], "Warning: JSON files may be unsafe": ["Ostrzeżenie: Pliki JSON mogą być nieb<PERSON>ne"], "Uploading JSON files from unknown sources can be harmful and put your site at risk. For maximum safety, only install JSON files from trusted sources.": ["Przesyłanie plików JSON z nieznanych źródeł może być szkodliwe i narazić twoją witrynę na ryzyko. Aby zapewnić maksymalne bezpieczeństwo, instaluj pliki JSON wyłącznie ze źródeł zaufanych."], "Deactivate": ["Wyłącz"], "Are you sure?": ["<PERSON>zy na pewno?"], "Do not show this message again": ["<PERSON>e pokazuj ponownie tej wiadomości"], "Importing...": ["Importowanie..."], "Activate": ["Włącz"], "First, activate another experiment.": ["Wcześniej włącz inną funkcję eksperymentalną."], "In order to use %1$s, first you need to activate %2$s.": ["Aby używać %1$s musisz włączyć %2$s."], "Import Without Enabling": ["Importuj bez włączania"], "Enable and Import": ["Włącz i importuj"], "If you do not enable uploading unfiltered files, any SVG or JSON (including lottie) files used in the uploaded template will not be imported.": ["Je<PERSON><PERSON> nie włączysz przesyłania niefiltrowanych plików, żadne pliki SVG lub JSON (w tym lottie) użyte w przesłanym szablonie nie zostaną zaimportowane."], "Got it": ["<PERSON><PERSON><PERSON><PERSON>"], "Migrate to v3.0": ["Migracja do wersji 3.0"], "Please note that this process will revert all changes made to Global Colors and Fonts since upgrading to v3.x.": ["<PERSON><PERSON><PERSON><PERSON>, że proces ten spowoduje cofnięcie wszystkich zmian wprowadzonych w globalnych kolorach i krojach pisma od momentu uaktualnienia do wersji 3.x."], "Enable Unfiltered File Uploads": ["Włącz niefiltrowane przesyłanie plików"], "Font Awesome 5 Migration": ["Migracja do Font Awesome 5"], "I acknowledge that some changes may affect my website and that this action cannot be undone.": ["Przyjmuję do wiadomości, że niektóre zmiany mogą mieć wpływ na moją stronę i że tej akcji nie można cofnąć."], "I understand that by upgrading to Font Awesome 5,": ["Roz<PERSON>em to, uaktualniając do Font Awesome 5,"], "Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.": ["Przed włączeniem przesyłania niefiltrowanych plików należy pamiętać, że wiąże się to z zagrożeniem bezpieczeństwa. Elementor uruchamia proces usuwania możliwego złośliwego kodu ale nadal istnieje ryzyko związane z korzystaniem z takich plików."], "Please note that you are switching to WordPress default editor. Your current layout, design and content might break.": ["<PERSON><PERSON><PERSON><PERSON><PERSON>, że przełączasz się na domyślny edytor WordPress. Twój obecny układ, projekt i zawartość mogą się zepsuć."], "Continue": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Back to WordPress Editor": ["Powrót do edytora WordPress"], "Are you sure you want to reinstall previous version?": ["<PERSON>zy na pewno chcesz ponownie zainstalować poprzednią wersję?"], "Rollback to Previous Version": ["P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> poprzednią wersję"], "Enable": ["Włącz"], "Cancel": ["<PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/js/admin.js"}}