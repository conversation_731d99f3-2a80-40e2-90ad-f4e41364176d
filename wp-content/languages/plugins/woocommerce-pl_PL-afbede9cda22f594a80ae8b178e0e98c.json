{"translation-revision-date": "2024-11-14 22:41+0000", "generator": "Loco https://localise.biz/", "source": "packages/woocommerce-blocks/build/blocks-checkout.js", "domain": "woocommerce", "locale_data": {"woocommerce": {"": {"domain": "", "lang": "pl_PL", "plural-forms": "nplurals=3; plural=(n == 1) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);"}, "Dismiss this notice": ["<PERSON><PERSON><PERSON><PERSON> powiadomienie"], "Fee": ["<PERSON><PERSON><PERSON>"], "Please enter a valid %s": ["<PERSON><PERSON><PERSON> %s"], "Please fix the following errors before continuing": ["<PERSON><PERSON><PERSON> następujące błędy przed kontynuowaniem"], "Returned value must include %1$s, you passed \"%2$s\"": ["Zwrócona wartość musi zawierać %1$s, a podano \"%2$s\""], "Something went wrong. Please contact us to get assistance.": ["Coś poszło nie tak. By <PERSON><PERSON><PERSON><PERSON> wspar<PERSON>, skontaktuj się z nami."], "Subtotal": ["K<PERSON><PERSON>"], "Taxes": ["<PERSON><PERSON><PERSON><PERSON>"], "The type returned by checkout filters must be the same as the type they receive. The function received %1$s but returned %2$s.": ["Typ zwracany przez filtry zamówienia musi być taki sam, jak typ, k<PERSON><PERSON><PERSON> otr<PERSON>. Funkcja otrzymała %1$s, ale zwróciła %2$s."], "Unable to get cart data from the API.": ["Nie udało się pobrać danych koszyka z API."]}}}