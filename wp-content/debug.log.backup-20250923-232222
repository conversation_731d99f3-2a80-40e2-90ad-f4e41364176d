[23-Sep-2025 11:25:39 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:25:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:25:40 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:26:06 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:26:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:26:07 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:27:02 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:27:02 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:27:03 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:27:25 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:27:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:27:26 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:27:26 UTC] PHP Warning:  Undefined array key "hideYoutubeUI" in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-content/plugins/yt-player/inc/Services/Shortcode.php on line 80
[23-Sep-2025 11:30:12 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:30:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:30:13 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:30:14 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:30:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:30:14 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:30:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:30:15 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:31:21 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:31:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:31:22 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:31:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:31:23 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:31:24 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:33:07 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:33:07 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:33:09 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:33:09 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:33:09 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:33:09 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:33:09 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:33:10 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:33:30 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:33:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:33:31 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:33:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:33:32 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:33:32 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:33:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:33:34 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:33:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:33:35 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:33:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:33:36 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:33:36 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:33:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:35:12 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:35:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:35:13 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:36:46 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:36:46 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:36:47 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:36:47 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:36:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:36:47 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:36:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:36:48 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:38:20 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:38:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:38:21 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:38:22 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:38:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:38:23 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:38:33 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:38:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:38:34 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:38:35 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:38:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:38:35 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:38:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:38:36 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:38:36 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:38:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:38:36 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:38:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:38:37 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:38:37 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:38:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:38:40 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:38:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:38:40 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:38:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:38:41 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:38:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:38:41 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:38:42 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:38:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:38:42 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:39:30 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:39:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:39:31 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:40:13 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:40:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:40:14 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:40:14 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:40:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:40:14 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:40:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:40:15 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:41:46 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:41:46 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:41:47 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:42:02 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:42:02 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:42:04 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:42:04 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:42:04 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:42:04 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:42:04 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:42:05 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:42:39 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:42:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:42:40 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:43:46 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:43:46 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:43:47 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:44:39 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:44:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:44:41 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:44:41 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:44:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:44:41 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:44:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:44:42 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:45:13 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:45:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:45:14 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:45:47 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:45:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:45:49 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:47:48 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:47:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:47:50 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:47:50 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:47:50 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:47:50 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:47:50 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:47:51 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:48:06 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:48:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:48:07 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:48:08 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:48:08 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:48:09 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:48:31 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:48:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:48:32 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:49:49 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:49:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:49:51 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:50:14 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:50:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:50:15 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:50:15 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:50:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:50:15 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:50:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:50:16 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:50:31 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:50:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:50:32 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:51:50 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:51:50 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:51:52 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:52:38 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:52:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:52:39 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:52:40 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:52:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:52:40 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:52:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:52:41 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:53:28 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:53:28 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:53:29 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:53:51 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:53:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:53:52 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:54:13 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:54:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:54:14 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:54:14 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:54:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:54:15 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:54:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:54:15 UTC] PHP Warning:  Undefined array key "hideYoutubeUI" in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-content/plugins/yt-player/inc/Services/Shortcode.php on line 80
[23-Sep-2025 11:54:16 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:54:16 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:54:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:54:16 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:54:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:54:17 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:54:17 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:54:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:54:20 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:54:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:54:20 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:54:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:54:21 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:54:21 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:54:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:55:14 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:55:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:55:15 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:55:28 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:55:28 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:55:30 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:55:30 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:55:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:55:30 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:55:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:55:31 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:55:53 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:55:53 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:55:54 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:56:27 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:56:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:56:28 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:56:30 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:56:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:56:31 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:56:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:56:31 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:56:32 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:56:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:56:34 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:56:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:56:34 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:56:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:56:35 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:56:35 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:56:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:56:35 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:56:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:56:36 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:56:37 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:56:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:57:05 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:57:05 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:57:06 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:57:29 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:57:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:57:31 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:57:53 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:57:53 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:57:54 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:59:30 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:59:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:59:32 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:59:32 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:59:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:59:32 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:59:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:59:33 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 11:59:54 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 11:59:54 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 11:59:55 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:00:15 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:00:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:00:16 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:01:21 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:01:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:01:22 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:01:25 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:01:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:01:27 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:01:55 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:01:55 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:01:56 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:01:57 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:01:57 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:01:57 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:01:57 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:01:58 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:02:00 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:02:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:02:01 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:02:03 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:02:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:02:03 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:02:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:02:03 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:02:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:02:04 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:02:05 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:02:05 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:02:06 UTC] PHP Deprecated:  strlen(): Passing null to parameter #1 ($string) of type string is deprecated in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/formatting.php on line 3763
[23-Sep-2025 12:03:56 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:03:56 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:03:58 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:04:00 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:04:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:04:01 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:04:02 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:04:02 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:04:02 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:04:02 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:04:03 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:05:15 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:05:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:05:16 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:05:58 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:05:58 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:05:59 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:05:59 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:05:59 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:05:59 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:05:59 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:06:00 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:06:02 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:06:02 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:06:03 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:06:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:06:04 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:06:05 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:07:58 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:07:58 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:07:59 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:08:18 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:08:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:08:19 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:08:20 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:08:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:08:20 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:08:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:08:21 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:09:13 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:09:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:09:14 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:09:41 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:09:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:09:42 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:09:42 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:09:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:09:43 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:09:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:09:43 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:09:59 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:09:59 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:10:01 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:10:16 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:10:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:10:17 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:11:20 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:11:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:11:21 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:12:01 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:12:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:12:02 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:12:02 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:12:02 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:12:03 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:12:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:12:03 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:12:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:12:03 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:12:04 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:12:57 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:12:57 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:12:59 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:13:00 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:13:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:13:00 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:13:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:13:01 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:13:01 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:13:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:13:04 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:13:04 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:13:04 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:13:04 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:13:05 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:13:06 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:13:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:13:16 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:13:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:13:17 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:13:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:13:18 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:13:55 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:13:55 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:13:56 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:14:01 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:14:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:14:03 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:15:16 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:15:17 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:15:59 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:15:59 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:16:00 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:16:00 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:16:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:16:00 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:16:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:16:01 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:17:17 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:17:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:17:18 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:19:22 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:19:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:19:23 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:19:23 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:19:23 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:19:23 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:19:23 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:19:24 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:20:07 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:20:07 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:20:08 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:20:17 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:20:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:20:18 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:20:28 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:20:28 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:20:30 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:20:30 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:20:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:20:30 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:20:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:20:31 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:20:32 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:20:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:20:32 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:20:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:20:33 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:20:33 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:20:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:20:36 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:20:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:20:36 UTC] Translation loading error: Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:20:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>cron-logger</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
[23-Sep-2025 12:20:37 UTC] Aktywne problematyczne wtyczki: sfwd-lms/sfwd_lms.php, woocommerce/woocommerce.php
[23-Sep-2025 12:20:37 UTC] Translation loading error: Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.
[23-Sep-2025 12:20:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /home/<USER>/mediaplanet/firmup.eu/public_html/wp-includes/functions.php on line 6121
